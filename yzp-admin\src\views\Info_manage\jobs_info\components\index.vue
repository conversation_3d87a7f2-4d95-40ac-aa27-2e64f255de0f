<script lang="ts" setup>
import { ref, computed } from "vue";
import { getInfoById } from "@/api/info_query/index";
import { formatTimestamp } from "@/utils/dateFormat";
import { ElMessage } from "element-plus";

const loading = ref<boolean>(false);
const rightType = ref<any>("note");
const infoTableData = ref<any>({});

const $emit = defineEmits(["cancelBtn", "update:dialogVisible"]);

const $props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  currentRow: {
    type: Object,
    default: () => ({})
  }
});

const visible = computed({
  get() {
    if ($props.dialogVisible) {
      getInfoByIdData();
    }
    return $props.dialogVisible;
  },
  set(value) {
    $emit("update:dialogVisible", value);
  }
});

// 获取详情
const getInfoByIdData = async () => {
  const res: any = await getInfoById({ id: $props.currentRow.id, type: 6 });
  if (res.code === 0) {
    infoTableData.value = res.data;
  } else {
    ElMessage.error(res.message);
  }
};

// 处理薪资范围
const handleSalaryRange = (salaryBegin: any, salaryEnd: any): string => {
  const isEmpty = (val: any) => val === undefined || val === null || val === "";
  const isNegotiable = (val: any) => val === "面议";
  // 两个都是"面议"
  if (isNegotiable(salaryBegin) && isNegotiable(salaryEnd)) {
    return "面议";
  }
  // 两个都是0
  if (
    (salaryBegin === 0 || salaryBegin === "0") &&
    (salaryEnd === 0 || salaryEnd === "0")
  ) {
    return "面议";
  }
  // 两个都是空
  if (
    (isEmpty(salaryBegin) || isNegotiable(salaryBegin)) &&
    (isEmpty(salaryEnd) || isNegotiable(salaryEnd))
  ) {
    return "";
  }
  // 转换为K并向下取整
  const toK = (value: any): number | null => {
    const num = parseFloat(value);
    return isNaN(num) ? null : Math.floor(num / 1000);
  };
  const beginK = toK(salaryBegin);
  const endK = toK(salaryEnd);
  // 单边有效处理
  if (isEmpty(salaryBegin) || isNegotiable(salaryBegin) || beginK === null) {
    return endK !== null ? `${endK}k` : "";
  }
  if (isEmpty(salaryEnd) || isNegotiable(salaryEnd) || endK === null) {
    return beginK !== null ? `${beginK}k` : "";
  }
  // 双边都有效
  return `${beginK}k - ${endK}k`;
};

// 取消
function cancelBtn() {
  $emit("cancelBtn", true);
}

// 取消
async function handleClose() {
  cancelBtn();
}
</script>

<template>
  <div>
    <el-dialog
      v-model="visible"
      v-loading="loading"
      close-on-click-modal
      destroy-on-close
      :title="null"
      width="1100px"
      :show-close="true"
      class="custom-detail-dialog"
      align-center
      @close="cancelBtn"
    >
      <!-- 详情内容区 -->
      <el-descriptions title="岗位信息">
        <el-descriptions-item label="岗位名称："
          >{{ infoTableData?.positionName || "暂无" }}
        </el-descriptions-item>

        <el-descriptions-item
          v-if="infoTableData?.jobType"
          label="工作类型："
          >{{
            infoTableData?.jobType === 1
              ? "全职"
              : infoTableData?.jobType === 2
                ? "兼职"
                : "实习"
          }}</el-descriptions-item
        >
        <el-descriptions-item label="招聘者：">{{
          infoTableData?.createUserName || "暂无"
        }}</el-descriptions-item>

        <el-descriptions-item label="工作要求：">{{
          infoTableData?.positionKey || "暂无"
        }}</el-descriptions-item>

        <el-descriptions-item
          v-if="
            infoTableData?.workExperienceEnd &&
            infoTableData?.workExperienceStart
          "
          label="工作年限："
        >
          {{
            `${infoTableData?.workExperienceEnd - infoTableData?.workExperienceStart}年` ||
            ""
          }}
        </el-descriptions-item>

        <el-descriptions-item
          v-if="infoTableData?.workSalaryBegin || infoTableData?.workSalaryEnd"
          label="薪资范围："
        >
          {{
            handleSalaryRange(
              infoTableData?.workSalaryBegin,
              infoTableData?.workSalaryEnd
            )
          }}
        </el-descriptions-item>

        <el-descriptions-item
          v-if="infoTableData?.manualInspectionStatus"
          label="状态状态："
        >
          <el-tag
            size="small"
            :type="
              infoTableData?.manualInspectionStatus === 0
                ? 'info'
                : infoTableData?.manualInspectionStatus === 1
                  ? 'success'
                  : 'danger'
            "
            >{{
              infoTableData?.manualInspectionStatus === 0
                ? "待审核"
                : infoTableData?.manualInspectionStatus === 1
                  ? "通过"
                  : "驳回"
            }}</el-tag
          >
        </el-descriptions-item>
        <!-- <el-descriptions-item label="岗位信息：">{{
          infoTableData.positionDesc || ""
        }}</el-descriptions-item> -->
        <el-descriptions-item label="提交时间：">{{
          formatTimestamp(infoTableData?.createTime)
        }}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions class="job-desc" title="岗位职责：">
        <el-descriptions-item>{{
          infoTableData?.positionDesc || "暂无"
        }}</el-descriptions-item>
      </el-descriptions>
      <!-- 按钮组 -->
      <div v-if="rightType === 'note'" class="btn-group-bottom">
        <el-button @click="handleClose()">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.custom-detail-dialog .el-dialog__body {
  background: #f5f6fa;
  padding-bottom: 0;
  min-height: 520px;
}

.btn-group-bottom {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin: 20px 40px 0 0;
}
:deep(.el-table--fit) {
  margin-top: 10px;
  border-radius: 3px;
}
.job-desc {
  margin-bottom: 5px !important;
  margin-top: 10px !important;
}
</style>
