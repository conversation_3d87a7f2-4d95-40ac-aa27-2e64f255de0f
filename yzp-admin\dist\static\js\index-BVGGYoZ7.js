var H=(S,n,r)=>new Promise((m,p)=>{var _=u=>{try{h(r.next(u))}catch(v){p(v)}},x=u=>{try{h(r.throw(u))}catch(v){p(v)}},h=u=>u.done?m(u.value):Promise.resolve(u.value).then(_,x);h((r=r.apply(S,n)).next())});import oe from"./index-BVi1N1Jg.js";import{g as re}from"./index-Dgu8EOgW.js";import{d as se,r as l,o as ie,u as ue,a as de,c as w,b as i,e as d,w as o,f as U,g as c,F as P,h as I,i as Y,j as C,k as g,l as T,t as j,_ as pe}from"./index-DOMkE6w1.js";import{f as ce}from"./index-DFv13GLf.js";import"./quickReply-D6WzbhB8.js";const me={class:"table-header-flex"},ve={class:"form-btns"},ye={key:0},fe={key:1},be={style:{display:"flex","justify-content":"flex-end","margin-top":"20px"}},E=420,ge=se({__name:"index",setup(S){const n=l({}),r=l(!1),m=l(""),p=l(0),_=l(!1);let x=[{type:"input",key:"name",label:"法人"},{type:"input",key:"phone",label:"招聘者"},{type:"input",key:"phone",label:"公司名称"},{type:"datetime",key:"dates",label:"提交时间"}];const h=[{property:"name",label:"公司名称",width:""},{property:"enterpriseLegalPerson",label:"法人",width:""},{property:"name",label:"社会信用代码",width:""},{property:"createUserName",label:"招聘者",width:""},{property:"phone",label:"电话",width:""},{property:"createTime",label:"提交时间",width:""}],u=l([]),v=l(1),V=l(10),M=l("default"),$=l(!1),F=l(!1),L=l(0),z=l({}),A=ue(),s=l({entity:{createUserName:"",endTime:"",enterpriseLegalPerson:"",manualInspectionStatus:null,name:"",startTime:""},orderBy:{},page:1,size:10}),Q=t=>{V.value=t,s.value.size=t,s.value.page=1,y()},q=t=>{v.value=t,s.value.page=t,y()},N=l(window.innerHeight-E);function R(){N.value=window.innerHeight-E}function G(t){z.value=t.row,r.value=!0,m.value="note"}const J=t=>{z.value=t.row,r.value=!0,m.value="transfer"},K=t=>{z.value=t.row,r.value=!0,m.value="record"},O=()=>{m.value="",r.value=!1,y()},W=()=>{n.value.dates&&n.value.dates.length===2?(s.value.entity.startTime=n.value.dates[0],s.value.entity.endTime=n.value.dates[1]):(delete s.value.entity.startTime,delete s.value.entity.endTime),s.value.entity.name=n.value.name||void 0,y()},X=()=>{n.value={},s.value={entity:{createUserName:"",endTime:"",enterpriseLegalPerson:"",manualInspectionStatus:p.value,name:"",startTime:""},orderBy:{},page:1,size:10},y()},y=()=>H(null,null,function*(){_.value=!0;try{const t=yield re(s.value);t.code===0&&(L.value=t.data.total,u.value=t.data.list)}catch(t){}finally{_.value=!1}});return ie(()=>{const t=A.meta.businessStatus;s.value.entity.manualInspectionStatus=t!==void 0?Number(t):0,p.value=t!==void 0?Number(t):0,y(),window.addEventListener("resize",R)}),de(()=>{window.removeEventListener("resize",R)}),(t,a)=>{const Z=c("el-input"),ee=c("el-date-picker"),te=c("el-form-item"),ae=c("el-form"),k=c("el-button"),B=c("el-card"),D=c("el-table-column"),le=c("el-table"),ne=c("el-pagination");return i(),w("div",null,[d(B,{shadow:"never"},{default:o(()=>[U("div",me,[d(ae,{inline:!0,model:n.value,class:"table-header-form"},{default:o(()=>[(i(!0),w(P,null,I(Y(x),(e,f)=>(i(),g(te,{key:f,label:e.label,class:"form-item"},{default:o(()=>[e.type==="input"?(i(),g(Z,{key:0,modelValue:n.value[e.key],"onUpdate:modelValue":b=>n.value[e.key]=b,placeholder:"请输入"+e.label,clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="datetime"?(i(),g(ee,{key:1,modelValue:n.value[e.key],"onUpdate:modelValue":b=>n.value[e.key]=b,type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",style:{width:"380px"},"value-format":"YYYY-MM-DD HH:mm:ss",clearable:""},null,8,["modelValue","onUpdate:modelValue"])):T("",!0)]),_:2},1032,["label"]))),128))]),_:1},8,["model"]),U("div",ve,[d(k,{size:"large",type:"primary",onClick:W},{default:o(()=>a[3]||(a[3]=[C("搜索")])),_:1}),d(k,{size:"large",type:"info",style:{"background-color":"#b4c4d1",color:"#ffffff",border:"#b4c4d1"},onClick:X},{default:o(()=>a[4]||(a[4]=[C("重置")])),_:1})])])]),_:1}),d(B,{shadow:"never",style:{"margin-top":"15px",padding:"5px 15px"}},{default:o(()=>[d(le,{ref:"tableContainer",data:u.value,loading:_.value,style:{width:"100%"},border:"",height:N.value},{default:o(()=>[(i(),w(P,null,I(h,(e,f)=>d(D,{key:f,width:e.width,label:e.label,"show-overflow-tooltip":""},{default:o(b=>[e.property==="createTime"?(i(),w("span",ye,j(Y(ce)(b.row[e.property])),1)):(i(),w("span",fe,j(b.row[e.property]),1))]),_:2},1032,["width","label"])),64)),d(D,{fixed:"right",label:"操作","min-width":"120"},{default:o(e=>[p.value===0?(i(),g(k,{key:0,link:"",type:"primary",style:{color:"#279efb"},onClick:f=>G(e)},{default:o(()=>a[5]||(a[5]=[C(" 视检 ")])),_:2},1032,["onClick"])):T("",!0),p.value===0?(i(),g(k,{key:1,link:"",type:"primary",style:{color:"#fb2727"},onClick:f=>J(e)},{default:o(()=>a[6]||(a[6]=[C(" 转办 ")])),_:2},1032,["onClick"])):T("",!0),p.value===1||p.value===2?(i(),g(k,{key:2,link:"",type:"primary",style:{color:"#4eb906"},onClick:f=>K(e)},{default:o(()=>a[7]||(a[7]=[C(" 操作记录 ")])),_:2},1032,["onClick"])):T("",!0)]),_:1})]),_:1},8,["data","loading","height"]),U("div",be,[d(ne,{"current-page":v.value,"onUpdate:currentPage":a[0]||(a[0]=e=>v.value=e),"page-size":V.value,"onUpdate:pageSize":a[1]||(a[1]=e=>V.value=e),"page-sizes":[10,20,50,100],size:M.value,disabled:F.value,background:$.value,layout:"total, sizes, prev, pager, next, jumper",total:L.value,onSizeChange:Q,onCurrentChange:q},null,8,["current-page","page-size","size","disabled","background","total"])])]),_:1}),d(oe,{dialogVisible:r.value,"onUpdate:dialogVisible":a[2]||(a[2]=e=>r.value=e),isRightType:m.value,currentRow:z.value,onCancelBtn:O,"onUpdate:updataList":y},null,8,["dialogVisible","isRightType","currentRow"])])}}}),Te=pe(ge,[["__scopeId","data-v-ec5c67ed"]]);export{Te as default};
