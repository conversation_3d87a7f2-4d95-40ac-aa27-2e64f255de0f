export default {
  path: "/coupon_info",
  redirect: "/coupon_info/403",
  meta: {
    icon: "ri-user-fill",
    // showLink: false,
    title: "优惠卷管理",
    rank: 13
  },
  children: [
    {
      path: "/coupon_info/Configuration",
      meta: {
        title: "优惠卷"
      },
      children: [
        {
          path: "/coupon_info/Configuration/list",
          name: "couponInfo",
          component: () => import("@/views/Info_manage/user_info/index.vue"),
          meta: {
            title: "列表"
          }
        }
      ]
    }
    // {
    //   path: "/company/settlement",
    //   meta: {
    //     title: "企业入驻"
    //   },
    //   children: [
    //     {
    //       path: "/company/settlement/awaiting_review",
    //       name: "settlement_awaiting_review",
    //       component: () =>
    //         import(
    //           "@/views/company_Information/enterprise_settlement/index.vue"
    //         ),
    //       meta: {
    //         title: "待审核",
    //         businessStatus: 0
    //       }
    //     },
    //     {
    //       path: "/company/settlement/passed",
    //       name: "settlement_passed",
    //       component: () =>
    //         import(
    //           "@/views/company_Information/enterprise_settlement/index.vue"
    //         ),
    //       meta: {
    //         title: "已通过",
    //         businessStatus: 1
    //       }
    //     },
    //     {
    //       path: "/company/settlement/rejected",
    //       name: "settlement_rejected",
    //       component: () =>
    //         import(
    //           "@/views/company_Information/enterprise_settlement/index.vue"
    //         ),
    //       meta: {
    //         title: "已驳回",
    //         businessStatus: 2
    //       }
    //     }
    //   ]
    // }
  ]
} satisfies RouteConfigsTable;
