var x=(h,T,v)=>new Promise((m,d)=>{var w=a=>{try{u(v.next(a))}catch(g){d(g)}},f=a=>{try{u(v.throw(a))}catch(g){d(g)}},u=a=>a.done?m(a.value):Promise.resolve(a.value).then(w,f);u((v=v.apply(h,T)).next())});import{T as G,A as J,R as K,n as W}from"./quickReply-DfweD696.js";import{f as X}from"./dateFormat-BuOeynu9.js";import{i as N,j as Y}from"./index-BeicPvf_.js";import{d as Z,r as s,m as V,P as j,c as C,b as i,T as ee,ab as te,k as b,w as n,e as o,f as r,l as ae,g as _,j as k,t as O,i as E,aQ as H,_ as oe}from"./index-VeYmKv4z.js";const se={class:"content-row"},ie={class:"img-card-row"},le={class:"img-card"},ne={key:1,class:"img-placeholder"},re={class:"img-card"},de={key:1,class:"img-placeholder"},ue={class:"card-box"},ce={key:0,class:"btn-group-bottom"},me=Z({__name:"index",props:{dialogVisible:{type:Boolean,default:!1},isRightType:{type:String,default:""},currentRow:{type:Object,default:()=>({})},closeOnClickModal:{type:Boolean,default:!1}},emits:["cancelBtn","update:dialogVisible"],setup(h,{emit:T}){const v=s(!1),m=s("note"),d=s({}),w=s(""),f=s(""),u=s(""),a=s([]),g=s([]),R=T,c=h,A=V({get(){return c.dialogVisible&&c.currentRow.comAttachId&&c.currentRow.employmentCertificateId&&M(),c.dialogVisible},set(e){R("update:dialogVisible",e)}});j(()=>c.isRightType,e=>{m.value=e},{immediate:!0});const M=()=>x(null,null,function*(){const e=yield N({id:c.currentRow.comAttachId}),t=yield N({id:c.currentRow.employmentCertificateId});e.code===0&&(f.value=e.data),t.code===0&&(u.value=t.data)});function I(){R("cancelBtn",!0),m.value="note"}const B=e=>x(null,null,function*(){(yield Y(e)).code===0&&(H.success("操作成功"),I())});function P(e){const t={id:d.value.id,status:1};B(t)}function $(e){if(!y.value||y.value.trim()===""){H.warning("请填写驳回原因");return}const t={id:d.value.id,status:2,reason:y.value};B(t)}function z(e){I()}const Q=[{name:"A组张三",value:"zhangsan"},{name:"A组李四",value:"lisi"}],L=s("lisi"),y=s("");return j(()=>c.currentRow,e=>{a.value=[],g.value=[],a.value.push({auditUserName:(e==null?void 0:e.auditUserName)||"",auditTime:(e==null?void 0:e.auditTime)||"",status:(e==null?void 0:e.auditStatus)===1?"1":(e==null?void 0:e.auditStatus)===2?"2":"0",reason:(e==null?void 0:e.reason)||""}),d.value=e,w.value=e.headImgUrl},{immediate:!0}),(e,t)=>{const p=_("el-table-column"),S=_("el-table"),U=_("el-image"),D=_("el-button"),q=_("el-dialog"),F=te("loading");return i(),C("div",null,[ee((i(),b(q,{modelValue:A.value,"onUpdate:modelValue":t[4]||(t[4]=l=>A.value=l),"close-on-click-modal":h.closeOnClickModal,"destroy-on-close":"",title:null,width:"1100px","show-close":!0,class:"custom-detail-dialog","align-center":"",onClose:I},{default:n(()=>[o(S,{data:[d.value],border:"",style:{width:"100%","margin-bottom":"12px"},class:"info-table-normal"},{default:n(()=>[o(p,{prop:"name","show-overflow-tooltip":"",label:"企业名称","min-width":"180"}),o(p,{prop:"socialCreditCode","show-overflow-tooltip":"",label:"社会信用代码","min-width":"180"}),o(p,{prop:"enterpriseLegalPerson","show-overflow-tooltip":"",label:"法人","min-width":"120"})]),_:1},8,["data"]),o(S,{data:[d.value],border:"",style:{width:"100%"},class:"info-table-normal"},{default:n(()=>[o(p,{prop:"userId",label:"提交人ID","min-width":"100"},{default:n(l=>[k(O(`ID：${l.row.userId}`),1)]),_:1}),o(p,{prop:"hrCardName",label:"提交人姓名","min-width":"120"}),o(p,{prop:"phone",label:"提交人电话","min-width":"140"}),o(p,{prop:"createTime",label:"提交时间","min-width":"180"},{default:n(l=>[k(O(E(X)(l.row.createTime)),1)]),_:1})]),_:1},8,["data"]),r("div",se,[r("div",ie,[r("div",le,[t[6]||(t[6]=r("div",{class:"img-title"},"营业执照",-1)),f.value?(i(),b(U,{key:0,src:f.value,class:"img-preview",fit:"contain","preview-src-list":[f.value],"initial-index":0},{error:n(()=>t[5]||(t[5]=[r("div",{class:"img-placeholder"},null,-1)])),_:1},8,["src","preview-src-list"])):(i(),C("div",ne))]),r("div",re,[t[8]||(t[8]=r("div",{class:"img-title"},"在职证明",-1)),u.value?(i(),b(U,{key:0,src:u.value,class:"img-preview",fit:"contain","preview-src-list":[u.value],"initial-index":0},{error:n(()=>t[7]||(t[7]=[r("div",{class:"img-placeholder"},"加载失败",-1)])),_:1},8,["src","preview-src-list"])):(i(),C("div",de,"暂无图片"))])]),r("div",ue,[m.value==="transfer"?(i(),b(G,{key:0,modelValue:L.value,"onUpdate:modelValue":t[0]||(t[0]=l=>L.value=l),transferList:Q,onSubmit:z},null,8,["modelValue"])):m.value==="record"?(i(),b(J,{key:1,auditList:a.value,statusList:g.value},null,8,["auditList","statusList"])):(i(),b(K,{key:2,modelValue:y.value,"onUpdate:modelValue":t[1]||(t[1]=l=>y.value=l),options:E(W)},null,8,["modelValue","options"]))])]),m.value==="note"?(i(),C("div",ce,[o(D,{type:"danger",onClick:t[2]||(t[2]=()=>$("reject"))},{default:n(()=>t[9]||(t[9]=[k("驳回")])),_:1}),o(D,{type:"success",onClick:t[3]||(t[3]=()=>P("pass"))},{default:n(()=>t[10]||(t[10]=[k("通过")])),_:1})])):ae("",!0)]),_:1},8,["modelValue","close-on-click-modal"])),[[F,v.value]])])}}}),ye=oe(me,[["__scopeId","data-v-8c108010"]]);export{ye as default};
