<template>
  <div class="test-pdf-page">
    <h1>PDF加载测试页面</h1>
    <el-button @click="openDialog" type="primary">测试PDF加载</el-button>

    <!-- 模拟附件简历弹框 -->
    <el-dialog
      v-model="dialogVisible"
      title="附件简历测试"
      width="1100px"
      destroy-on-close
    >
      <div class="pdf-test-container">
        <div class="pdf-preview-wrapper">
          <!-- PDF加载状态 -->
          <div v-if="pdfLoading && fileUrl" class="pdf-loading">
            <div class="loading-spinner">
              <Icon icon="eos-icons:loading" class="loading-icon" />
            </div>
            <div class="loading-text">PDF正在加载中...</div>
            <div v-if="pdfLoadProgress > 0" class="loading-progress">
              <el-progress
                :percentage="pdfLoadProgress"
                :show-text="false"
                :stroke-width="4"
                color="#409eff"
              />
              <span class="progress-text">{{ pdfLoadProgress }}%</span>
            </div>
          </div>

          <!-- PDF错误状态 -->
          <div v-else-if="pdfLoadError" class="pdf-error">
            <Icon icon="material-symbols:warning" class="error-icon" />
            <div class="error-text">{{ pdfLoadError }}</div>
            <el-button
              type="primary"
              size="small"
              style="margin-top: 12px"
              @click="loadTestPdf"
            >
              重新加载
            </el-button>
          </div>

          <!-- PDF组件 -->
          <VuePdfEmbed
            v-else-if="fileUrl && !pdfLoading"
            :source="{
              url: fileUrl,
              httpHeaders: {
                'Cache-Control': 'max-age=3600'
              },
              withCredentials: false
            }"
            :page="1"
            text-layer
            annotation-layer
            class="pdf-preview"
            @loaded="handlePdfLoaded"
            @loading-failed="handlePdfLoadingFailed"
            @progress="handlePdfProgress"
            @rendered="handlePdfRendered"
            @rendering-failed="handlePdfRenderingFailed"
          />

          <!-- 无附件状态 -->
          <div v-else class="no-pdf">点击按钮加载测试PDF</div>
        </div>
      </div>

      <template #footer>
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button @click="loadTestPdf" type="primary">加载测试PDF</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import VuePdfEmbed from "vue-pdf-embed";
import { Icon } from "@iconify/vue";
import { ElMessage } from "element-plus";

const dialogVisible = ref(false);
const fileUrl = ref("");

// PDF加载状态管理
const pdfLoading = ref<boolean>(false);
const pdfLoadError = ref<string>("");
const pdfLoadProgress = ref<number>(0);

// 打开对话框
const openDialog = () => {
  dialogVisible.value = true;
  resetPdfState();
};

// 重置PDF状态
const resetPdfState = () => {
  pdfLoading.value = false;
  pdfLoadError.value = "";
  pdfLoadProgress.value = 0;
  fileUrl.value = "";
};

// 加载测试PDF
const loadTestPdf = () => {
  pdfLoading.value = true;
  pdfLoadError.value = "";
  pdfLoadProgress.value = 0;

  // 使用一个公开的测试PDF文件
  fileUrl.value =
    "https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf";

  console.log("开始加载测试PDF:", fileUrl.value);
};

// PDF组件事件处理
const handlePdfLoaded = () => {
  pdfLoading.value = false;
  pdfLoadProgress.value = 100;
  ElMessage.success("PDF加载完成");
  console.log("PDF加载完成");
};

const handlePdfLoadingFailed = (error: any) => {
  pdfLoading.value = false;
  pdfLoadError.value = error?.message || "PDF加载失败";
  ElMessage.error(pdfLoadError.value);
  console.error("PDF加载失败:", error);
};

const handlePdfProgress = (progressData: any) => {
  if (progressData && progressData.total > 0) {
    pdfLoadProgress.value = Math.round(
      (progressData.loaded / progressData.total) * 100
    );
    console.log("PDF加载进度:", pdfLoadProgress.value + "%");
  }
};

const handlePdfRendered = () => {
  console.log("PDF渲染完成");
};

const handlePdfRenderingFailed = (error: any) => {
  pdfLoadError.value = error?.message || "PDF渲染失败";
  ElMessage.error(pdfLoadError.value);
  console.error("PDF渲染失败:", error);
};
</script>

<style lang="scss" scoped>
.test-pdf-page {
  padding: 20px;
}

.pdf-test-container {
  .pdf-preview-wrapper {
    background: #f7f8fa;
    border-radius: 8px;
    width: 100%;
    height: 500px;
    overflow: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .pdf-preview {
    width: 100%;
    height: 500px;
    border: none;
    background: #f7f8fa;
  }

  .no-pdf {
    color: #888;
    font-size: 16px;
    text-align: center;
    width: 100%;
  }

  // PDF加载状态样式
  .pdf-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: #666;

    .loading-spinner {
      margin-bottom: 16px;

      .loading-icon {
        font-size: 32px;
        color: #409eff;
        animation: spin 1s linear infinite;
      }
    }

    .loading-text {
      font-size: 16px;
      margin-bottom: 16px;
      color: #666;
    }

    .loading-progress {
      width: 200px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;

      .progress-text {
        font-size: 14px;
        color: #409eff;
        font-weight: 500;
      }
    }
  }

  // PDF错误状态样式
  .pdf-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;

    .error-icon {
      font-size: 32px;
      color: #f56c6c;
      margin-bottom: 12px;
    }

    .error-text {
      font-size: 16px;
      color: #666;
      text-align: center;
      margin-bottom: 16px;
      max-width: 280px;
      line-height: 1.5;
    }
  }
}

// 旋转动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
