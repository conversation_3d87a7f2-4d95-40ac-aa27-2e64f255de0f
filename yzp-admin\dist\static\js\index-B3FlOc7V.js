var I=Object.defineProperty;var A=Object.getOwnPropertySymbols;var S=Object.prototype.hasOwnProperty,W=Object.prototype.propertyIsEnumerable;var B=(r,o,n)=>o in r?I(r,o,{enumerable:!0,configurable:!0,writable:!0,value:n}):r[o]=n,$=(r,o)=>{for(var n in o||(o={}))S.call(o,n)&&B(r,n,o[n]);if(A)for(var n of A(o))W.call(o,n)&&B(r,n,o[n]);return r};import{d as q,r as d,c as h,b as D,f as s,e,F as G,h as H,w as a,g as u,j as m,W as J,t as L,_ as K}from"./index-DOMkE6w1.js";const M={class:"assign-detail-page"},O={class:"assign-tabs"},P={class:"assign-filter"},Q={class:"assign-filter-actions"},R={class:"assign-content"},X={class:"assign-title-row"},Y={class:"assign-title-left"},Z={class:"add-user-filter-row"},ee=q({__name:"index",setup(r){const o=["头像","简历","附件","作品集","企业入驻","企业信息","地址","岗位"],n=d("头像"),V=d("未分配"),w=d(""),y=d([{name:"张三",status:"待审核",assignNum:""},{name:"张三",status:"待审核",assignNum:"99999"},{name:"张三",status:"待审核",assignNum:"99999"}]),c=d(!1),x=d("A组"),U=d(""),z=d([{name:"张三",account:"********",group:"A组"}]),F=d(null);function T(){c.value=!0,F.value=null}function j(C){y.value.push($({},C)),c.value=!1}return(C,l)=>{const p=u("el-button"),f=u("el-option"),k=u("el-select"),_=u("el-form-item"),g=u("el-form"),v=u("el-input"),i=u("el-table-column"),N=u("el-table"),E=u("el-dialog");return D(),h("div",M,[s("div",O,[(D(),h(G,null,H(o,t=>e(p,{key:t,type:n.value===t?"primary":"default",class:"assign-tab-btn",onClick:b=>n.value=t},{default:a(()=>[m(L(t),1)]),_:2},1032,["type","onClick"])),64))]),s("div",P,[e(g,{inline:!0,class:"assign-filter-form"},{default:a(()=>[e(_,{label:"状态",class:"assign-form-item"},{default:a(()=>[e(k,{modelValue:V.value,"onUpdate:modelValue":l[0]||(l[0]=t=>V.value=t),style:{width:"180px"}},{default:a(()=>[e(f,{label:"未分配",value:"未分配"}),e(f,{label:"已分配",value:"已分配"})]),_:1},8,["modelValue"])]),_:1}),s("div",Q,[e(p,{type:"primary"},{default:a(()=>l[5]||(l[5]=[m("搜索")])),_:1}),e(p,null,{default:a(()=>l[6]||(l[6]=[m("重置")])),_:1})])]),_:1})]),s("div",R,[s("div",X,[s("div",Y,[l[8]||(l[8]=s("span",{class:"assign-title"},"分配申批",-1)),e(g,{inline:!0,class:"assign-form-inline"},{default:a(()=>[e(_,{label:"审批人员"},{default:a(()=>[e(v,{modelValue:w.value,"onUpdate:modelValue":l[1]||(l[1]=t=>w.value=t),placeholder:"请输入审批人员名称",style:{width:"220px"}},null,8,["modelValue"])]),_:1}),e(_,null,{default:a(()=>[e(p,{type:"primary",icon:"el-icon-plus",onClick:T},{default:a(()=>l[7]||(l[7]=[m("添加人员")])),_:1})]),_:1})]),_:1})]),l[9]||(l[9]=s("span",{class:"assign-count"},[m(" 待审批："),s("span",{class:"assign-count-num"},"999")],-1))]),e(N,{data:y.value,class:"assign-table",border:""},{default:a(()=>[e(i,{label:"#",width:"50"},{default:a(t=>[s("span",{class:J("assign-index assign-index-"+(t.$index+1))},L(t.$index+1),3)]),_:1}),e(i,{prop:"name",label:"姓名",width:"80"}),e(i,{prop:"status",label:"状态",width:"100"}),e(i,{label:"明细"},{default:a(()=>l[10]||(l[10]=[s("span",{class:"assign-detail-item"},"简历--229",-1),s("span",{class:"assign-detail-item"},"附件--229",-1),s("span",{class:"assign-detail-item"},"企业入驻--229",-1),s("span",{class:"assign-detail-item"},"岗位发布--229",-1)])),_:1}),e(i,{label:"分配",width:"180"},{default:a(t=>[e(v,{modelValue:t.row.assignNum,"onUpdate:modelValue":b=>t.row.assignNum=b,placeholder:"输入分配数量"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(i,{width:"120"},{default:a(()=>[e(p,{type:"primary"},{default:a(()=>l[11]||(l[11]=[m("确认分配")])),_:1})]),_:1})]),_:1},8,["data"])]),e(E,{modelValue:c.value,"onUpdate:modelValue":l[4]||(l[4]=t=>c.value=t),title:"添加人员",width:"60%"},{default:a(()=>[s("div",Z,[e(g,{inline:!0},{default:a(()=>[e(_,{label:"部门分组"},{default:a(()=>[e(k,{modelValue:x.value,"onUpdate:modelValue":l[2]||(l[2]=t=>x.value=t),style:{width:"180px"}},{default:a(()=>[e(f,{label:"A组",value:"A组"}),e(f,{label:"B组",value:"B组"})]),_:1},8,["modelValue"])]),_:1}),e(_,null,{default:a(()=>[e(v,{modelValue:U.value,"onUpdate:modelValue":l[3]||(l[3]=t=>U.value=t),placeholder:"搜索姓名/账号",style:{width:"260px"}},null,8,["modelValue"])]),_:1})]),_:1})]),e(N,{data:z.value,style:{"margin-top":"16px","margin-bottom":"50px"}},{default:a(()=>[e(i,{prop:"name",label:"姓名"}),e(i,{prop:"account",label:"账号"}),e(i,{prop:"group",label:"分组"}),e(i,{label:"操作",width:"80"},{default:a(t=>[e(p,{type:"primary",size:"small",onClick:b=>j(t.row)},{default:a(()=>l[12]||(l[12]=[m("确认")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"])])}}}),te=K(ee,[["__scopeId","data-v-af27a3c4"]]);export{te as default};
