var C=(T,N,_)=>new Promise(($,e)=>{var I=c=>{try{v(_.next(c))}catch(w){e(w)}},g=c=>{try{v(_.throw(c))}catch(w){e(w)}},v=c=>c.done?$(c.value):Promise.resolve(c.value).then(I,g);v((_=_.apply(T,N)).next())});import{a as O}from"./index-CpjYoR0F.js";import{f as Q}from"./dateFormat-BuOeynu9.js";import{d as q,r as D,m as A,aQ as G,c as z,b as f,T as H,ab as J,k as S,w as n,e as d,l as x,g as V,j as u,t as p,i as L,_ as P}from"./index-VeYmKv4z.js";const W={key:0,class:"btn-group-bottom"},X=q({__name:"index",props:{dialogVisible:{type:Boolean,default:!1},currentRow:{type:Object,default:()=>({})}},emits:["cancelBtn","update:dialogVisible"],setup(T,{emit:N}){const _=D(!1),$=D("note"),e=D({}),I=N,g=T,v=A({get(){return g.dialogVisible&&c(),g.dialogVisible},set(l){I("update:dialogVisible",l)}}),c=()=>C(null,null,function*(){const l=yield O({id:g.currentRow.id,type:6});l.code===0?e.value=l.data:G.error(l.message)}),w=(l,o)=>{const a=r=>r==null||r==="",m=r=>r==="面议";if(m(l)&&m(o)||(l===0||l==="0")&&(o===0||o==="0"))return"面议";if((a(l)||m(l))&&(a(o)||m(o)))return"";const y=r=>{const s=parseFloat(r);return isNaN(s)?null:Math.floor(s/1e3)},b=y(l),k=y(o);return a(l)||m(l)||b===null?k!==null?`${k}k`:"":a(o)||m(o)||k===null?b!==null?`${b}k`:"":`${b}k - ${k}k`};function j(){I("cancelBtn",!0)}function F(){return C(this,null,function*(){j()})}return(l,o)=>{const a=V("el-descriptions-item"),m=V("el-tag"),y=V("el-descriptions"),b=V("el-button"),k=V("el-dialog"),r=J("loading");return f(),z("div",null,[H((f(),S(k,{modelValue:v.value,"onUpdate:modelValue":o[1]||(o[1]=s=>v.value=s),"close-on-click-modal":"","destroy-on-close":"",title:null,width:"1100px","show-close":!0,class:"custom-detail-dialog","align-center":"",onClose:j},{default:n(()=>[d(y,{title:"岗位信息"},{default:n(()=>{var s,h,B,E,K,R;return[d(a,{label:"岗位名称："},{default:n(()=>{var t;return[u(p(((t=e.value)==null?void 0:t.positionName)||"暂无"),1)]}),_:1}),(s=e.value)!=null&&s.jobType?(f(),S(a,{key:0,label:"工作类型："},{default:n(()=>{var t,i;return[u(p(((t=e.value)==null?void 0:t.jobType)===1?"全职":((i=e.value)==null?void 0:i.jobType)===2?"兼职":"实习"),1)]}),_:1})):x("",!0),d(a,{label:"招聘者："},{default:n(()=>{var t;return[u(p(((t=e.value)==null?void 0:t.createUserName)||"暂无"),1)]}),_:1}),d(a,{label:"工作要求："},{default:n(()=>{var t;return[u(p(((t=e.value)==null?void 0:t.positionKey)||"暂无"),1)]}),_:1}),(h=e.value)!=null&&h.workExperienceEnd&&((B=e.value)!=null&&B.workExperienceStart)?(f(),S(a,{key:1,label:"工作年限："},{default:n(()=>{var t,i;return[u(p(`${((t=e.value)==null?void 0:t.workExperienceEnd)-((i=e.value)==null?void 0:i.workExperienceStart)}年`||""),1)]}),_:1})):x("",!0),(E=e.value)!=null&&E.workSalaryBegin||(K=e.value)!=null&&K.workSalaryEnd?(f(),S(a,{key:2,label:"薪资范围："},{default:n(()=>{var t,i;return[u(p(w((t=e.value)==null?void 0:t.workSalaryBegin,(i=e.value)==null?void 0:i.workSalaryEnd)),1)]}),_:1})):x("",!0),(R=e.value)!=null&&R.manualInspectionStatus?(f(),S(a,{key:3,label:"状态状态："},{default:n(()=>{var t,i;return[d(m,{size:"small",type:((t=e.value)==null?void 0:t.manualInspectionStatus)===0?"info":((i=e.value)==null?void 0:i.manualInspectionStatus)===1?"success":"danger"},{default:n(()=>{var M,U;return[u(p(((M=e.value)==null?void 0:M.manualInspectionStatus)===0?"待审核":((U=e.value)==null?void 0:U.manualInspectionStatus)===1?"通过":"驳回"),1)]}),_:1},8,["type"])]}),_:1})):x("",!0),d(a,{label:"提交时间："},{default:n(()=>{var t;return[u(p(L(Q)((t=e.value)==null?void 0:t.createTime)),1)]}),_:1})]}),_:1}),d(y,{class:"job-desc",title:"岗位职责："},{default:n(()=>[d(a,null,{default:n(()=>{var s;return[u(p(((s=e.value)==null?void 0:s.positionDesc)||"暂无"),1)]}),_:1})]),_:1}),$.value==="note"?(f(),z("div",W,[d(b,{onClick:o[0]||(o[0]=s=>F())},{default:n(()=>o[2]||(o[2]=[u("取消")])),_:1})])):x("",!0)]),_:1},8,["modelValue"])),[[r,_.value]])])}}}),le=P(X,[["__scopeId","data-v-0476dbe9"]]);export{le as default};
