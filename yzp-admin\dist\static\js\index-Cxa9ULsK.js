var P=(N,n,s)=>new Promise((m,c)=>{var g=d=>{try{_(s.next(d))}catch(y){c(y)}},v=d=>{try{_(s.throw(d))}catch(y){c(y)}},_=d=>d.done?m(d.value):Promise.resolve(d.value).then(g,v);_((s=s.apply(N,n)).next())});import oe from"./index-D6E-FpM7.js";import{g as re}from"./index-BeicPvf_.js";import{d as se,r as a,o as ie,u as ue,a as de,c as k,b as i,e as u,w as r,f as U,g as p,F as M,h as I,i as Y,j as z,k as w,l as S,t as j,_ as pe}from"./index-VeYmKv4z.js";import{f as ce}from"./dateFormat-BuOeynu9.js";import"./quickReply-DfweD696.js";const me={class:"table-header-flex"},ve={class:"form-btns"},ye={key:0},fe={key:1},be={style:{display:"flex","justify-content":"flex-end","margin-top":"20px"}},E=420,ge=se({__name:"index",setup(N){const n=a({}),s=a(!1),m=a(""),c=a(0),g=a(!1),v=a(!1);let _=[{type:"input",key:"enterpriseLegalPerson",label:"法人"},{type:"input",key:"createUserName",label:"招聘者"},{type:"input",key:"name",label:"公司名称"},{type:"datetime",key:"dates",label:"提交时间"}];const d=[{property:"name",label:"公司名称",width:""},{property:"enterpriseLegalPerson",label:"法人",width:""},{property:"name",label:"社会信用代码",width:""},{property:"createUserName",label:"招聘者",width:""},{property:"phone",label:"电话",width:""},{property:"createTime",label:"提交时间",width:""}],y=a([]),x=a(1),T=a(10),O=a("default"),F=a(!1),$=a(!1),L=a(0),V=a({}),A=ue(),o=a({entity:{createUserName:"",endTime:"",enterpriseLegalPerson:"",manualInspectionStatus:null,name:"",startTime:""},orderBy:{},page:1,size:10}),Q=t=>{T.value=t,o.value.size=t,o.value.page=1,f()},q=t=>{x.value=t,o.value.page=t,f()},R=a(window.innerHeight-E);function B(){R.value=window.innerHeight-E}function G(t){V.value=t.row,s.value=!0,v.value=!1,m.value="note"}const J=t=>{V.value=t.row,s.value=!0,v.value=!0,m.value="record"},K=()=>{m.value="",s.value=!1,v.value=!1,f()},W=()=>{n.value.dates&&n.value.dates.length===2?(o.value.entity.startTime=n.value.dates[0],o.value.entity.endTime=n.value.dates[1]):(delete o.value.entity.startTime,delete o.value.entity.endTime),o.value.entity.name=n.value.name||void 0,f()},X=()=>{n.value={},o.value={entity:{createUserName:"",endTime:"",enterpriseLegalPerson:"",manualInspectionStatus:c.value,name:"",startTime:""},orderBy:{},page:1,size:10},f()},f=()=>P(null,null,function*(){g.value=!0;try{const t=yield re(o.value);t.code===0&&(L.value=t.data.total,y.value=t.data.list)}catch(t){}finally{g.value=!1}});return ie(()=>{const t=A.meta.businessStatus;o.value.entity.manualInspectionStatus=t!==void 0?Number(t):0,c.value=t!==void 0?Number(t):0,f(),window.addEventListener("resize",B)}),de(()=>{window.removeEventListener("resize",B)}),(t,l)=>{const Z=p("el-input"),ee=p("el-date-picker"),te=p("el-form-item"),ae=p("el-form"),C=p("el-button"),D=p("el-card"),H=p("el-table-column"),le=p("el-table"),ne=p("el-pagination");return i(),k("div",null,[u(D,{shadow:"never"},{default:r(()=>[U("div",me,[u(ae,{inline:!0,model:n.value,class:"table-header-form"},{default:r(()=>[(i(!0),k(M,null,I(Y(_),(e,h)=>(i(),w(te,{key:h,label:e.label,class:"form-item"},{default:r(()=>[e.type==="input"?(i(),w(Z,{key:0,modelValue:n.value[e.key],"onUpdate:modelValue":b=>n.value[e.key]=b,placeholder:"请输入"+e.label,clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="datetime"?(i(),w(ee,{key:1,modelValue:n.value[e.key],"onUpdate:modelValue":b=>n.value[e.key]=b,type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",style:{width:"380px"},"value-format":"YYYY-MM-DD HH:mm:ss",clearable:""},null,8,["modelValue","onUpdate:modelValue"])):S("",!0)]),_:2},1032,["label"]))),128))]),_:1},8,["model"]),U("div",ve,[u(C,{size:"large",type:"primary",onClick:W},{default:r(()=>l[3]||(l[3]=[z("搜索")])),_:1}),u(C,{size:"large",type:"info",style:{"background-color":"#b4c4d1",color:"#ffffff",border:"#b4c4d1"},onClick:X},{default:r(()=>l[4]||(l[4]=[z("重置")])),_:1})])])]),_:1}),u(D,{shadow:"never",style:{"margin-top":"15px",padding:"5px 15px"}},{default:r(()=>[u(le,{ref:"tableContainer",data:y.value,loading:g.value,style:{width:"100%"},border:"",height:R.value},{default:r(()=>[(i(),k(M,null,I(d,(e,h)=>u(H,{key:h,width:e.width,label:e.label,"show-overflow-tooltip":""},{default:r(b=>[e.property==="createTime"?(i(),k("span",ye,j(Y(ce)(b.row[e.property])),1)):(i(),k("span",fe,j(b.row[e.property]),1))]),_:2},1032,["width","label"])),64)),u(H,{fixed:"right",label:"操作","min-width":"120"},{default:r(e=>[c.value===0?(i(),w(C,{key:0,link:"",type:"primary",style:{color:"#279efb"},onClick:h=>G(e)},{default:r(()=>l[5]||(l[5]=[z(" 视检 ")])),_:2},1032,["onClick"])):S("",!0),c.value===1||c.value===2?(i(),w(C,{key:1,link:"",type:"primary",style:{color:"#4eb906"},onClick:h=>J(e)},{default:r(()=>l[6]||(l[6]=[z(" 操作记录 ")])),_:2},1032,["onClick"])):S("",!0)]),_:1})]),_:1},8,["data","loading","height"]),U("div",be,[u(ne,{"current-page":x.value,"onUpdate:currentPage":l[0]||(l[0]=e=>x.value=e),"page-size":T.value,"onUpdate:pageSize":l[1]||(l[1]=e=>T.value=e),"page-sizes":[10,20,50,100],size:O.value,disabled:$.value,background:F.value,layout:"total, sizes, prev, pager, next, jumper",total:L.value,onSizeChange:Q,onCurrentChange:q},null,8,["current-page","page-size","size","disabled","background","total"])])]),_:1}),u(oe,{dialogVisible:s.value,"onUpdate:dialogVisible":l[2]||(l[2]=e=>s.value=e),isRightType:m.value,currentRow:V.value,closeOnClickModal:v.value,onCancelBtn:K},null,8,["dialogVisible","isRightType","currentRow","closeOnClickModal"])])}}}),xe=pe(ge,[["__scopeId","data-v-c941fc11"]]);export{xe as default};
