import { http } from "@/utils/http";

// 获取公司地址
const getCompanyAddress = (data: any) => {
  return http.request(
    "post",
    "/easyzhipin-admin/companyWorkAddress/queryList",
    {
      data
    }
  );
};

// 根据ID获取公司图片
const getCompanyImageById = (data: any) => {
  return http.request(
    "post",
    "/easyzhipin-admin/companyWorkAddress/queryById",
    {
      data
    }
  );
};

// 公司地址视检
const handleCompanyAddressAudit = (data: any) => {
  return http.request("post", "/easyzhipin-admin/companyWorkAddress/audit", {
    data
  });
};

// 企业入驻信息列表
const getCompanyHrList = (data: any) => {
  return http.request("post", "/easyzhipin-admin/companyHr/queryList", {
    data
  });
};

// 根据ID获取企业照片
const getCompanyHrImageById = (data: any) => {
  return http.request("post", "/easyzhipin-admin/companyHr/queryId", {
    data
  });
};

// 企业入驻审核接口
const handleCompanyHrAudit = (data: any) => {
  return http.request("post", "/easyzhipin-admin/companyHr/audit", {
    data
  });
};

// 企业信息
const getCompanyList = (data: any) => {
  return http.request("post", "/easyzhipin-admin/company/queryList", {
    data
  });
};

//通过ID获取企业信息
const getCompanyInfoById = (data: any) => {
  return http.request("post", "/easyzhipin-admin/company/queryById", {
    data
  });
};

// 企业信息审核
const handleCompanyAudit = (data: any) => {
  return http.request("post", "/easyzhipin-admin/company/audit", {
    data
  });
};

// 岗位信息列表
const getCompanyJobList = (data: any) => {
  return http.request("post", "/easyzhipin-admin/positionList/queryList", {
    data
  });
};

// 岗位信息审核
const handleCompanyJobAudit = (data: any) => {
  return http.request("post", "/easyzhipin-admin/positionList/audit", {
    data
  });
};

// 根据ID获取岗位信息
const getCompanyJobInfoById = (data: any) => {
  return http.request("post", "/easyzhipin-admin/positionList/queryById", {
    data
  });
};

export {
  getCompanyAddress,
  getCompanyImageById,
  handleCompanyAddressAudit,
  getCompanyHrList,
  getCompanyHrImageById,
  handleCompanyHrAudit,
  getCompanyList,
  getCompanyInfoById,
  handleCompanyAudit,
  getCompanyJobList,
  getCompanyJobInfoById,
  handleCompanyJobAudit
};
