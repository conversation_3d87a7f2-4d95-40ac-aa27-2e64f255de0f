export default {
  path: "/company",
  redirect: "/company/403",
  meta: {
    icon: "ri-building-2-fill",
    // showLink: false,
    title: "视检管理",
    rank: 10
  },
  children: [
    {
      path: "/user/certificate",
      // name: "/user/certificate",
      // component: () => import("@/views/user_approval/index.vue"),
      meta: {
        title: "证书"
      },
      children: [
        {
          path: "/user/certificate/awaiting_review",
          name: "certificate_awaiting_review",
          component: () => import("@/views/user_info/user_approval/index.vue"),
          meta: {
            title: "待审核",
            businessStatus: 1
          }
        },
        {
          path: "/user/certificate/passed",
          name: "certificate_passed",
          component: () => import("@/views/user_info/user_approval/index.vue"),
          meta: {
            title: "已通过",
            businessStatus: 3
          }
        },
        {
          path: "/user/certificate/rejected",
          name: "certificate_rejected",
          component: () => import("@/views/user_info/user_approval/index.vue"),
          meta: {
            title: "已驳回",
            businessStatus: 2
          }
        }
      ]
    },
    {
      path: "/user/appendix",
      // name: "/user/407",
      // component: () => import("@/views/user_approval/index.vue"),
      meta: {
        title: "附件简历"
      },
      children: [
        {
          path: "/user/appendix/awaiting_review",
          name: "appendix_awaiting_review",
          component: () => import("@/views/user_info/user_appendix/index.vue"),
          meta: {
            title: "待审核",
            businessStatus: 0
          }
        },
        {
          path: "/user/appendix/passed",
          name: "appendix_passed",
          component: () => import("@/views/user_info/user_appendix/index.vue"),
          meta: {
            title: "已通过",
            businessStatus: 1
          }
        },
        {
          path: "/user/appendix/rejected",
          name: "appendix_rejected",
          component: () => import("@/views/user_info/user_appendix/index.vue"),
          meta: {
            title: "已驳回",
            businessStatus: 2
          }
        }
      ]
    },
    {
      path: "/user/portfolio",
      meta: {
        title: "作品集"
      },
      children: [
        {
          path: "/user/portfolio/awaiting_review",
          name: "portfolio_awaiting_review",
          component: () => import("@/views/user_info/user_portfolio/index.vue"),
          meta: {
            title: "待审核",
            businessStatus: 1
          }
        },
        {
          path: "/user/portfolio/passed",
          name: "portfolio_passed",
          component: () => import("@/views/user_info/user_portfolio/index.vue"),
          meta: {
            title: "已通过",
            businessStatus: 3
          }
        },
        {
          path: "/user/portfolio/rejected",
          name: "portfolio_rejected",
          component: () => import("@/views/user_info/user_portfolio/index.vue"),
          meta: {
            title: "已驳回",
            businessStatus: 2
          }
        }
      ]
    },
    {
      path: "/company/information",
      meta: {
        title: "企业信息"
      },
      children: [
        {
          path: "/company/information/awaiting_review",
          name: "information_awaiting_review",
          component: () =>
            import("@/views/company_Information/company_info/index.vue"),
          meta: {
            title: "待审核",
            businessStatus: 0
          }
        },
        {
          path: "/company/information/passed",
          name: "information_passed",
          component: () =>
            import("@/views/company_Information/company_info/index.vue"),
          meta: {
            title: "已通过",
            businessStatus: 1
          }
        },
        {
          path: "/company/information/rejected",
          name: "information_rejected",
          component: () =>
            import("@/views/company_Information/company_info/index.vue"),
          meta: {
            title: "已驳回",
            businessStatus: 2
          }
        }
      ]
    },
    {
      path: "/company/postInfo",
      meta: {
        title: "职位发布"
      },
      children: [
        {
          path: "/company/postInfo/awaiting_review",
          name: "postInfo_awaiting_review",
          component: () =>
            import("@/views/company_Information/job_certification/index.vue"),
          meta: {
            title: "待审核",
            businessStatus: 0
          }
        },
        {
          path: "/company/postInfo/passed",
          name: "postInfo_passed",
          component: () =>
            import("@/views/company_Information/job_certification/index.vue"),
          meta: {
            title: "已通过",
            businessStatus: 1
          }
        },
        {
          path: "/company/postInfo/rejected",
          name: "postInfo_rejected",
          component: () =>
            import("@/views/company_Information/job_certification/index.vue"),
          meta: {
            title: "已驳回",
            businessStatus: 2
          }
        }
      ]
    }
  ]
} satisfies RouteConfigsTable;
