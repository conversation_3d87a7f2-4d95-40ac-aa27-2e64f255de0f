import{d as k,r as V,P as w,c as f,b as r,f as p,e as a,g as m,w as i,j as S,t as x,_ as L,l as v,k as z,i as C,V as X,F as A,h as T}from"./index-VeYmKv4z.js";import{f as $}from"./dateFormat-BuOeynu9.js";const j={class:"transfer-select-box"},N={class:"scroll-container"},R={class:"transfer-btn-bar"},U=k({__name:"index",props:{transferList:{},modelValue:{}},emits:["update:modelValue","submit"],setup(h,{emit:n}){const t=h,l=n,o=V(t.modelValue);w(()=>t.modelValue,_=>o.value=_),w(()=>o.value,_=>l("update:modelValue",_));function e(){l("submit",o.value)}return(_,d)=>{const s=m("el-radio"),u=m("el-table-column"),b=m("el-table"),y=m("el-button");return r(),f("div",j,[d[2]||(d[2]=p("div",{class:"record-title"},"转审人员",-1)),p("div",N,[a(b,{data:_.transferList,border:"",class:"transfer-table","show-header":!0,"header-cell-style":{background:"#f7f8fa",color:"#888",fontWeight:500,fontSize:"15px",textAlign:"left"},"cell-style":{background:"#f7f8fa",color:"#222",fontSize:"15px",textAlign:"left"}},{default:i(()=>[a(u,{prop:"name",label:"名字","min-width":"180"},{default:i(g=>[a(s,{modelValue:o.value,"onUpdate:modelValue":d[0]||(d[0]=c=>o.value=c),label:g.row.value,style:{"margin-left":"8px"}},{default:i(()=>[S(x(g.row.name),1)]),_:2},1032,["modelValue","label"])]),_:1})]),_:1},8,["data"]),p("div",R,[a(y,{type:"primary",style:{width:"100px"},onClick:e},{default:i(()=>d[1]||(d[1]=[S("转审")])),_:1})])])])}}}),J=L(U,[["__scopeId","data-v-f6efb835"]]),B={class:"audit-status-records-box"},F={class:"scroll-container"},I={key:0},W={key:0},E={key:1},q={key:2},D=k({__name:"index",props:{auditList:{},statusList:{}},setup(h){return(n,t)=>{const l=m("el-table-column"),o=m("el-table");return r(),f("div",B,[p("div",F,[n.auditList.length>0?(r(),f("div",I,[t[0]||(t[0]=p("div",{class:"record-title"},"审核记录",-1)),a(o,{data:n.auditList,border:"",class:"audit-table","show-header":!0,"header-cell-style":{background:"#f7f8fa",color:"#888",fontWeight:500,fontSize:"15px",textAlign:"center"},"cell-style":{background:"#f7f8fa",color:"#222",fontSize:"15px",textAlign:"center"},style:{width:"100%","margin-bottom":"24px"}},{default:i(()=>[a(l,{prop:"auditUserName",label:"人员","min-width":"80"}),a(l,{prop:"auditTime",label:"审核时间","min-width":"120","show-overflow-tooltip":""},{default:i(e=>[p("span",null,x(C($)(e.row.auditTime)),1)]),_:1}),n.auditList.some(e=>e.status==="2")?(r(),z(l,{key:0,prop:"reason",label:"驳回原因","min-width":"120","show-overflow-tooltip":""},{default:i(e=>[e.row.status==="2"?(r(),f("span",W,x(e.row.reason),1)):v("",!0)]),_:1})):v("",!0),a(l,{prop:"status",label:"结果","min-width":"80"},{default:i(e=>[p("span",{style:X({color:e.row.status==="1"?"#1FC600":e.row.status==="2"?"#F56C6C":"#222"})},x(e.row.status==="1"?"通过":e.row.status==="2"?"已驳回":"待审核"),5)]),_:1})]),_:1},8,["data"])])):v("",!0),n.statusList.length>0?(r(),f("div",E,[t[1]||(t[1]=p("div",{class:"record-title"},"状态记录",-1)),a(o,{data:n.statusList,border:"",class:"status-table","show-header":!0,"header-cell-style":{background:"#f7f8fa",color:"#888",fontWeight:500,fontSize:"15px",textAlign:"center"},"cell-style":{background:"#f7f8fa",color:"#222",fontSize:"15px",textAlign:"center"},style:{width:"100%"}},{default:i(()=>[a(l,{prop:"person",label:"人员","min-width":"80"}),a(l,{prop:"time",label:"转审时间","min-width":"160"}),a(l,{prop:"person2",label:"人员","min-width":"80"})]),_:1},8,["data"])])):v("",!0),n.statusList.length===0&&n.auditList.length===0?(r(),f("div",q,t[2]||(t[2]=[p("div",{class:"no-data"},"暂无数据",-1)]))):v("",!0)])])}}}),K=L(D,[["__scopeId","data-v-7b350a81"]]),O={class:"reason-edit-card"},P=k({__name:"index",props:{options:{},modelValue:{}},emits:["update:modelValue","change"],setup(h,{emit:n}){var _,d;const t=h,l=n,o=V(((_=t.options[0])==null?void 0:_.value)||""),e=V(((d=t.options[0])==null?void 0:d.content)||"");return w(o,s=>{const u=t.options.find(b=>b.value===s);e.value=u?u.content:"",l("change",s),l("update:modelValue",e.value)}),w(()=>t.modelValue,s=>{s!==void 0&&(e.value=s)}),w(e,s=>{l("update:modelValue",s)}),(s,u)=>{const b=m("el-option"),y=m("el-select"),g=m("el-input");return r(),f("div",O,[a(y,{modelValue:o.value,"onUpdate:modelValue":u[0]||(u[0]=c=>o.value=c),class:"reason-select",style:{width:"220px"}},{default:i(()=>[(r(!0),f(A,null,T(s.options,c=>(r(),z(b,{key:c.value,label:c.label,value:c.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),a(g,{modelValue:e.value,"onUpdate:modelValue":u[1]||(u[1]=c=>e.value=c),placeholder:"请填写批注内容",type:"textarea",rows:7,class:"reason-textarea",resize:"none",autosize:{minRows:7,maxRows:12}},null,8,["modelValue"])])}}}),M=L(P,[["__scopeId","data-v-6d9ae132"]]),Q=[{label:"自定义批注",value:"none",content:""},{label:"实名认证未通过",value:"id_invalid",content:"您的实名认证未通过，请核对身份证信息是否清晰完整，并重新上传。如有疑问，可联系客服。"},{label:"营业执照不清晰或缺角",value:"license_blur",content:"您上传的营业执照不清晰/缺角，请重新拍摄完整原件（需四角齐全、文字可见），我们将在2小时内优先处理。"},{label:"营业执照已过期",value:"license_expired",content:"您上传的企业营业执照有效期已过期，请更新后重新提交审核。"},{label:"企业名称不一致",value:"company_name_mismatch",content:"您登记的企业名称与工商系统不一致，请修改为全称。"},{label:"职位含敏感词",value:"job_sensitive",content:"您发布的职位描述含敏感词‘XXX’，请1小时内修改后重新提交。"},{label:"岗位涉嫌歧视",value:"job_discrimination",content:"您发布的该岗位疑似存在性别/年龄歧视，请参考《劳动法》1小时内修改内容信息。"},{label:"岗位涉嫌违规",value:"job_illegal",content:"尊敬的【用户姓名】，您发布的岗位涉嫌违法违规，违反平台规则。请1小时内修改岗位内容并及时提交，超出时间会下架该岗位。"},{label:"职位已到期下线",value:"job_expired",content:"您的【】职位已到期（30天未刷新），系统自动下线，如需继续招聘请重新发布。"},{label:"投诉已受理",value:"complaint_received",content:"尊敬的【用户姓名】，您对【企业名称/岗位名称】的投诉已收到，我们将在【48小时】内核实并处理。感谢您帮助我们维护平台秩序！"},{label:"举报需补充证据",value:"report_evidence",content:"举报需补充截图证据，请在24小时内通过‘聊天页面-举报中心’提交。"},{label:"频繁举报提醒",value:"report_frequent",content:"系统检测到您频繁举报，请确保信息真实，避免滥用功能。"},{label:"建议法律途径解决",value:"offline_issue_law",content:"您与企业产生的线下问题，建议通过法律途径解决，感谢您的反馈。"},{label:"建议劳动仲裁",value:"dispute_labor",content:"您与企业【XXX】的纠纷建议通过劳动仲裁解决，感谢您的反馈。"},{label:"反馈已记录",value:"feedback_recorded",content:"感谢您的反馈！我们已记录并会核查该职位信息，后续将加强企业审核，避免误导求职者。"},{label:"反馈解答已发送",value:"feedback_answered",content:"您好！关于您反馈【XXX】的解答已发送至您的系统通知，感谢您的反馈。"},{label:"企业资质审核通过",value:"company_verified",content:"您好！您提交的企业资质审核已顺利通过！认证首周岗位曝光量翻倍，快去发布职位吧。祝您早日找到理想人才！"},{label:"个人信息审核通过",value:"personal_verified",content:"您好！您提交的个人信息审核已顺利通过！优质岗位正在飞奔而来，愿您早日遇见心仪offer！"}];export{K as A,M as R,J as T,Q as n};
