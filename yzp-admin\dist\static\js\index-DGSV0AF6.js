var j=(I,a,c)=>new Promise((w,v)=>{var k=i=>{try{b(c.next(i))}catch(y){v(y)}},T=i=>{try{b(c.throw(i))}catch(y){v(y)}},b=i=>i.done?w(i.value):Promise.resolve(i.value).then(k,T);b((c=c.apply(I,a)).next())});import le from"./index-BokCUB8Y.js";import{g as oe}from"./index-CpjYoR0F.js";import{f as ne}from"./dateFormat-BuOeynu9.js";import{d as re,r as l,o as se,a as ie,c as m,b as n,e as u,w as d,f as S,g as p,F as x,h as V,i as L,j as C,k as _,l as ue,t as g,V as de,_ as pe}from"./index-VeYmKv4z.js";const ce={class:"table-header-flex"},me={class:"form-btns"},ye={key:0},ve={key:2},be={key:3},fe={style:{display:"flex","justify-content":"flex-end","margin-top":"20px"}},R=420,_e=re({__name:"index",setup(I){const a=l({}),c=l(!1),w=l(""),v=l(!1);let k=[{type:"input",key:"positionName",label:"岗位名称"},{type:"input",key:"createUserName",label:"招聘者"},{type:"select",key:"manualInspectionStatus",label:"视检状态"},{type:"datetime",key:"dates",label:"提交时间"}];const T=[{property:"positionName",label:"岗位名称",width:""},{property:"jobType",label:"工作类型",width:""},{property:"createUserName",label:"招聘者",width:""},{property:"createTime",label:"提交时间",width:""},{property:"manualInspectionStatus",label:"视检状态",width:""}],b=l([]),i=l(1),y=l(10),Y=l("default"),E=l(!1),M=l(!1),D=l(0),N=l({}),F=l([{label:"待审核",value:0},{label:"通过",value:1},{label:"驳回",value:2}]),r=l({entity:{positionInfoSearchDTO:{endTime:"",startTime:"",manualInspectionStatus:"",createUserName:"",positionName:""},type:6},orderBy:{},page:1,size:10}),P=o=>{y.value=o,r.value.size=o,r.value.page=1,f()},A=o=>{i.value=o,r.value.page=o,f()},U=l(window.innerHeight-R);function O(){U.value=window.innerHeight-R}function Q(o){N.value=o.row,c.value=!0}const $=()=>{w.value="",c.value=!1,f()},q=()=>{a.value.dates&&a.value.dates.length===2?(r.value.entity.positionInfoSearchDTO.startTime=a.value.dates[0],r.value.entity.positionInfoSearchDTO.endTime=a.value.dates[1]):(delete r.value.entity.positionInfoSearchDTO.startTime,delete r.value.entity.positionInfoSearchDTO.endTime),r.value.entity.positionInfoSearchDTO.manualInspectionStatus=a.value.manualInspectionStatus,r.value.entity.positionInfoSearchDTO.positionName=a.value.positionName,r.value.entity.positionInfoSearchDTO.createUserName=a.value.createUserName,f()},G=()=>{a.value={},r.value={entity:{positionInfoSearchDTO:{endTime:"",startTime:"",manualInspectionStatus:"",createUserName:"",positionName:""},type:6},orderBy:{},page:1,size:10},f()},f=()=>j(null,null,function*(){v.value=!0;try{const o=yield oe(r.value);o.code===0&&(D.value=o.data.total,b.value=o.data.list)}catch(o){}finally{v.value=!1}});return se(()=>{f(),window.addEventListener("resize",O)}),ie(()=>{window.removeEventListener("resize",O)}),(o,s)=>{const J=p("el-input"),K=p("el-option"),W=p("el-select"),X=p("el-date-picker"),Z=p("el-form-item"),ee=p("el-form"),z=p("el-button"),B=p("el-card"),H=p("el-table-column"),te=p("el-table"),ae=p("el-pagination");return n(),m("div",null,[u(B,{shadow:"never"},{default:d(()=>[S("div",ce,[u(ee,{inline:!0,model:a.value,class:"table-header-form"},{default:d(()=>[(n(!0),m(x,null,V(L(k),(e,h)=>(n(),_(Z,{key:h,label:e.label,class:"form-item"},{default:d(()=>[e.type==="input"?(n(),_(J,{key:0,modelValue:a.value[e.key],"onUpdate:modelValue":t=>a.value[e.key]=t,size:"large",placeholder:"请输入"+e.label,style:{width:"180px"},clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="select"?(n(),_(W,{key:1,modelValue:a.value[e.key],"onUpdate:modelValue":t=>a.value[e.key]=t,size:"large",placeholder:"请选择"+e.label,style:{width:"180px"},clearable:""},{default:d(()=>[(n(!0),m(x,null,V(F.value,t=>(n(),_(K,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="datetime"?(n(),_(X,{key:2,modelValue:a.value[e.key],"onUpdate:modelValue":t=>a.value[e.key]=t,type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",size:"large",style:{width:"380px"},"value-format":"YYYY-MM-DD HH:mm:ss",clearable:""},null,8,["modelValue","onUpdate:modelValue"])):ue("",!0)]),_:2},1032,["label"]))),128))]),_:1},8,["model"]),S("div",me,[u(z,{size:"large",type:"primary",onClick:q},{default:d(()=>s[3]||(s[3]=[C("搜索")])),_:1}),u(z,{size:"large",type:"info",style:{"background-color":"#b4c4d1",color:"#ffffff",border:"#b4c4d1"},onClick:G},{default:d(()=>s[4]||(s[4]=[C("重置")])),_:1})])])]),_:1}),u(B,{shadow:"never",style:{"margin-top":"15px",padding:"5px 15px"}},{default:d(()=>[u(te,{ref:"tableContainer",data:b.value,loading:v.value,"element-loading-text":"数据加载中...","element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(255, 255, 255, 0.9)","element-loading-svg-view-box":"-10, -10, 50, 50",style:{width:"100%"},height:U.value},{default:d(()=>[(n(),m(x,null,V(T,(e,h)=>u(H,{key:h,width:e.width,label:e.label,"show-overflow-tooltip":""},{default:d(t=>[e.property==="createTime"?(n(),m("span",ye,g(L(ne)(t.row[e.property])),1)):e.property==="manualInspectionStatus"?(n(),m("span",{key:1,style:de({color:t.row[e.property]===0?"":t.row[e.property]===1?"#67C23A":"#fb5451"})},g(t.row[e.property]===0?"待审核":t.row[e.property]===1?"通过":"驳回"),5)):e.property==="jobType"?(n(),m("span",ve,g(t.row[e.property]===1?"全职":t.row[e.property]===2?"兼职":"实习"),1)):(n(),m("span",be,g(t.row[e.property]),1))]),_:2},1032,["width","label"])),64)),u(H,{fixed:"right",label:"操作","min-width":"120"},{default:d(e=>[u(z,{link:"",type:"primary",style:{color:"#279efb"},onClick:h=>Q(e)},{default:d(()=>s[5]||(s[5]=[C(" 详情 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","loading","height"]),S("div",fe,[u(ae,{"current-page":i.value,"onUpdate:currentPage":s[0]||(s[0]=e=>i.value=e),"page-size":y.value,"onUpdate:pageSize":s[1]||(s[1]=e=>y.value=e),"page-sizes":[10,20,50,100],size:Y.value,disabled:M.value,background:E.value,layout:"total, sizes, prev, pager, next, jumper",total:D.value,onSizeChange:P,onCurrentChange:A},null,8,["current-page","page-size","size","disabled","background","total"])])]),_:1}),u(le,{dialogVisible:c.value,"onUpdate:dialogVisible":s[2]||(s[2]=e=>c.value=e),currentRow:N.value,onCancelBtn:$},null,8,["dialogVisible","currentRow"])])}}}),ze=pe(_e,[["__scopeId","data-v-10286884"]]);export{ze as default};
