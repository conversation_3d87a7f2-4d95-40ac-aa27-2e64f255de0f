import{d as u,P as i,a as r,r as m,c as f,b as p,e as v,w as c,f as e,i as V,aU as w}from"./index-VeYmKv4z.js";const B=u({__name:"details",props:{showDialog:{type:Boolean,default:!1},data:{type:String,default:""}},setup(n){const o=n;i(()=>o.data,a=>{},{immediate:!0}),i(()=>o.showDialog,a=>{a&&s()},{immediate:!0}),r(()=>{});const l=m(!0);function s(a){l.value=!0}return(a,t)=>(p(),f("div",null,[v(V(w),{modelValue:l.value,"onUpdate:modelValue":t[0]||(t[0]=d=>l.value=d),title:"标题"},{default:c(()=>t[1]||(t[1]=[e("div",null,[e("div",null," 审核图片 "),e("div",null,[e("div"),e("div")])],-1)])),_:1},8,["modelValue"])]))}});export{B as default};
