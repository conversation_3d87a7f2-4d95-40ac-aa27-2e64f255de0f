var ie=Object.defineProperty;var N=Object.getOwnPropertySymbols;var ue=Object.prototype.hasOwnProperty,de=Object.prototype.propertyIsEnumerable;var L=(d,t,a)=>t in d?ie(d,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):d[t]=a,A=(d,t)=>{for(var a in t||(t={}))ue.call(t,a)&&L(d,a,t[a]);if(N)for(var a of N(t))de.call(t,a)&&L(d,a,t[a]);return d};var Y=(d,t,a)=>new Promise((w,h)=>{var C=s=>{try{y(a.next(s))}catch(m){h(m)}},v=s=>{try{y(a.throw(s))}catch(m){h(m)}},y=s=>s.done?w(s.value):Promise.resolve(s.value).then(C,v);y((a=a.apply(d,t)).next())});import pe from"./index-2lhC7KPk.js";import{d as ce,r as o,o as me,u as ve,a as ye,c as _,b as i,e as p,w as u,f as R,g as c,F as j,h as E,i as O,j as z,k,l as S,t as U,_ as fe}from"./index-VeYmKv4z.js";import{a as be}from"./index-D6X2KkBM.js";import{f as _e}from"./dateFormat-BuOeynu9.js";import"./quickReply-DfweD696.js";import"./index-C3VS7c2V.js";const he={class:"table-header-flex"},ge={class:"form-btns"},ke={key:0},we={key:1},Ce={key:2},xe={style:{display:"flex","justify-content":"flex-end","margin-top":"20px"}},P=420,ze=ce({__name:"index",setup(d){const t=o({}),a=o(!1),w=o(!1),h=o(0),C=o([]),v=o(0),y=o({}),s=o(""),m=o(!1),F=ve(),r=o({entity:{endTime:"",name:"",phone:"",startTime:"",status:null},orderBy:{},page:1,size:10});let $=[{type:"input",key:"name",label:"姓名"},{type:"input",key:"phone",label:"手机号"},{type:"datetime",key:"dates",label:"提交时间"}];const I=[{property:"name",label:"姓名",width:""},{property:"age",label:"年龄",width:""},{property:"sex",label:"性别",width:""},{property:"phone",label:"手机号码",width:""},{property:"createTime",label:"提交时间",width:""}],T=o(1),V=o(10),Q=o("default"),q=o(!1),G=o(!1),J=l=>{V.value=l,r.value.size=l,r.value.page=1,b()},K=l=>{T.value=l,r.value.page=l,b()},B=o(window.innerHeight-P);function H(){B.value=window.innerHeight-P}const b=()=>Y(null,null,function*(){w.value=!0;try{const l=yield be(A({},r.value));l.code===0&&(h.value=l.data.total,C.value=l.data.list)}catch(l){}finally{w.value=!1}}),W=()=>{t.value={},r.value.entity={endTime:"",name:"",phone:"",startTime:"",status:v.value},b()},X=()=>{t.value.dates&&t.value.dates.length===2?(r.value.entity.startTime=t.value.dates[0],r.value.entity.endTime=t.value.dates[1]):(delete r.value.entity.startTime,delete r.value.entity.endTime),r.value.entity.name=t.value.name||void 0,r.value.entity.phone=t.value.phone||void 0,b()},Z=l=>{y.value=l.row,a.value=!0,m.value=!1,s.value="note"},ee=l=>{y.value=l.row,a.value=!0,m.value=!0,s.value="record"},te=()=>{s.value="",a.value=!1,m.value=!1,b()};return me(()=>{const l=F.meta.businessStatus;r.value.entity.status=l!==void 0?Number(l):0,v.value=l!==void 0?Number(l):0,b(),window.addEventListener("resize",H)}),ye(()=>{window.removeEventListener("resize",H)}),(l,n)=>{const ae=c("el-input"),le=c("el-date-picker"),oe=c("el-form-item"),ne=c("el-form"),x=c("el-button"),D=c("el-card"),M=c("el-table-column"),se=c("el-table"),re=c("el-pagination");return i(),_("div",null,[p(D,{shadow:"never"},{default:u(()=>[R("div",he,[p(ne,{inline:!0,model:t.value,class:"table-header-form"},{default:u(()=>[(i(!0),_(j,null,E(O($),(e,g)=>(i(),k(oe,{key:g,label:e.label,class:"form-item"},{default:u(()=>[e.type==="input"?(i(),k(ae,{key:0,modelValue:t.value[e.key],"onUpdate:modelValue":f=>t.value[e.key]=f,size:"large",placeholder:"请输入"+e.label,clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="datetime"?(i(),k(le,{key:1,modelValue:t.value[e.key],"onUpdate:modelValue":f=>t.value[e.key]=f,type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",size:"large",style:{width:"380px"},"value-format":"YYYY-MM-DD HH:mm:ss",clearable:""},null,8,["modelValue","onUpdate:modelValue"])):S("",!0)]),_:2},1032,["label"]))),128))]),_:1},8,["model"]),R("div",ge,[p(x,{size:"large",type:"primary",onClick:X},{default:u(()=>n[3]||(n[3]=[z("搜索")])),_:1}),p(x,{size:"large",type:"info",style:{"background-color":"#b4c4d1",color:"#ffffff",border:"#b4c4d1"},onClick:W},{default:u(()=>n[4]||(n[4]=[z("重置")])),_:1})])])]),_:1}),p(D,{shadow:"never",style:{"margin-top":"15px",padding:"5px 15px"}},{default:u(()=>[p(se,{ref:"tableContainer",data:C.value,style:{width:"100%"},border:"",height:B.value},{default:u(()=>[(i(),_(j,null,E(I,(e,g)=>p(M,{key:g,width:e.width,label:e.label,property:e.property,"show-overflow-tooltip":""},{default:u(f=>[e.property==="createTime"?(i(),_("span",ke,U(O(_e)(f.row[e.property])),1)):e.property==="sex"?(i(),_("span",we,U(f.row[e.property]===1?"男":"女"),1)):(i(),_("span",Ce,U(f.row[e.property]),1))]),_:2},1032,["width","label","property"])),64)),p(M,{fixed:"right",label:"操作","min-width":"60"},{default:u(e=>[v.value===0?(i(),k(x,{key:0,link:"",type:"primary",style:{color:"#279efb"},onClick:g=>Z(e)},{default:u(()=>n[5]||(n[5]=[z(" 视检 ")])),_:2},1032,["onClick"])):S("",!0),v.value===1||v.value===2?(i(),k(x,{key:1,link:"",type:"primary",style:{color:"#4eb906"},onClick:g=>ee(e)},{default:u(()=>n[6]||(n[6]=[z(" 操作记录 ")])),_:2},1032,["onClick"])):S("",!0)]),_:1})]),_:1},8,["data","height"]),R("div",xe,[p(re,{"current-page":T.value,"onUpdate:currentPage":n[0]||(n[0]=e=>T.value=e),"page-size":V.value,"onUpdate:pageSize":n[1]||(n[1]=e=>V.value=e),"page-sizes":[10,20,50,100],size:Q.value,disabled:G.value,background:q.value,layout:"total, sizes, prev, pager, next, jumper",total:h.value,onSizeChange:J,onCurrentChange:K},null,8,["current-page","page-size","size","disabled","background","total"])])]),_:1}),p(pe,{dialogVisible:a.value,"onUpdate:dialogVisible":n[2]||(n[2]=e=>a.value=e),isRightType:s.value,currentRow:y.value,closeOnClickModal:m.value,onCancelBtn:te},null,8,["dialogVisible","isRightType","currentRow","closeOnClickModal"])])}}}),De=fe(ze,[["__scopeId","data-v-cca65ded"]]);export{De as default};
