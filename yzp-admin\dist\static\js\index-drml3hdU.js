var L=(k,T,p)=>new Promise((d,i)=>{var C=l=>{try{u(p.next(l))}catch(c){i(c)}},v=l=>{try{u(p.throw(l))}catch(c){i(c)}},u=l=>l.done?d(l.value):Promise.resolve(l.value).then(C,v);u((p=p.apply(k,T)).next())});import{f as H,T as J,A as K,R as W}from"./index-DFv13GLf.js";import{h as X,d as Y}from"./index-Dgu8EOgW.js";import{d as Z,r as n,x as V,O as D,c as f,b as o,S as ee,ab as te,k as y,w as r,e as s,f as g,l as ae,g as _,j as h,t as N,i as se,F as oe,h as le,aQ as w,_ as ie}from"./index-DOMkE6w1.js";const ne={class:"content-row"},re={class:"img-card-row"},de={key:1,class:"img-placeholder"},ue={key:1,class:"no-data-card"},ce={class:"card-box"},me={key:0,class:"btn-group-bottom"},pe=Z({__name:"index",props:{dialogVisible:{type:Boolean,default:!1},isRightType:{type:String,default:""},currentRow:{type:Object,default:()=>({})}},emits:["cancelBtn","update:dialogVisible","update:updataList"],setup(k,{emit:T}){const p=n(!1),d=n("note"),i=n({}),C=n(""),v=n([]),u=n([]),l=n([]),c=T,x=k,B=V({get(){return x.dialogVisible},set(e){c("update:dialogVisible",e)}});D(()=>x.isRightType,e=>{d.value=e},{immediate:!0});const j=()=>L(null,null,function*(){const e=yield Y({id:i.value.id});e.code===0&&(v.value=e.data.slice(0,2))});function I(){c("cancelBtn",!0),d.value="note"}const S=e=>L(null,null,function*(){(yield X(e)).code===0&&(w.success("操作成功"),I())});function E(e){const t={id:i.value.id,auditStatus:1};S(t),c("update:updataList",!0)}function O(e){if(!b.value||b.value.trim()===""){w.warning("请填写驳回原因");return}const t={id:i.value.id,auditStatus:2,reason:b.value};S(t)}function $(){d.value="transfer"}function F(e){I()}const P=[{name:"A组张三",value:"zhangsan"},{name:"A组李四",value:"lisi"}],R=n("lisi"),z=[{label:"自定义批注",value:"none",content:""},{label:"资料不合规",value:"lack",content:"您提交的资料不合规，请重新提交。"}],b=n("");return D(()=>x.currentRow,e=>{u.value=[],l.value=[],u.value.push({auditUserName:(e==null?void 0:e.manualInspectionUserName)||"",auditTime:(e==null?void 0:e.manualInspectionTime)||"",status:(e==null?void 0:e.status)===1?"1":(e==null?void 0:e.status)===2?"2":"0",reason:(e==null?void 0:e.reason)||""}),i.value=e,C.value=e.headImgUrl,j()},{immediate:!0}),(e,t)=>{const m=_("el-table-column"),A=_("el-table"),M=_("el-image"),U=_("el-button"),Q=_("el-dialog"),q=te("loading");return o(),f("div",null,[ee((o(),y(Q,{modelValue:B.value,"onUpdate:modelValue":t[4]||(t[4]=a=>B.value=a),"close-on-click-modal":"","destroy-on-close":"",title:null,width:"1100px","show-close":!0,class:"custom-detail-dialog","align-center":"",onClose:I},{default:r(()=>[s(A,{data:[i.value],border:"",style:{width:"100%","margin-bottom":"12px"},class:"info-table-normal"},{default:r(()=>[s(m,{prop:"name","show-overflow-tooltip":"",label:"企业名称","min-width":"180"}),s(m,{prop:"socialCreditCode","show-overflow-tooltip":"",label:"社会信用代码","min-width":"180"}),s(m,{prop:"enterpriseLegalPerson",label:"法人","min-width":"120"})]),_:1},8,["data"]),s(A,{data:[i.value],border:"",style:{width:"100%"},class:"info-table-normal"},{default:r(()=>[s(m,{prop:"id",label:"提交人ID","min-width":"100"},{default:r(a=>[h(N(`ID：${a.row.id}`),1)]),_:1}),s(m,{prop:"createUserName",label:"提交人姓名","min-width":"120"}),s(m,{prop:"phone",label:"提交人电话","min-width":"80"}),s(m,{prop:"createTime",label:"提交时间","min-width":"180"},{default:r(a=>[h(N(se(H)(a.row.createTime)),1)]),_:1})]),_:1},8,["data"]),g("div",ne,[g("div",re,[v.value&&v.value.length>0?(o(!0),f(oe,{key:0},le(v.value,(a,G)=>(o(),f("div",{key:G,class:"img-card"},[t[6]||(t[6]=g("div",{class:"img-title"},"公司照片",-1)),a.fileIdUrl?(o(),y(M,{key:0,src:a.fileIdUrl,class:"img-preview",fit:"contain","preview-src-list":[a.fileIdUrl],"initial-index":0},{error:r(()=>t[5]||(t[5]=[g("div",{class:"img-placeholder"},null,-1)])),_:2},1032,["src","preview-src-list"])):(o(),f("div",de))]))),128)):(o(),f("div",ue,t[7]||(t[7]=[g("div",{class:"no-data-text"},"暂无数据",-1)])))]),g("div",ce,[d.value==="transfer"?(o(),y(J,{key:0,modelValue:R.value,"onUpdate:modelValue":t[0]||(t[0]=a=>R.value=a),transferList:P,onSubmit:F},null,8,["modelValue"])):d.value==="record"?(o(),y(K,{key:1,auditList:u.value,statusList:l.value},null,8,["auditList","statusList"])):(o(),y(W,{key:2,modelValue:b.value,"onUpdate:modelValue":t[1]||(t[1]=a=>b.value=a),options:z},null,8,["modelValue"]))])]),d.value==="note"?(o(),f("div",me,[s(U,{type:"primary",style:{background:"#3477f4",border:"none"},onClick:$},{default:r(()=>t[8]||(t[8]=[h("转审")])),_:1}),s(U,{type:"danger",onClick:t[2]||(t[2]=()=>O("reject"))},{default:r(()=>t[9]||(t[9]=[h("驳回")])),_:1}),s(U,{type:"success",onClick:t[3]||(t[3]=()=>E("pass"))},{default:r(()=>t[10]||(t[10]=[h("通过")])),_:1})])):ae("",!0)]),_:1},8,["modelValue"])),[[q,p.value]])])}}}),ye=ie(pe,[["__scopeId","data-v-6b172684"]]);export{ye as default};
