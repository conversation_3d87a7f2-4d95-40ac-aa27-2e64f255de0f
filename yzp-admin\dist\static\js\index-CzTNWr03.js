var ie=Object.defineProperty;var Y=Object.getOwnPropertySymbols;var ue=Object.prototype.hasOwnProperty,de=Object.prototype.propertyIsEnumerable;var j=(d,t,a)=>t in d?ie(d,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):d[t]=a,E=(d,t)=>{for(var a in t||(t={}))ue.call(t,a)&&j(d,a,t[a]);if(Y)for(var a of Y(t))de.call(t,a)&&j(d,a,t[a]);return d};var M=(d,t,a)=>new Promise((T,c)=>{var z=r=>{try{m(a.next(r))}catch(k){c(k)}},h=r=>{try{m(a.throw(r))}catch(k){c(k)}},m=r=>r.done?T(r.value):Promise.resolve(r.value).then(z,h);m((a=a.apply(d,t)).next())});import{d as pe,r as n,o as ce,u as me,a as ve,c as g,b as i,e as p,w as u,f as B,g as v,F as P,h as $,i as A,j as C,k as _,l as x,t as S,_ as ye}from"./index-DOMkE6w1.js";import fe from"./index-CR1_BhA4.js";import{g as be}from"./index-C5VTKhL7.js";import{f as ge}from"./index-DFv13GLf.js";import"./quickReply-D6WzbhB8.js";const _e={class:"table-header-flex"},he={class:"form-btns"},ke={key:0},we={key:1},Ce={key:2},ze={style:{display:"flex","justify-content":"flex-end","margin-top":"20px"}},F=420,xe=pe({__name:"index",setup(d){const t=n({}),a=n(!1),T=me(),c=n(0),z=n(0),h=n(!1),m=n(""),r=n({});let k=[{type:"input",key:"name",label:"姓名"},{type:"input",key:"phone",label:"手机号"},{type:"datetime",key:"dates",label:"提交时间"}];const s=n({entity:{endTime:"",name:"",phone:"",startTime:"",status:null},orderBy:{},page:1,size:10}),I=[{property:"name",label:"姓名",width:""},{property:"age",label:"年龄",width:""},{property:"sex",label:"性别",width:""},{property:"phone",label:"手机号码",width:""},{property:"certificate",label:"证书名称",width:""},{property:"createTime",label:"提交时间",width:""}],U=n([]),f=()=>M(null,null,function*(){h.value=!0;try{const l=yield be(E({},s.value));l.code===0&&(z.value=l.data.total,U.value=l.data.list)}catch(l){}finally{h.value=!1}}),V=n(1),R=n(10),Q=n("default"),q=n(!1),G=n(!1),J=l=>{R.value=l,s.value.size=l,s.value.page=1,f()},K=l=>{V.value=l,s.value.page=l,f()},H=n(window.innerHeight-F);function D(){H.value=window.innerHeight-F}const O=l=>{r.value=l.row,a.value=!0,m.value="note"},W=l=>{r.value=l.row,a.value=!0,m.value="transfer"},X=l=>{r.value=l.row,a.value=!0,m.value="record"},Z=()=>{m.value="",a.value=!1,f()},ee=()=>{t.value.dates&&t.value.dates.length===2?(s.value.entity.startTime=t.value.dates[0],s.value.entity.endTime=t.value.dates[1]):(delete s.value.entity.startTime,delete s.value.entity.endTime),s.value.entity.name=t.value.name||void 0,s.value.entity.phone=t.value.phone||void 0,f()},te=()=>{t.value={},s.value={entity:{endTime:"",name:"",phone:"",startTime:"",status:c.value},orderBy:{},page:1,size:10},f()};return ce(()=>{const l=T.meta.businessStatus;s.value.entity.status=l!==void 0?Number(l):1,c.value=l!==void 0?Number(l):1,f(),window.addEventListener("resize",D)}),ve(()=>{window.removeEventListener("resize",D)}),(l,o)=>{const ae=v("el-input"),le=v("el-date-picker"),oe=v("el-form-item"),ne=v("el-form"),w=v("el-button"),N=v("el-card"),L=v("el-table-column"),re=v("el-table"),se=v("el-pagination");return i(),g("div",null,[p(N,{shadow:"never"},{default:u(()=>[B("div",_e,[p(ne,{inline:!0,model:t.value,class:"table-header-form"},{default:u(()=>[(i(!0),g(P,null,$(A(k),(e,b)=>(i(),_(oe,{key:b,label:e.label,class:"form-item"},{default:u(()=>[e.type==="input"?(i(),_(ae,{key:0,modelValue:t.value[e.key],"onUpdate:modelValue":y=>t.value[e.key]=y,size:"large",placeholder:"请输入"+e.label,clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="datetime"?(i(),_(le,{key:1,modelValue:t.value[e.key],"onUpdate:modelValue":y=>t.value[e.key]=y,type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",size:"large",style:{width:"380px"},"value-format":"YYYY-MM-DD HH:mm:ss",clearable:""},null,8,["modelValue","onUpdate:modelValue"])):x("",!0)]),_:2},1032,["label"]))),128))]),_:1},8,["model"]),B("div",he,[p(w,{size:"large",type:"primary",onClick:ee},{default:u(()=>o[3]||(o[3]=[C("搜索")])),_:1}),p(w,{size:"large",type:"info",style:{"background-color":"#b4c4d1",color:"#ffffff",border:"#b4c4d1"},onClick:te},{default:u(()=>o[4]||(o[4]=[C("重置")])),_:1})])])]),_:1}),p(N,{shadow:"never",style:{"margin-top":"15px",padding:"5px 15px"}},{default:u(()=>[p(re,{ref:"tableContainer",data:U.value,style:{width:"100%"},loading:h.value,height:H.value},{default:u(()=>[(i(),g(P,null,$(I,(e,b)=>p(L,{key:b,width:e.width,label:e.label,property:e.property,"show-overflow-tooltip":""},{default:u(y=>[e.property==="createTime"?(i(),g("span",ke,S(A(ge)(y.row[e.property])),1)):e.property==="sex"?(i(),g("span",we,S(y.row[e.property]===1?"男":"女"),1)):(i(),g("span",Ce,S(y.row[e.property]),1))]),_:2},1032,["width","label","property"])),64)),p(L,{fixed:"right",label:"操作","min-width":"60"},{default:u(e=>[c.value===1?(i(),_(w,{key:0,link:"",type:"primary",style:{color:"#279efb"},onClick:b=>O(e)},{default:u(()=>o[5]||(o[5]=[C(" 视检 ")])),_:2},1032,["onClick"])):x("",!0),c.value===1?(i(),_(w,{key:1,link:"",type:"primary",style:{color:"#fb2727"},onClick:b=>W(e)},{default:u(()=>o[6]||(o[6]=[C(" 转办 ")])),_:2},1032,["onClick"])):x("",!0),c.value===2||c.value===3?(i(),_(w,{key:2,link:"",type:"primary",style:{color:"#4eb906"},onClick:b=>X(e)},{default:u(()=>o[7]||(o[7]=[C(" 操作记录 ")])),_:2},1032,["onClick"])):x("",!0)]),_:1})]),_:1},8,["data","loading","height"]),B("div",ze,[p(se,{"current-page":V.value,"onUpdate:currentPage":o[0]||(o[0]=e=>V.value=e),"page-size":R.value,"onUpdate:pageSize":o[1]||(o[1]=e=>R.value=e),"page-sizes":[10,20,50,100],size:Q.value,disabled:G.value,background:q.value,layout:"total, sizes, prev, pager, next, jumper",total:z.value,onSizeChange:J,onCurrentChange:K},null,8,["current-page","page-size","size","disabled","background","total"])])]),_:1}),p(fe,{dialogVisible:a.value,"onUpdate:dialogVisible":o[2]||(o[2]=e=>a.value=e),isRightType:m.value,currentRow:r.value,onCancelBtn:Z},null,8,["dialogVisible","isRightType","currentRow"])])}}}),He=ye(xe,[["__scopeId","data-v-4fb1d315"]]);export{He as default};
