import{d as B,r as f,m as h,c as i,b as o,f as u,e as a,w as n,g as r,F as k,h as b,t as v,n as N,k as T,p as q,_ as F}from"./index-VeYmKv4z.js";const S={class:"task-list-page"},$={class:"task-list-header"},z={key:1,class:"executor-none"},E={class:"remain-red"},I=["onClick"],L=["onClick"],O=B({__name:"index",setup(P){const _=f([{name:"头像",executors:["张三","李四","王正国"],time:"2024年12月31日 23:59",remain:1999},{name:"简历",executors:[],time:"",remain:0}]),w=h(()=>Array.from(new Set(_.value.map(t=>t.name)))),s=f({name:"",project:""}),x=h(()=>_.value.filter(t=>{const l=!s.value.name||t.name===s.value.name,d=!s.value.project||t.project&&t.project.includes(s.value.project);return l&&d})),p=N();function g(t){p.push({name:"TaskAssignmentDetail",query:{id:t.id}})}function y(t){p.push({name:"TaskAssignmentDetail",query:{id:t.id}})}return(t,l)=>{const d=r("el-option"),C=r("el-select"),j=r("el-form-item"),V=r("el-form"),c=r("el-table-column"),D=r("el-table");return o(),i("div",S,[u("div",$,[l[1]||(l[1]=u("span",{class:"task-list-title"},"待审批列表表",-1)),a(V,{inline:!0,class:"task-list-form"},{default:n(()=>[a(j,{label:"审批名称"},{default:n(()=>[a(C,{modelValue:s.value.name,"onUpdate:modelValue":l[0]||(l[0]=e=>s.value.name=e),placeholder:"请输入项目名称",style:{width:"260px"}},{default:n(()=>[(o(!0),i(k,null,b(w.value,e=>(o(),T(d,{key:e,label:e,value:e},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),a(D,{data:x.value,border:"",class:"task-list-table"},{default:n(()=>[a(c,{prop:"name",label:"名称","min-width":"100"}),a(c,{prop:"executors",label:"执行","min-width":"200"},{default:n(e=>[e.row.executors&&e.row.executors.length?(o(!0),i(k,{key:0},b(e.row.executors,(m,A)=>(o(),i("span",{key:m,class:q(["executor-tag","executor-tag-"+A])},v(m),3))),128)):(o(),i("span",z,"无"))]),_:1}),a(c,{prop:"time",label:"时间","min-width":"180"}),a(c,{prop:"remain",label:"剩余","min-width":"80"},{default:n(e=>[u("span",E,v(e.row.remain),1)]),_:1}),a(c,{label:"状态","min-width":"120"},{default:n(e=>[u("a",{class:"status-link",onClick:m=>g(e.row)},"查看",8,I),u("span",{class:"status-assign",onClick:m=>y(e.row)},"分配",8,L)]),_:1})]),_:1},8,["data"])])}}}),U=F(O,[["__scopeId","data-v-f9067b45"]]);export{U as default};
