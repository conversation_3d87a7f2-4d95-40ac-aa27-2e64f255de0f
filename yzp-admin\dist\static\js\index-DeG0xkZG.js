var U=(h,T,p)=>new Promise((r,i)=>{var C=l=>{try{u(p.next(l))}catch(c){i(c)}},v=l=>{try{u(p.throw(l))}catch(c){i(c)}},u=l=>l.done?r(l.value):Promise.resolve(l.value).then(C,v);u((p=p.apply(h,T)).next())});import{f as G}from"./dateFormat-BuOeynu9.js";import{T as H,A as J,R as K,n as W}from"./quickReply-DfweD696.js";import{h as X,d as Y}from"./index-BeicPvf_.js";import{d as Z,r as n,m as V,P as D,c as f,b as s,T as ee,ab as te,k as y,w as d,e as o,f as g,l as ae,g as _,j as k,t as N,i as w,F as se,h as oe,aQ as j,_ as le}from"./index-VeYmKv4z.js";const ie={class:"content-row"},ne={class:"img-card-row"},de={key:1,class:"img-placeholder"},re={key:1,class:"no-data-card"},ue={class:"card-box"},ce={key:0,class:"btn-group-bottom"},me=Z({__name:"index",props:{dialogVisible:{type:Boolean,default:!1},isRightType:{type:String,default:""},currentRow:{type:Object,default:()=>({})},closeOnClickModal:{type:Boolean,default:!1}},emits:["cancelBtn","update:dialogVisible","update:updataList"],setup(h,{emit:T}){const p=n(!1),r=n("note"),i=n({}),C=n(""),v=n([]),u=n([]),l=n([]),c=T,I=h,L=V({get(){return I.dialogVisible},set(e){c("update:dialogVisible",e)}});D(()=>I.isRightType,e=>{r.value=e},{immediate:!0});const O=()=>U(null,null,function*(){const e=yield Y({id:i.value.id});e.code===0&&(v.value=e.data.slice(0,2))});function x(){c("cancelBtn",!0),r.value="note"}const B=e=>U(null,null,function*(){(yield X(e)).code===0&&(j.success("操作成功"),x())});function E(e){const t={id:i.value.id,auditStatus:1};B(t),c("update:updataList",!0)}function M(e){if(!b.value||b.value.trim()===""){j.warning("请填写驳回原因");return}const t={id:i.value.id,auditStatus:2,reason:b.value};B(t)}function P(e){x()}const $=[{name:"A组张三",value:"zhangsan"},{name:"A组李四",value:"lisi"}],R=n("lisi"),b=n("");return D(()=>I.currentRow,e=>{u.value=[],l.value=[],u.value.push({auditUserName:(e==null?void 0:e.manualInspectionUserName)||"",auditTime:(e==null?void 0:e.manualInspectionTime)||"",status:(e==null?void 0:e.status)===1?"1":(e==null?void 0:e.status)===2?"2":"0",reason:(e==null?void 0:e.reason)||""}),i.value=e,C.value=e.headImgUrl,O()},{immediate:!0}),(e,t)=>{const m=_("el-table-column"),S=_("el-table"),F=_("el-image"),A=_("el-button"),z=_("el-dialog"),Q=te("loading");return s(),f("div",null,[ee((s(),y(z,{modelValue:L.value,"onUpdate:modelValue":t[4]||(t[4]=a=>L.value=a),"close-on-click-modal":h.closeOnClickModal,"destroy-on-close":"",title:null,width:"1100px","show-close":!0,class:"custom-detail-dialog","align-center":"",onClose:x},{default:d(()=>[o(S,{data:[i.value],border:"",style:{width:"100%","margin-bottom":"12px"},class:"info-table-normal"},{default:d(()=>[o(m,{prop:"name","show-overflow-tooltip":"",label:"企业名称","min-width":"180"}),o(m,{prop:"socialCreditCode","show-overflow-tooltip":"",label:"社会信用代码","min-width":"180"}),o(m,{prop:"enterpriseLegalPerson",label:"法人","min-width":"120"})]),_:1},8,["data"]),o(S,{data:[i.value],border:"",style:{width:"100%"},class:"info-table-normal"},{default:d(()=>[o(m,{prop:"id",label:"提交人ID","min-width":"100"},{default:d(a=>[k(N(`ID：${a.row.id}`),1)]),_:1}),o(m,{prop:"createUserName",label:"提交人姓名","min-width":"120"}),o(m,{prop:"phone",label:"提交人电话","min-width":"80"}),o(m,{prop:"createTime",label:"提交时间","min-width":"180"},{default:d(a=>[k(N(w(G)(a.row.createTime)),1)]),_:1})]),_:1},8,["data"]),g("div",ie,[g("div",ne,[v.value&&v.value.length>0?(s(!0),f(se,{key:0},oe(v.value,(a,q)=>(s(),f("div",{key:q,class:"img-card"},[t[6]||(t[6]=g("div",{class:"img-title"},"公司照片",-1)),a.fileIdUrl?(s(),y(F,{key:0,src:a.fileIdUrl,class:"img-preview",fit:"contain","preview-src-list":[a.fileIdUrl],"initial-index":0},{error:d(()=>t[5]||(t[5]=[g("div",{class:"img-placeholder"},"加载失败",-1)])),_:2},1032,["src","preview-src-list"])):(s(),f("div",de,"暂无照片"))]))),128)):(s(),f("div",re,t[7]||(t[7]=[g("div",{class:"no-data-text"},"暂无数据",-1)])))]),g("div",ue,[r.value==="transfer"?(s(),y(H,{key:0,modelValue:R.value,"onUpdate:modelValue":t[0]||(t[0]=a=>R.value=a),transferList:$,onSubmit:P},null,8,["modelValue"])):r.value==="record"?(s(),y(J,{key:1,auditList:u.value,statusList:l.value},null,8,["auditList","statusList"])):(s(),y(K,{key:2,modelValue:b.value,"onUpdate:modelValue":t[1]||(t[1]=a=>b.value=a),options:w(W)},null,8,["modelValue","options"]))])]),r.value==="note"?(s(),f("div",ce,[o(A,{type:"danger",onClick:t[2]||(t[2]=()=>M("reject"))},{default:d(()=>t[8]||(t[8]=[k("驳回")])),_:1}),o(A,{type:"success",onClick:t[3]||(t[3]=()=>E("pass"))},{default:d(()=>t[9]||(t[9]=[k("通过")])),_:1})])):ae("",!0)]),_:1},8,["modelValue","close-on-click-modal"])),[[Q,p.value]])])}}}),ye=le(me,[["__scopeId","data-v-e869c345"]]);export{ye as default};
