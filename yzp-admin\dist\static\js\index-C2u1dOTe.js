import{d as v,g as r,ab as w,c as g,b as d,f as o,e as n,t as k,i as p,aT as x,w as t,j as i,k as a,l as f,aU as _,S as y}from"./index-DOMkE6w1.js";const B={class:"mb-2!"},V=v({name:"PermissionButtonRouter",__name:"index",setup(A){return(C,e)=>{const s=r("el-button"),l=r("Auth"),m=r("el-space"),u=r("el-card"),b=w("auth");return d(),g("div",null,[o("p",B,"当前拥有的code列表："+k(p(x)()),1),n(u,{shadow:"never",class:"mb-2"},{header:t(()=>e[0]||(e[0]=[o("div",{class:"card-header"},"组件方式判断权限",-1)])),default:t(()=>[n(m,{wrap:""},{default:t(()=>[n(l,{value:"permission:btn:add"},{default:t(()=>[n(s,{plain:"",type:"warning"},{default:t(()=>e[1]||(e[1]=[i(" 拥有code：'permission:btn:add' 权限可见 ")])),_:1})]),_:1}),n(l,{value:["permission:btn:edit"]},{default:t(()=>[n(s,{plain:"",type:"primary"},{default:t(()=>e[2]||(e[2]=[i(" 拥有code：['permission:btn:edit'] 权限可见 ")])),_:1})]),_:1}),n(l,{value:["permission:btn:add","permission:btn:edit","permission:btn:delete"]},{default:t(()=>[n(s,{plain:"",type:"danger"},{default:t(()=>e[3]||(e[3]=[i(" 拥有code：['permission:btn:add', 'permission:btn:edit', 'permission:btn:delete'] 权限可见 ")])),_:1})]),_:1})]),_:1})]),_:1}),n(u,{shadow:"never",class:"mb-2"},{header:t(()=>e[4]||(e[4]=[o("div",{class:"card-header"},"函数方式判断权限",-1)])),default:t(()=>[n(m,{wrap:""},{default:t(()=>[p(_)("permission:btn:add")?(d(),a(s,{key:0,plain:"",type:"warning"},{default:t(()=>e[5]||(e[5]=[i(" 拥有code：'permission:btn:add' 权限可见 ")])),_:1})):f("",!0),p(_)(["permission:btn:edit"])?(d(),a(s,{key:1,plain:"",type:"primary"},{default:t(()=>e[6]||(e[6]=[i(" 拥有code：['permission:btn:edit'] 权限可见 ")])),_:1})):f("",!0),p(_)(["permission:btn:add","permission:btn:edit","permission:btn:delete"])?(d(),a(s,{key:2,plain:"",type:"danger"},{default:t(()=>e[7]||(e[7]=[i(" 拥有code：['permission:btn:add', 'permission:btn:edit', 'permission:btn:delete'] 权限可见 ")])),_:1})):f("",!0)]),_:1})]),_:1}),n(u,{shadow:"never"},{header:t(()=>e[8]||(e[8]=[o("div",{class:"card-header"}," 指令方式判断权限（该方式不能动态修改权限） ",-1)])),default:t(()=>[n(m,{wrap:""},{default:t(()=>[y((d(),a(s,{plain:"",type:"warning"},{default:t(()=>e[9]||(e[9]=[i(" 拥有code：'permission:btn:add' 权限可见 ")])),_:1})),[[b,"permission:btn:add"]]),y((d(),a(s,{plain:"",type:"primary"},{default:t(()=>e[10]||(e[10]=[i(" 拥有code：['permission:btn:edit'] 权限可见 ")])),_:1})),[[b,["permission:btn:edit"]]]),y((d(),a(s,{plain:"",type:"danger"},{default:t(()=>e[11]||(e[11]=[i(" 拥有code：['permission:btn:add', 'permission:btn:edit', 'permission:btn:delete'] 权限可见 ")])),_:1})),[[b,["permission:btn:add","permission:btn:edit","permission:btn:delete"]]])]),_:1})]),_:1})])}}});export{V as default};
