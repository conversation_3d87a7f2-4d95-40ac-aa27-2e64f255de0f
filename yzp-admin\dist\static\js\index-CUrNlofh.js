var L=(S,o,r)=>new Promise((m,c)=>{var g=d=>{try{_(r.next(d))}catch(y){c(y)}},v=d=>{try{_(r.throw(d))}catch(y){c(y)}},_=d=>d.done?m(d.value):Promise.resolve(d.value).then(g,v);_((r=r.apply(S,o)).next())});import ne from"./index-Dht7BWil.js";import{a as se}from"./index-BeicPvf_.js";import{d as re,r as a,o as ie,u as ue,a as de,c as k,b as i,e as u,w as s,f as N,g as p,F as I,h as Y,i as j,j as z,k as w,l as U,t as E,_ as pe}from"./index-VeYmKv4z.js";import{f as ce}from"./dateFormat-BuOeynu9.js";import"./quickReply-DfweD696.js";const me={class:"table-header-flex"},ve={class:"form-btns"},ye={key:0},fe={key:1},be={style:{display:"flex","justify-content":"flex-end","margin-top":"20px"}},O=420,ge=re({__name:"index",setup(S){const o=a({}),r=a(!1),m=a(""),c=a(0),g=a(!1),v=a(!1);let _=[{type:"input",key:"createUserName",label:"招聘者"},{type:"input",key:"name",label:"公司名称"},{type:"input",key:"positionName",label:"岗位名称"},{type:"datetime",key:"dates",label:"提交时间"}];const d=[{property:"name",label:"公司名称",width:""},{property:"positionName",label:"岗位名称",width:""},{property:"createUserName",label:"招聘者",width:""},{property:"phone",label:"电话",width:""},{property:"createTime",label:"提交时间",width:""}],y=a([]),x=a(1),T=a(10),P=a("default"),F=a(!1),J=a(!1),R=a(0),V=a({}),$=ue(),n=a({entity:{name:"",positionName:"",createUserName:"",startTime:"",endTime:"",manualInspectionStatus:null},orderBy:{},page:1,size:10}),A=t=>{T.value=t,n.value.size=t,n.value.page=1,f()},Q=t=>{x.value=t,n.value.page=t,f()},B=a(window.innerHeight-O);function D(){B.value=window.innerHeight-O}function q(t){V.value=t.row,r.value=!0,v.value=!1,m.value="note"}const G=t=>{V.value=t.row,r.value=!0,v.value=!0,m.value="record"},K=()=>{m.value="",r.value=!1,v.value=!1,f()},W=()=>{o.value.dates&&o.value.dates.length===2?(n.value.entity.startTime=o.value.dates[0],n.value.entity.endTime=o.value.dates[1]):(delete n.value.entity.startTime,delete n.value.entity.endTime),n.value.entity.name=o.value.name||void 0,f()},X=()=>{o.value={},n.value={entity:{name:"",positionName:"",createUserName:"",startTime:"",endTime:"",manualInspectionStatus:c.value},orderBy:{},page:1,size:10},f()},f=()=>L(null,null,function*(){g.value=!0;try{const t=yield se(n.value);t.code===0&&(R.value=t.data.total,y.value=t.data.list)}catch(t){}finally{g.value=!1}});return ie(()=>{const t=$.meta.businessStatus;n.value.entity.manualInspectionStatus=t!==void 0?Number(t):0,c.value=t!==void 0?Number(t):0,f(),window.addEventListener("resize",D)}),de(()=>{window.removeEventListener("resize",D)}),(t,l)=>{const Z=p("el-input"),ee=p("el-date-picker"),te=p("el-form-item"),ae=p("el-form"),C=p("el-button"),H=p("el-card"),M=p("el-table-column"),le=p("el-table"),oe=p("el-pagination");return i(),k("div",null,[u(H,{shadow:"never"},{default:s(()=>[N("div",me,[u(ae,{inline:!0,model:o.value,class:"table-header-form"},{default:s(()=>[(i(!0),k(I,null,Y(j(_),(e,h)=>(i(),w(te,{key:h,label:e.label,class:"form-item"},{default:s(()=>[e.type==="input"?(i(),w(Z,{key:0,modelValue:o.value[e.key],"onUpdate:modelValue":b=>o.value[e.key]=b,placeholder:"请输入"+e.label,clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="datetime"?(i(),w(ee,{key:1,modelValue:o.value[e.key],"onUpdate:modelValue":b=>o.value[e.key]=b,type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",style:{width:"380px"},"value-format":"YYYY-MM-DD HH:mm:ss",clearable:""},null,8,["modelValue","onUpdate:modelValue"])):U("",!0)]),_:2},1032,["label"]))),128))]),_:1},8,["model"]),N("div",ve,[u(C,{size:"large",type:"primary",onClick:W},{default:s(()=>l[3]||(l[3]=[z("搜索")])),_:1}),u(C,{size:"large",type:"info",style:{"background-color":"#b4c4d1",color:"#ffffff",border:"#b4c4d1"},onClick:X},{default:s(()=>l[4]||(l[4]=[z("重置")])),_:1})])])]),_:1}),u(H,{shadow:"never",style:{"margin-top":"15px",padding:"5px 15px"}},{default:s(()=>[u(le,{ref:"tableContainer",data:y.value,loading:g.value,style:{width:"100%"},border:"",height:B.value},{default:s(()=>[(i(),k(I,null,Y(d,(e,h)=>u(M,{key:h,width:e.width,label:e.label,"show-overflow-tooltip":""},{default:s(b=>[e.property==="createTime"?(i(),k("span",ye,E(j(ce)(b.row[e.property])),1)):(i(),k("span",fe,E(b.row[e.property]),1))]),_:2},1032,["width","label"])),64)),u(M,{fixed:"right",label:"操作","min-width":"120"},{default:s(e=>[c.value===0?(i(),w(C,{key:0,link:"",type:"primary",style:{color:"#279efb"},onClick:h=>q(e)},{default:s(()=>l[5]||(l[5]=[z(" 视检 ")])),_:2},1032,["onClick"])):U("",!0),c.value===1||c.value===2?(i(),w(C,{key:1,link:"",type:"primary",style:{color:"#4eb906"},onClick:h=>G(e)},{default:s(()=>l[6]||(l[6]=[z(" 操作记录 ")])),_:2},1032,["onClick"])):U("",!0)]),_:1})]),_:1},8,["data","loading","height"]),N("div",be,[u(oe,{"current-page":x.value,"onUpdate:currentPage":l[0]||(l[0]=e=>x.value=e),"page-size":T.value,"onUpdate:pageSize":l[1]||(l[1]=e=>T.value=e),"page-sizes":[10,20,50,100],size:P.value,disabled:J.value,background:F.value,layout:"total, sizes, prev, pager, next, jumper",total:R.value,onSizeChange:A,onCurrentChange:Q},null,8,["current-page","page-size","size","disabled","background","total"])])]),_:1}),u(ne,{dialogVisible:r.value,"onUpdate:dialogVisible":l[2]||(l[2]=e=>r.value=e),isRightType:m.value,currentRow:V.value,closeOnClickModal:v.value,onCancelBtn:K},null,8,["dialogVisible","isRightType","currentRow","closeOnClickModal"])])}}}),xe=pe(ge,[["__scopeId","data-v-ac541e06"]]);export{xe as default};
