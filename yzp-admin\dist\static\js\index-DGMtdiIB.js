import{d as y,m as g,r as w,aB as u,g as s,c,b as m,e as l,w as n,F as C,h as b,f as d,t as V,V as k,N as x,L as B,aC as S}from"./index-VeYmKv4z.js";const N={class:"card-header"},F=y({name:"PermissionPage",__name:"index",setup(P){var o;const i=g(()=>({width:"85vw",justifyContent:"start"})),a=w((o=u())==null?void 0:o.username),p=[{value:"admin",label:"管理员角色"},{value:"common",label:"普通角色"}];function _(){u().loginByUsername({username:a.value,password:"admin123"}).then(t=>{t.success&&(x().removeItem("async-routes"),B().clearAllCachePage(),S())})}return(t,r)=>{const v=s("el-option"),f=s("el-select"),h=s("el-card");return m(),c("div",null,[l(h,{shadow:"never",style:k(i.value)},{header:n(()=>[d("div",N,[d("span",null,"当前角色："+V(a.value),1)])]),default:n(()=>[l(f,{modelValue:a.value,"onUpdate:modelValue":r[0]||(r[0]=e=>a.value=e),class:"w-[160px]!",onChange:_},{default:n(()=>[(m(),c(C,null,b(p,e=>l(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1},8,["style"])])}}});export{F as default};
