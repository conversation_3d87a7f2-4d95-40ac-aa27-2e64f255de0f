var N=(D,a,c)=>new Promise((h,v)=>{var w=i=>{try{b(c.next(i))}catch(m){v(m)}},k=i=>{try{b(c.throw(i))}catch(m){v(m)}},b=i=>i.done?h(i.value):Promise.resolve(i.value).then(w,k);b((c=c.apply(D,a)).next())});import le from"./index-CFUE8sOw.js";import{g as ne}from"./index-CpjYoR0F.js";import{f as oe}from"./dateFormat-BuOeynu9.js";import{d as re,r as l,o as se,a as ie,c as y,b as s,e as d,w as u,f as z,g as p,F as C,h as x,i as R,j as V,k as _,l as de,t as S,V as ue,_ as pe}from"./index-VeYmKv4z.js";const ce={class:"table-header-flex"},me={class:"form-btns"},ye={key:0},ve={key:2},be={style:{display:"flex","justify-content":"flex-end","margin-top":"20px"}},Y=420,ge=re({__name:"index",setup(D){const a=l({}),c=l(!1),h=l(""),v=l(!1);let w=[{type:"input",key:"name",label:"公司名称"},{type:"input",key:"enterpriseLegalPerson",label:"法人"},{type:"select",key:"status",label:"状态"},{type:"datetime",key:"dates",label:"提交时间"}];const k=[{property:"name",label:"公司名称",width:""},{property:"enterpriseLegalPerson",label:"法人",width:""},{property:"socialCreditCode",label:"社会信用代码",width:""},{property:"hrCardName",label:"招聘者",width:""},{property:"auditStatus",label:"状态",width:""},{property:"createTime",label:"提交时间",width:""}],b=l([]),i=l(1),m=l(10),j=l("default"),E=l(!1),I=l(!1),H=l(0),L=l({}),M=l([{label:"待审核",value:0},{label:"通过",value:1},{label:"驳回",value:2}]),o=l({entity:{companyHrSearchDTO:{endTime:"",startTime:"",status:"",enterpriseLegalPerson:"",name:""},type:3},orderBy:{},page:1,size:10}),F=n=>{m.value=n,o.value.size=n,o.value.page=1,g()},A=n=>{i.value=n,o.value.page=n,g()},U=l(window.innerHeight-Y);function O(){U.value=window.innerHeight-Y}function Q(n){L.value=n.row,c.value=!0}const $=()=>{h.value="",c.value=!1,g()},q=()=>{a.value.dates&&a.value.dates.length===2?(o.value.entity.companyHrSearchDTO.startTime=a.value.dates[0],o.value.entity.companyHrSearchDTO.endTime=a.value.dates[1]):(delete o.value.entity.companyHrSearchDTO.startTime,delete o.value.entity.companyHrSearchDTO.endTime),o.value.entity.companyHrSearchDTO.status=a.value.status,o.value.entity.companyHrSearchDTO.enterpriseLegalPerson=a.value.enterpriseLegalPerson,o.value.entity.companyHrSearchDTO.name=a.value.name,g()},G=()=>{a.value={},o.value={entity:{companyHrSearchDTO:{endTime:"",startTime:"",status:"",enterpriseLegalPerson:"",name:""},type:3},orderBy:{},page:1,size:10},g()},g=()=>N(null,null,function*(){v.value=!0;try{const n=yield ne(o.value);n.code===0&&(H.value=n.data.total,b.value=n.data.list)}catch(n){}finally{v.value=!1}});return se(()=>{g(),window.addEventListener("resize",O)}),ie(()=>{window.removeEventListener("resize",O)}),(n,r)=>{const J=p("el-input"),K=p("el-option"),W=p("el-select"),X=p("el-date-picker"),Z=p("el-form-item"),ee=p("el-form"),T=p("el-button"),B=p("el-card"),P=p("el-table-column"),ae=p("el-table"),te=p("el-pagination");return s(),y("div",null,[d(B,{shadow:"never"},{default:u(()=>[z("div",ce,[d(ee,{inline:!0,model:a.value,class:"table-header-form"},{default:u(()=>[(s(!0),y(C,null,x(R(w),(e,f)=>(s(),_(Z,{key:f,label:e.label,class:"form-item"},{default:u(()=>[e.type==="input"?(s(),_(J,{key:0,modelValue:a.value[e.key],"onUpdate:modelValue":t=>a.value[e.key]=t,size:"large",placeholder:"请输入"+e.label,style:{width:"180px"},clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="select"?(s(),_(W,{key:1,modelValue:a.value[e.key],"onUpdate:modelValue":t=>a.value[e.key]=t,size:"large",placeholder:"请选择"+e.label,style:{width:"180px"},clearable:""},{default:u(()=>[(s(!0),y(C,null,x(M.value,t=>(s(),_(K,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="datetime"?(s(),_(X,{key:2,modelValue:a.value[e.key],"onUpdate:modelValue":t=>a.value[e.key]=t,type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",size:"large",style:{width:"380px"},"value-format":"YYYY-MM-DD HH:mm:ss",clearable:""},null,8,["modelValue","onUpdate:modelValue"])):de("",!0)]),_:2},1032,["label"]))),128))]),_:1},8,["model"]),z("div",me,[d(T,{size:"large",type:"primary",onClick:q},{default:u(()=>r[3]||(r[3]=[V("搜索")])),_:1}),d(T,{size:"large",type:"info",style:{"background-color":"#b4c4d1",color:"#ffffff",border:"#b4c4d1"},onClick:G},{default:u(()=>r[4]||(r[4]=[V("重置")])),_:1})])])]),_:1}),d(B,{shadow:"never",style:{"margin-top":"15px",padding:"5px 15px"}},{default:u(()=>[d(ae,{ref:"tableContainer",data:b.value,loading:v.value,"element-loading-text":"数据加载中...","element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(255, 255, 255, 0.9)","element-loading-svg-view-box":"-10, -10, 50, 50",style:{width:"100%"},height:U.value},{default:u(()=>[(s(),y(C,null,x(k,(e,f)=>d(P,{key:f,width:e.width,label:e.label,"show-overflow-tooltip":""},{default:u(t=>[e.property==="createTime"?(s(),y("span",ye,S(R(oe)(t.row[e.property])),1)):e.property==="auditStatus"?(s(),y("span",{key:1,style:ue({color:t.row[e.property]===0?"":t.row[e.property]===1?"#67C23A":"#fb5451"})},S(t.row[e.property]===0?"待审核":t.row[e.property]===1?"通过":"驳回"),5)):(s(),y("span",ve,S(t.row[e.property]),1))]),_:2},1032,["width","label"])),64)),d(P,{fixed:"right",label:"操作","min-width":"120"},{default:u(e=>[d(T,{link:"",type:"primary",style:{color:"#279efb"},onClick:f=>Q(e)},{default:u(()=>r[5]||(r[5]=[V(" 详情 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","loading","height"]),z("div",be,[d(te,{"current-page":i.value,"onUpdate:currentPage":r[0]||(r[0]=e=>i.value=e),"page-size":m.value,"onUpdate:pageSize":r[1]||(r[1]=e=>m.value=e),"page-sizes":[10,20,50,100],size:j.value,disabled:I.value,background:E.value,layout:"total, sizes, prev, pager, next, jumper",total:H.value,onSizeChange:F,onCurrentChange:A},null,8,["current-page","page-size","size","disabled","background","total"])])]),_:1}),d(le,{dialogVisible:c.value,"onUpdate:dialogVisible":r[2]||(r[2]=e=>c.value=e),currentRow:L.value,onCancelBtn:$},null,8,["dialogVisible","currentRow"])])}}}),Te=pe(ge,[["__scopeId","data-v-da9278b1"]]);export{Te as default};
