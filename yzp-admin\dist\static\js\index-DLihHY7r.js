var H=(B,l,p)=>new Promise((h,g)=>{var w=s=>{try{v(p.next(s))}catch(m){g(m)}},k=s=>{try{v(p.throw(s))}catch(m){g(m)}},v=s=>s.done?h(s.value):Promise.resolve(s.value).then(w,k);v((p=p.apply(B,l)).next())});import te from"./index-D95zPymQ.js";import{g as ne}from"./index-CpjYoR0F.js";import{f as oe}from"./dateFormat-BuOeynu9.js";import{d as se,r as a,o as re,a as ie,c as b,b as i,e as d,w as u,f as T,g as c,F as x,h as C,i as S,j as V,k as f,l as de,t as R,_ as ue}from"./index-VeYmKv4z.js";const ce={class:"table-header-flex"},pe={class:"form-btns"},me={key:0},ge={key:1},ve={style:{display:"flex","justify-content":"flex-end","margin-top":"20px"}},I=420,ye=se({__name:"index",setup(B){const l=a({}),p=a(!1),h=a(""),g=a(!1);let w=[{type:"input",key:"name",label:"公司名称"},{type:"input",key:"legalPerson",label:"法人"},{type:"datetime",key:"dates",label:"提交时间"}];const k=[{property:"companyName",label:"公司名称",width:""},{property:"legalPerson",label:"法人",width:""},{property:"creditCode",label:"社会信用代码",width:""},{property:"createTime",label:"提交时间",width:""}],v=a([]),s=a(1),m=a(10),Y=a("default"),j=a(!1),E=a(!1),D=a(0),L=a({}),M=a([{label:"待审核",value:0},{label:"通过",value:1},{label:"驳回",value:2}]),r=a({entity:{recognizeBusinessLicenseDTO:{endTime:"",startTime:"",legalPerson:"",companyName:""},type:4},orderBy:{},page:1,size:10}),F=t=>{m.value=t,r.value.size=t,r.value.page=1,y()},Q=t=>{s.value=t,r.value.page=t,y()},U=a(window.innerHeight-I);function N(){U.value=window.innerHeight-I}function $(t){L.value=t.row,p.value=!0}const q=()=>{h.value="",p.value=!1,y()},A=()=>{l.value.dates&&l.value.dates.length===2?(r.value.entity.recognizeBusinessLicenseDTO.startTime=l.value.dates[0],r.value.entity.recognizeBusinessLicenseDTO.endTime=l.value.dates[1]):(delete r.value.entity.recognizeBusinessLicenseDTO.startTime,delete r.value.entity.recognizeBusinessLicenseDTO.endTime),l.value.manualInspectionStatus,r.value.entity.recognizeBusinessLicenseDTO.legalPerson=l.value.legalPerson,r.value.entity.recognizeBusinessLicenseDTO.companyName=l.value.companyName,y()},G=()=>{l.value={},r.value={entity:{recognizeBusinessLicenseDTO:{endTime:"",startTime:"",legalPerson:"",companyName:""},type:4},orderBy:{},page:1,size:10},y()},y=()=>H(null,null,function*(){g.value=!0;try{const t=yield ne(r.value);t.code===0&&(D.value=t.data.total,v.value=t.data.list)}catch(t){}finally{g.value=!1}});return re(()=>{y(),window.addEventListener("resize",N)}),ie(()=>{window.removeEventListener("resize",N)}),(t,n)=>{const J=c("el-input"),K=c("el-option"),W=c("el-select"),X=c("el-date-picker"),Z=c("el-form-item"),ee=c("el-form"),z=c("el-button"),O=c("el-card"),P=c("el-table-column"),le=c("el-table"),ae=c("el-pagination");return i(),b("div",null,[d(O,{shadow:"never"},{default:u(()=>[T("div",ce,[d(ee,{inline:!0,model:l.value,class:"table-header-form"},{default:u(()=>[(i(!0),b(x,null,C(S(w),(e,_)=>(i(),f(Z,{key:_,label:e.label,class:"form-item"},{default:u(()=>[e.type==="input"?(i(),f(J,{key:0,modelValue:l.value[e.key],"onUpdate:modelValue":o=>l.value[e.key]=o,size:"large",placeholder:"请输入"+e.label,style:{width:"180px"},clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="select"?(i(),f(W,{key:1,modelValue:l.value[e.key],"onUpdate:modelValue":o=>l.value[e.key]=o,size:"large",placeholder:"请选择"+e.label,style:{width:"180px"},clearable:""},{default:u(()=>[(i(!0),b(x,null,C(M.value,o=>(i(),f(K,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="datetime"?(i(),f(X,{key:2,modelValue:l.value[e.key],"onUpdate:modelValue":o=>l.value[e.key]=o,type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",size:"large",style:{width:"380px"},"value-format":"YYYY-MM-DD HH:mm:ss",clearable:""},null,8,["modelValue","onUpdate:modelValue"])):de("",!0)]),_:2},1032,["label"]))),128))]),_:1},8,["model"]),T("div",pe,[d(z,{size:"large",type:"primary",onClick:A},{default:u(()=>n[3]||(n[3]=[V("搜索")])),_:1}),d(z,{size:"large",type:"info",style:{"background-color":"#b4c4d1",color:"#ffffff",border:"#b4c4d1"},onClick:G},{default:u(()=>n[4]||(n[4]=[V("重置")])),_:1})])])]),_:1}),d(O,{shadow:"never",style:{"margin-top":"15px",padding:"5px 15px"}},{default:u(()=>[d(le,{ref:"tableContainer",data:v.value,loading:g.value,"element-loading-text":"数据加载中...","element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(255, 255, 255, 0.9)","element-loading-svg-view-box":"-10, -10, 50, 50",style:{width:"100%"},height:U.value},{default:u(()=>[(i(),b(x,null,C(k,(e,_)=>d(P,{key:_,width:e.width,label:e.label,"show-overflow-tooltip":""},{default:u(o=>[e.property==="createTime"?(i(),b("span",me,R(S(oe)(o.row[e.property])),1)):(i(),b("span",ge,R(o.row[e.property]),1))]),_:2},1032,["width","label"])),64)),d(P,{fixed:"right",label:"操作","min-width":"120"},{default:u(e=>[d(z,{link:"",type:"primary",style:{color:"#279efb"},onClick:_=>$(e)},{default:u(()=>n[5]||(n[5]=[V(" 详情 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","loading","height"]),T("div",ve,[d(ae,{"current-page":s.value,"onUpdate:currentPage":n[0]||(n[0]=e=>s.value=e),"page-size":m.value,"onUpdate:pageSize":n[1]||(n[1]=e=>m.value=e),"page-sizes":[10,20,50,100],size:Y.value,disabled:E.value,background:j.value,layout:"total, sizes, prev, pager, next, jumper",total:D.value,onSizeChange:F,onCurrentChange:Q},null,8,["current-page","page-size","size","disabled","background","total"])])]),_:1}),d(te,{dialogVisible:p.value,"onUpdate:dialogVisible":n[2]||(n[2]=e=>p.value=e),currentRow:L.value,onCancelBtn:q},null,8,["dialogVisible","currentRow"])])}}}),ke=ue(ye,[["__scopeId","data-v-d13b1e12"]]);export{ke as default};
