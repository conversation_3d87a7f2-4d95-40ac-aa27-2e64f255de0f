{"version": 3, "sources": ["../../.pnpm/jsbn@1.1.0/node_modules/jsbn/index.js", "../../.pnpm/sm-crypto@0.3.13/node_modules/sm-crypto/src/sm2/asn1.js", "../../.pnpm/sm-crypto@0.3.13/node_modules/sm-crypto/src/sm2/ec.js", "../../.pnpm/sm-crypto@0.3.13/node_modules/sm-crypto/src/sm2/utils.js", "../../.pnpm/sm-crypto@0.3.13/node_modules/sm-crypto/src/sm2/sm3.js", "../../.pnpm/sm-crypto@0.3.13/node_modules/sm-crypto/src/sm2/index.js", "../../.pnpm/sm-crypto@0.3.13/node_modules/sm-crypto/src/sm3/index.js", "../../.pnpm/sm-crypto@0.3.13/node_modules/sm-crypto/src/sm4/index.js", "../../.pnpm/sm-crypto@0.3.13/node_modules/sm-crypto/src/index.js"], "sourcesContent": ["(function(){\n\n    // Copyright (c) 2005  Tom Wu\n    // All Rights Reserved.\n    // See \"LICENSE\" for details.\n\n    // Basic JavaScript BN library - subset useful for RSA encryption.\n\n    // Bits per digit\n    var dbits;\n\n    // JavaScript engine analysis\n    var canary = 0xdeadbeefcafe;\n    var j_lm = ((canary&0xffffff)==0xefcafe);\n\n    // (public) Constructor\n    function BigInteger(a,b,c) {\n      if(a != null)\n        if(\"number\" == typeof a) this.fromNumber(a,b,c);\n        else if(b == null && \"string\" != typeof a) this.fromString(a,256);\n        else this.fromString(a,b);\n    }\n\n    // return new, unset BigInteger\n    function nbi() { return new BigInteger(null); }\n\n    // am: Compute w_j += (x*this_i), propagate carries,\n    // c is initial carry, returns final carry.\n    // c < 3*dvalue, x < 2*dvalue, this_i < dvalue\n    // We need to select the fastest one that works in this environment.\n\n    // am1: use a single mult and divide to get the high bits,\n    // max digit bits should be 26 because\n    // max internal value = 2*dvalue^2-2*dvalue (< 2^53)\n    function am1(i,x,w,j,c,n) {\n      while(--n >= 0) {\n        var v = x*this[i++]+w[j]+c;\n        c = Math.floor(v/0x4000000);\n        w[j++] = v&0x3ffffff;\n      }\n      return c;\n    }\n    // am2 avoids a big mult-and-extract completely.\n    // Max digit bits should be <= 30 because we do bitwise ops\n    // on values up to 2*hdvalue^2-hdvalue-1 (< 2^31)\n    function am2(i,x,w,j,c,n) {\n      var xl = x&0x7fff, xh = x>>15;\n      while(--n >= 0) {\n        var l = this[i]&0x7fff;\n        var h = this[i++]>>15;\n        var m = xh*l+h*xl;\n        l = xl*l+((m&0x7fff)<<15)+w[j]+(c&0x3fffffff);\n        c = (l>>>30)+(m>>>15)+xh*h+(c>>>30);\n        w[j++] = l&0x3fffffff;\n      }\n      return c;\n    }\n    // Alternately, set max digit bits to 28 since some\n    // browsers slow down when dealing with 32-bit numbers.\n    function am3(i,x,w,j,c,n) {\n      var xl = x&0x3fff, xh = x>>14;\n      while(--n >= 0) {\n        var l = this[i]&0x3fff;\n        var h = this[i++]>>14;\n        var m = xh*l+h*xl;\n        l = xl*l+((m&0x3fff)<<14)+w[j]+c;\n        c = (l>>28)+(m>>14)+xh*h;\n        w[j++] = l&0xfffffff;\n      }\n      return c;\n    }\n    var inBrowser = typeof navigator !== \"undefined\";\n    if(inBrowser && j_lm && (navigator.appName == \"Microsoft Internet Explorer\")) {\n      BigInteger.prototype.am = am2;\n      dbits = 30;\n    }\n    else if(inBrowser && j_lm && (navigator.appName != \"Netscape\")) {\n      BigInteger.prototype.am = am1;\n      dbits = 26;\n    }\n    else { // Mozilla/Netscape seems to prefer am3\n      BigInteger.prototype.am = am3;\n      dbits = 28;\n    }\n\n    BigInteger.prototype.DB = dbits;\n    BigInteger.prototype.DM = ((1<<dbits)-1);\n    BigInteger.prototype.DV = (1<<dbits);\n\n    var BI_FP = 52;\n    BigInteger.prototype.FV = Math.pow(2,BI_FP);\n    BigInteger.prototype.F1 = BI_FP-dbits;\n    BigInteger.prototype.F2 = 2*dbits-BI_FP;\n\n    // Digit conversions\n    var BI_RM = \"0123456789abcdefghijklmnopqrstuvwxyz\";\n    var BI_RC = new Array();\n    var rr,vv;\n    rr = \"0\".charCodeAt(0);\n    for(vv = 0; vv <= 9; ++vv) BI_RC[rr++] = vv;\n    rr = \"a\".charCodeAt(0);\n    for(vv = 10; vv < 36; ++vv) BI_RC[rr++] = vv;\n    rr = \"A\".charCodeAt(0);\n    for(vv = 10; vv < 36; ++vv) BI_RC[rr++] = vv;\n\n    function int2char(n) { return BI_RM.charAt(n); }\n    function intAt(s,i) {\n      var c = BI_RC[s.charCodeAt(i)];\n      return (c==null)?-1:c;\n    }\n\n    // (protected) copy this to r\n    function bnpCopyTo(r) {\n      for(var i = this.t-1; i >= 0; --i) r[i] = this[i];\n      r.t = this.t;\n      r.s = this.s;\n    }\n\n    // (protected) set from integer value x, -DV <= x < DV\n    function bnpFromInt(x) {\n      this.t = 1;\n      this.s = (x<0)?-1:0;\n      if(x > 0) this[0] = x;\n      else if(x < -1) this[0] = x+this.DV;\n      else this.t = 0;\n    }\n\n    // return bigint initialized to value\n    function nbv(i) { var r = nbi(); r.fromInt(i); return r; }\n\n    // (protected) set from string and radix\n    function bnpFromString(s,b) {\n      var k;\n      if(b == 16) k = 4;\n      else if(b == 8) k = 3;\n      else if(b == 256) k = 8; // byte array\n      else if(b == 2) k = 1;\n      else if(b == 32) k = 5;\n      else if(b == 4) k = 2;\n      else { this.fromRadix(s,b); return; }\n      this.t = 0;\n      this.s = 0;\n      var i = s.length, mi = false, sh = 0;\n      while(--i >= 0) {\n        var x = (k==8)?s[i]&0xff:intAt(s,i);\n        if(x < 0) {\n          if(s.charAt(i) == \"-\") mi = true;\n          continue;\n        }\n        mi = false;\n        if(sh == 0)\n          this[this.t++] = x;\n        else if(sh+k > this.DB) {\n          this[this.t-1] |= (x&((1<<(this.DB-sh))-1))<<sh;\n          this[this.t++] = (x>>(this.DB-sh));\n        }\n        else\n          this[this.t-1] |= x<<sh;\n        sh += k;\n        if(sh >= this.DB) sh -= this.DB;\n      }\n      if(k == 8 && (s[0]&0x80) != 0) {\n        this.s = -1;\n        if(sh > 0) this[this.t-1] |= ((1<<(this.DB-sh))-1)<<sh;\n      }\n      this.clamp();\n      if(mi) BigInteger.ZERO.subTo(this,this);\n    }\n\n    // (protected) clamp off excess high words\n    function bnpClamp() {\n      var c = this.s&this.DM;\n      while(this.t > 0 && this[this.t-1] == c) --this.t;\n    }\n\n    // (public) return string representation in given radix\n    function bnToString(b) {\n      if(this.s < 0) return \"-\"+this.negate().toString(b);\n      var k;\n      if(b == 16) k = 4;\n      else if(b == 8) k = 3;\n      else if(b == 2) k = 1;\n      else if(b == 32) k = 5;\n      else if(b == 4) k = 2;\n      else return this.toRadix(b);\n      var km = (1<<k)-1, d, m = false, r = \"\", i = this.t;\n      var p = this.DB-(i*this.DB)%k;\n      if(i-- > 0) {\n        if(p < this.DB && (d = this[i]>>p) > 0) { m = true; r = int2char(d); }\n        while(i >= 0) {\n          if(p < k) {\n            d = (this[i]&((1<<p)-1))<<(k-p);\n            d |= this[--i]>>(p+=this.DB-k);\n          }\n          else {\n            d = (this[i]>>(p-=k))&km;\n            if(p <= 0) { p += this.DB; --i; }\n          }\n          if(d > 0) m = true;\n          if(m) r += int2char(d);\n        }\n      }\n      return m?r:\"0\";\n    }\n\n    // (public) -this\n    function bnNegate() { var r = nbi(); BigInteger.ZERO.subTo(this,r); return r; }\n\n    // (public) |this|\n    function bnAbs() { return (this.s<0)?this.negate():this; }\n\n    // (public) return + if this > a, - if this < a, 0 if equal\n    function bnCompareTo(a) {\n      var r = this.s-a.s;\n      if(r != 0) return r;\n      var i = this.t;\n      r = i-a.t;\n      if(r != 0) return (this.s<0)?-r:r;\n      while(--i >= 0) if((r=this[i]-a[i]) != 0) return r;\n      return 0;\n    }\n\n    // returns bit length of the integer x\n    function nbits(x) {\n      var r = 1, t;\n      if((t=x>>>16) != 0) { x = t; r += 16; }\n      if((t=x>>8) != 0) { x = t; r += 8; }\n      if((t=x>>4) != 0) { x = t; r += 4; }\n      if((t=x>>2) != 0) { x = t; r += 2; }\n      if((t=x>>1) != 0) { x = t; r += 1; }\n      return r;\n    }\n\n    // (public) return the number of bits in \"this\"\n    function bnBitLength() {\n      if(this.t <= 0) return 0;\n      return this.DB*(this.t-1)+nbits(this[this.t-1]^(this.s&this.DM));\n    }\n\n    // (protected) r = this << n*DB\n    function bnpDLShiftTo(n,r) {\n      var i;\n      for(i = this.t-1; i >= 0; --i) r[i+n] = this[i];\n      for(i = n-1; i >= 0; --i) r[i] = 0;\n      r.t = this.t+n;\n      r.s = this.s;\n    }\n\n    // (protected) r = this >> n*DB\n    function bnpDRShiftTo(n,r) {\n      for(var i = n; i < this.t; ++i) r[i-n] = this[i];\n      r.t = Math.max(this.t-n,0);\n      r.s = this.s;\n    }\n\n    // (protected) r = this << n\n    function bnpLShiftTo(n,r) {\n      var bs = n%this.DB;\n      var cbs = this.DB-bs;\n      var bm = (1<<cbs)-1;\n      var ds = Math.floor(n/this.DB), c = (this.s<<bs)&this.DM, i;\n      for(i = this.t-1; i >= 0; --i) {\n        r[i+ds+1] = (this[i]>>cbs)|c;\n        c = (this[i]&bm)<<bs;\n      }\n      for(i = ds-1; i >= 0; --i) r[i] = 0;\n      r[ds] = c;\n      r.t = this.t+ds+1;\n      r.s = this.s;\n      r.clamp();\n    }\n\n    // (protected) r = this >> n\n    function bnpRShiftTo(n,r) {\n      r.s = this.s;\n      var ds = Math.floor(n/this.DB);\n      if(ds >= this.t) { r.t = 0; return; }\n      var bs = n%this.DB;\n      var cbs = this.DB-bs;\n      var bm = (1<<bs)-1;\n      r[0] = this[ds]>>bs;\n      for(var i = ds+1; i < this.t; ++i) {\n        r[i-ds-1] |= (this[i]&bm)<<cbs;\n        r[i-ds] = this[i]>>bs;\n      }\n      if(bs > 0) r[this.t-ds-1] |= (this.s&bm)<<cbs;\n      r.t = this.t-ds;\n      r.clamp();\n    }\n\n    // (protected) r = this - a\n    function bnpSubTo(a,r) {\n      var i = 0, c = 0, m = Math.min(a.t,this.t);\n      while(i < m) {\n        c += this[i]-a[i];\n        r[i++] = c&this.DM;\n        c >>= this.DB;\n      }\n      if(a.t < this.t) {\n        c -= a.s;\n        while(i < this.t) {\n          c += this[i];\n          r[i++] = c&this.DM;\n          c >>= this.DB;\n        }\n        c += this.s;\n      }\n      else {\n        c += this.s;\n        while(i < a.t) {\n          c -= a[i];\n          r[i++] = c&this.DM;\n          c >>= this.DB;\n        }\n        c -= a.s;\n      }\n      r.s = (c<0)?-1:0;\n      if(c < -1) r[i++] = this.DV+c;\n      else if(c > 0) r[i++] = c;\n      r.t = i;\n      r.clamp();\n    }\n\n    // (protected) r = this * a, r != this,a (HAC 14.12)\n    // \"this\" should be the larger one if appropriate.\n    function bnpMultiplyTo(a,r) {\n      var x = this.abs(), y = a.abs();\n      var i = x.t;\n      r.t = i+y.t;\n      while(--i >= 0) r[i] = 0;\n      for(i = 0; i < y.t; ++i) r[i+x.t] = x.am(0,y[i],r,i,0,x.t);\n      r.s = 0;\n      r.clamp();\n      if(this.s != a.s) BigInteger.ZERO.subTo(r,r);\n    }\n\n    // (protected) r = this^2, r != this (HAC 14.16)\n    function bnpSquareTo(r) {\n      var x = this.abs();\n      var i = r.t = 2*x.t;\n      while(--i >= 0) r[i] = 0;\n      for(i = 0; i < x.t-1; ++i) {\n        var c = x.am(i,x[i],r,2*i,0,1);\n        if((r[i+x.t]+=x.am(i+1,2*x[i],r,2*i+1,c,x.t-i-1)) >= x.DV) {\n          r[i+x.t] -= x.DV;\n          r[i+x.t+1] = 1;\n        }\n      }\n      if(r.t > 0) r[r.t-1] += x.am(i,x[i],r,2*i,0,1);\n      r.s = 0;\n      r.clamp();\n    }\n\n    // (protected) divide this by m, quotient and remainder to q, r (HAC 14.20)\n    // r != q, this != m.  q or r may be null.\n    function bnpDivRemTo(m,q,r) {\n      var pm = m.abs();\n      if(pm.t <= 0) return;\n      var pt = this.abs();\n      if(pt.t < pm.t) {\n        if(q != null) q.fromInt(0);\n        if(r != null) this.copyTo(r);\n        return;\n      }\n      if(r == null) r = nbi();\n      var y = nbi(), ts = this.s, ms = m.s;\n      var nsh = this.DB-nbits(pm[pm.t-1]);   // normalize modulus\n      if(nsh > 0) { pm.lShiftTo(nsh,y); pt.lShiftTo(nsh,r); }\n      else { pm.copyTo(y); pt.copyTo(r); }\n      var ys = y.t;\n      var y0 = y[ys-1];\n      if(y0 == 0) return;\n      var yt = y0*(1<<this.F1)+((ys>1)?y[ys-2]>>this.F2:0);\n      var d1 = this.FV/yt, d2 = (1<<this.F1)/yt, e = 1<<this.F2;\n      var i = r.t, j = i-ys, t = (q==null)?nbi():q;\n      y.dlShiftTo(j,t);\n      if(r.compareTo(t) >= 0) {\n        r[r.t++] = 1;\n        r.subTo(t,r);\n      }\n      BigInteger.ONE.dlShiftTo(ys,t);\n      t.subTo(y,y);  // \"negative\" y so we can replace sub with am later\n      while(y.t < ys) y[y.t++] = 0;\n      while(--j >= 0) {\n        // Estimate quotient digit\n        var qd = (r[--i]==y0)?this.DM:Math.floor(r[i]*d1+(r[i-1]+e)*d2);\n        if((r[i]+=y.am(0,qd,r,j,0,ys)) < qd) {   // Try it out\n          y.dlShiftTo(j,t);\n          r.subTo(t,r);\n          while(r[i] < --qd) r.subTo(t,r);\n        }\n      }\n      if(q != null) {\n        r.drShiftTo(ys,q);\n        if(ts != ms) BigInteger.ZERO.subTo(q,q);\n      }\n      r.t = ys;\n      r.clamp();\n      if(nsh > 0) r.rShiftTo(nsh,r); // Denormalize remainder\n      if(ts < 0) BigInteger.ZERO.subTo(r,r);\n    }\n\n    // (public) this mod a\n    function bnMod(a) {\n      var r = nbi();\n      this.abs().divRemTo(a,null,r);\n      if(this.s < 0 && r.compareTo(BigInteger.ZERO) > 0) a.subTo(r,r);\n      return r;\n    }\n\n    // Modular reduction using \"classic\" algorithm\n    function Classic(m) { this.m = m; }\n    function cConvert(x) {\n      if(x.s < 0 || x.compareTo(this.m) >= 0) return x.mod(this.m);\n      else return x;\n    }\n    function cRevert(x) { return x; }\n    function cReduce(x) { x.divRemTo(this.m,null,x); }\n    function cMulTo(x,y,r) { x.multiplyTo(y,r); this.reduce(r); }\n    function cSqrTo(x,r) { x.squareTo(r); this.reduce(r); }\n\n    Classic.prototype.convert = cConvert;\n    Classic.prototype.revert = cRevert;\n    Classic.prototype.reduce = cReduce;\n    Classic.prototype.mulTo = cMulTo;\n    Classic.prototype.sqrTo = cSqrTo;\n\n    // (protected) return \"-1/this % 2^DB\"; useful for Mont. reduction\n    // justification:\n    //         xy == 1 (mod m)\n    //         xy =  1+km\n    //   xy(2-xy) = (1+km)(1-km)\n    // x[y(2-xy)] = 1-k^2m^2\n    // x[y(2-xy)] == 1 (mod m^2)\n    // if y is 1/x mod m, then y(2-xy) is 1/x mod m^2\n    // should reduce x and y(2-xy) by m^2 at each step to keep size bounded.\n    // JS multiply \"overflows\" differently from C/C++, so care is needed here.\n    function bnpInvDigit() {\n      if(this.t < 1) return 0;\n      var x = this[0];\n      if((x&1) == 0) return 0;\n      var y = x&3;       // y == 1/x mod 2^2\n      y = (y*(2-(x&0xf)*y))&0xf; // y == 1/x mod 2^4\n      y = (y*(2-(x&0xff)*y))&0xff;   // y == 1/x mod 2^8\n      y = (y*(2-(((x&0xffff)*y)&0xffff)))&0xffff;    // y == 1/x mod 2^16\n      // last step - calculate inverse mod DV directly;\n      // assumes 16 < DB <= 32 and assumes ability to handle 48-bit ints\n      y = (y*(2-x*y%this.DV))%this.DV;       // y == 1/x mod 2^dbits\n      // we really want the negative inverse, and -DV < y < DV\n      return (y>0)?this.DV-y:-y;\n    }\n\n    // Montgomery reduction\n    function Montgomery(m) {\n      this.m = m;\n      this.mp = m.invDigit();\n      this.mpl = this.mp&0x7fff;\n      this.mph = this.mp>>15;\n      this.um = (1<<(m.DB-15))-1;\n      this.mt2 = 2*m.t;\n    }\n\n    // xR mod m\n    function montConvert(x) {\n      var r = nbi();\n      x.abs().dlShiftTo(this.m.t,r);\n      r.divRemTo(this.m,null,r);\n      if(x.s < 0 && r.compareTo(BigInteger.ZERO) > 0) this.m.subTo(r,r);\n      return r;\n    }\n\n    // x/R mod m\n    function montRevert(x) {\n      var r = nbi();\n      x.copyTo(r);\n      this.reduce(r);\n      return r;\n    }\n\n    // x = x/R mod m (HAC 14.32)\n    function montReduce(x) {\n      while(x.t <= this.mt2) // pad x so am has enough room later\n        x[x.t++] = 0;\n      for(var i = 0; i < this.m.t; ++i) {\n        // faster way of calculating u0 = x[i]*mp mod DV\n        var j = x[i]&0x7fff;\n        var u0 = (j*this.mpl+(((j*this.mph+(x[i]>>15)*this.mpl)&this.um)<<15))&x.DM;\n        // use am to combine the multiply-shift-add into one call\n        j = i+this.m.t;\n        x[j] += this.m.am(0,u0,x,i,0,this.m.t);\n        // propagate carry\n        while(x[j] >= x.DV) { x[j] -= x.DV; x[++j]++; }\n      }\n      x.clamp();\n      x.drShiftTo(this.m.t,x);\n      if(x.compareTo(this.m) >= 0) x.subTo(this.m,x);\n    }\n\n    // r = \"x^2/R mod m\"; x != r\n    function montSqrTo(x,r) { x.squareTo(r); this.reduce(r); }\n\n    // r = \"xy/R mod m\"; x,y != r\n    function montMulTo(x,y,r) { x.multiplyTo(y,r); this.reduce(r); }\n\n    Montgomery.prototype.convert = montConvert;\n    Montgomery.prototype.revert = montRevert;\n    Montgomery.prototype.reduce = montReduce;\n    Montgomery.prototype.mulTo = montMulTo;\n    Montgomery.prototype.sqrTo = montSqrTo;\n\n    // (protected) true iff this is even\n    function bnpIsEven() { return ((this.t>0)?(this[0]&1):this.s) == 0; }\n\n    // (protected) this^e, e < 2^32, doing sqr and mul with \"r\" (HAC 14.79)\n    function bnpExp(e,z) {\n      if(e > 0xffffffff || e < 1) return BigInteger.ONE;\n      var r = nbi(), r2 = nbi(), g = z.convert(this), i = nbits(e)-1;\n      g.copyTo(r);\n      while(--i >= 0) {\n        z.sqrTo(r,r2);\n        if((e&(1<<i)) > 0) z.mulTo(r2,g,r);\n        else { var t = r; r = r2; r2 = t; }\n      }\n      return z.revert(r);\n    }\n\n    // (public) this^e % m, 0 <= e < 2^32\n    function bnModPowInt(e,m) {\n      var z;\n      if(e < 256 || m.isEven()) z = new Classic(m); else z = new Montgomery(m);\n      return this.exp(e,z);\n    }\n\n    // protected\n    BigInteger.prototype.copyTo = bnpCopyTo;\n    BigInteger.prototype.fromInt = bnpFromInt;\n    BigInteger.prototype.fromString = bnpFromString;\n    BigInteger.prototype.clamp = bnpClamp;\n    BigInteger.prototype.dlShiftTo = bnpDLShiftTo;\n    BigInteger.prototype.drShiftTo = bnpDRShiftTo;\n    BigInteger.prototype.lShiftTo = bnpLShiftTo;\n    BigInteger.prototype.rShiftTo = bnpRShiftTo;\n    BigInteger.prototype.subTo = bnpSubTo;\n    BigInteger.prototype.multiplyTo = bnpMultiplyTo;\n    BigInteger.prototype.squareTo = bnpSquareTo;\n    BigInteger.prototype.divRemTo = bnpDivRemTo;\n    BigInteger.prototype.invDigit = bnpInvDigit;\n    BigInteger.prototype.isEven = bnpIsEven;\n    BigInteger.prototype.exp = bnpExp;\n\n    // public\n    BigInteger.prototype.toString = bnToString;\n    BigInteger.prototype.negate = bnNegate;\n    BigInteger.prototype.abs = bnAbs;\n    BigInteger.prototype.compareTo = bnCompareTo;\n    BigInteger.prototype.bitLength = bnBitLength;\n    BigInteger.prototype.mod = bnMod;\n    BigInteger.prototype.modPowInt = bnModPowInt;\n\n    // \"constants\"\n    BigInteger.ZERO = nbv(0);\n    BigInteger.ONE = nbv(1);\n\n    // Copyright (c) 2005-2009  Tom Wu\n    // All Rights Reserved.\n    // See \"LICENSE\" for details.\n\n    // Extended JavaScript BN functions, required for RSA private ops.\n\n    // Version 1.1: new BigInteger(\"0\", 10) returns \"proper\" zero\n    // Version 1.2: square() API, isProbablePrime fix\n\n    // (public)\n    function bnClone() { var r = nbi(); this.copyTo(r); return r; }\n\n    // (public) return value as integer\n    function bnIntValue() {\n      if(this.s < 0) {\n        if(this.t == 1) return this[0]-this.DV;\n        else if(this.t == 0) return -1;\n      }\n      else if(this.t == 1) return this[0];\n      else if(this.t == 0) return 0;\n      // assumes 16 < DB < 32\n      return ((this[1]&((1<<(32-this.DB))-1))<<this.DB)|this[0];\n    }\n\n    // (public) return value as byte\n    function bnByteValue() { return (this.t==0)?this.s:(this[0]<<24)>>24; }\n\n    // (public) return value as short (assumes DB>=16)\n    function bnShortValue() { return (this.t==0)?this.s:(this[0]<<16)>>16; }\n\n    // (protected) return x s.t. r^x < DV\n    function bnpChunkSize(r) { return Math.floor(Math.LN2*this.DB/Math.log(r)); }\n\n    // (public) 0 if this == 0, 1 if this > 0\n    function bnSigNum() {\n      if(this.s < 0) return -1;\n      else if(this.t <= 0 || (this.t == 1 && this[0] <= 0)) return 0;\n      else return 1;\n    }\n\n    // (protected) convert to radix string\n    function bnpToRadix(b) {\n      if(b == null) b = 10;\n      if(this.signum() == 0 || b < 2 || b > 36) return \"0\";\n      var cs = this.chunkSize(b);\n      var a = Math.pow(b,cs);\n      var d = nbv(a), y = nbi(), z = nbi(), r = \"\";\n      this.divRemTo(d,y,z);\n      while(y.signum() > 0) {\n        r = (a+z.intValue()).toString(b).substr(1) + r;\n        y.divRemTo(d,y,z);\n      }\n      return z.intValue().toString(b) + r;\n    }\n\n    // (protected) convert from radix string\n    function bnpFromRadix(s,b) {\n      this.fromInt(0);\n      if(b == null) b = 10;\n      var cs = this.chunkSize(b);\n      var d = Math.pow(b,cs), mi = false, j = 0, w = 0;\n      for(var i = 0; i < s.length; ++i) {\n        var x = intAt(s,i);\n        if(x < 0) {\n          if(s.charAt(i) == \"-\" && this.signum() == 0) mi = true;\n          continue;\n        }\n        w = b*w+x;\n        if(++j >= cs) {\n          this.dMultiply(d);\n          this.dAddOffset(w,0);\n          j = 0;\n          w = 0;\n        }\n      }\n      if(j > 0) {\n        this.dMultiply(Math.pow(b,j));\n        this.dAddOffset(w,0);\n      }\n      if(mi) BigInteger.ZERO.subTo(this,this);\n    }\n\n    // (protected) alternate constructor\n    function bnpFromNumber(a,b,c) {\n      if(\"number\" == typeof b) {\n        // new BigInteger(int,int,RNG)\n        if(a < 2) this.fromInt(1);\n        else {\n          this.fromNumber(a,c);\n          if(!this.testBit(a-1))    // force MSB set\n            this.bitwiseTo(BigInteger.ONE.shiftLeft(a-1),op_or,this);\n          if(this.isEven()) this.dAddOffset(1,0); // force odd\n          while(!this.isProbablePrime(b)) {\n            this.dAddOffset(2,0);\n            if(this.bitLength() > a) this.subTo(BigInteger.ONE.shiftLeft(a-1),this);\n          }\n        }\n      }\n      else {\n        // new BigInteger(int,RNG)\n        var x = new Array(), t = a&7;\n        x.length = (a>>3)+1;\n        b.nextBytes(x);\n        if(t > 0) x[0] &= ((1<<t)-1); else x[0] = 0;\n        this.fromString(x,256);\n      }\n    }\n\n    // (public) convert to bigendian byte array\n    function bnToByteArray() {\n      var i = this.t, r = new Array();\n      r[0] = this.s;\n      var p = this.DB-(i*this.DB)%8, d, k = 0;\n      if(i-- > 0) {\n        if(p < this.DB && (d = this[i]>>p) != (this.s&this.DM)>>p)\n          r[k++] = d|(this.s<<(this.DB-p));\n        while(i >= 0) {\n          if(p < 8) {\n            d = (this[i]&((1<<p)-1))<<(8-p);\n            d |= this[--i]>>(p+=this.DB-8);\n          }\n          else {\n            d = (this[i]>>(p-=8))&0xff;\n            if(p <= 0) { p += this.DB; --i; }\n          }\n          if((d&0x80) != 0) d |= -256;\n          if(k == 0 && (this.s&0x80) != (d&0x80)) ++k;\n          if(k > 0 || d != this.s) r[k++] = d;\n        }\n      }\n      return r;\n    }\n\n    function bnEquals(a) { return(this.compareTo(a)==0); }\n    function bnMin(a) { return(this.compareTo(a)<0)?this:a; }\n    function bnMax(a) { return(this.compareTo(a)>0)?this:a; }\n\n    // (protected) r = this op a (bitwise)\n    function bnpBitwiseTo(a,op,r) {\n      var i, f, m = Math.min(a.t,this.t);\n      for(i = 0; i < m; ++i) r[i] = op(this[i],a[i]);\n      if(a.t < this.t) {\n        f = a.s&this.DM;\n        for(i = m; i < this.t; ++i) r[i] = op(this[i],f);\n        r.t = this.t;\n      }\n      else {\n        f = this.s&this.DM;\n        for(i = m; i < a.t; ++i) r[i] = op(f,a[i]);\n        r.t = a.t;\n      }\n      r.s = op(this.s,a.s);\n      r.clamp();\n    }\n\n    // (public) this & a\n    function op_and(x,y) { return x&y; }\n    function bnAnd(a) { var r = nbi(); this.bitwiseTo(a,op_and,r); return r; }\n\n    // (public) this | a\n    function op_or(x,y) { return x|y; }\n    function bnOr(a) { var r = nbi(); this.bitwiseTo(a,op_or,r); return r; }\n\n    // (public) this ^ a\n    function op_xor(x,y) { return x^y; }\n    function bnXor(a) { var r = nbi(); this.bitwiseTo(a,op_xor,r); return r; }\n\n    // (public) this & ~a\n    function op_andnot(x,y) { return x&~y; }\n    function bnAndNot(a) { var r = nbi(); this.bitwiseTo(a,op_andnot,r); return r; }\n\n    // (public) ~this\n    function bnNot() {\n      var r = nbi();\n      for(var i = 0; i < this.t; ++i) r[i] = this.DM&~this[i];\n      r.t = this.t;\n      r.s = ~this.s;\n      return r;\n    }\n\n    // (public) this << n\n    function bnShiftLeft(n) {\n      var r = nbi();\n      if(n < 0) this.rShiftTo(-n,r); else this.lShiftTo(n,r);\n      return r;\n    }\n\n    // (public) this >> n\n    function bnShiftRight(n) {\n      var r = nbi();\n      if(n < 0) this.lShiftTo(-n,r); else this.rShiftTo(n,r);\n      return r;\n    }\n\n    // return index of lowest 1-bit in x, x < 2^31\n    function lbit(x) {\n      if(x == 0) return -1;\n      var r = 0;\n      if((x&0xffff) == 0) { x >>= 16; r += 16; }\n      if((x&0xff) == 0) { x >>= 8; r += 8; }\n      if((x&0xf) == 0) { x >>= 4; r += 4; }\n      if((x&3) == 0) { x >>= 2; r += 2; }\n      if((x&1) == 0) ++r;\n      return r;\n    }\n\n    // (public) returns index of lowest 1-bit (or -1 if none)\n    function bnGetLowestSetBit() {\n      for(var i = 0; i < this.t; ++i)\n        if(this[i] != 0) return i*this.DB+lbit(this[i]);\n      if(this.s < 0) return this.t*this.DB;\n      return -1;\n    }\n\n    // return number of 1 bits in x\n    function cbit(x) {\n      var r = 0;\n      while(x != 0) { x &= x-1; ++r; }\n      return r;\n    }\n\n    // (public) return number of set bits\n    function bnBitCount() {\n      var r = 0, x = this.s&this.DM;\n      for(var i = 0; i < this.t; ++i) r += cbit(this[i]^x);\n      return r;\n    }\n\n    // (public) true iff nth bit is set\n    function bnTestBit(n) {\n      var j = Math.floor(n/this.DB);\n      if(j >= this.t) return(this.s!=0);\n      return((this[j]&(1<<(n%this.DB)))!=0);\n    }\n\n    // (protected) this op (1<<n)\n    function bnpChangeBit(n,op) {\n      var r = BigInteger.ONE.shiftLeft(n);\n      this.bitwiseTo(r,op,r);\n      return r;\n    }\n\n    // (public) this | (1<<n)\n    function bnSetBit(n) { return this.changeBit(n,op_or); }\n\n    // (public) this & ~(1<<n)\n    function bnClearBit(n) { return this.changeBit(n,op_andnot); }\n\n    // (public) this ^ (1<<n)\n    function bnFlipBit(n) { return this.changeBit(n,op_xor); }\n\n    // (protected) r = this + a\n    function bnpAddTo(a,r) {\n      var i = 0, c = 0, m = Math.min(a.t,this.t);\n      while(i < m) {\n        c += this[i]+a[i];\n        r[i++] = c&this.DM;\n        c >>= this.DB;\n      }\n      if(a.t < this.t) {\n        c += a.s;\n        while(i < this.t) {\n          c += this[i];\n          r[i++] = c&this.DM;\n          c >>= this.DB;\n        }\n        c += this.s;\n      }\n      else {\n        c += this.s;\n        while(i < a.t) {\n          c += a[i];\n          r[i++] = c&this.DM;\n          c >>= this.DB;\n        }\n        c += a.s;\n      }\n      r.s = (c<0)?-1:0;\n      if(c > 0) r[i++] = c;\n      else if(c < -1) r[i++] = this.DV+c;\n      r.t = i;\n      r.clamp();\n    }\n\n    // (public) this + a\n    function bnAdd(a) { var r = nbi(); this.addTo(a,r); return r; }\n\n    // (public) this - a\n    function bnSubtract(a) { var r = nbi(); this.subTo(a,r); return r; }\n\n    // (public) this * a\n    function bnMultiply(a) { var r = nbi(); this.multiplyTo(a,r); return r; }\n\n    // (public) this^2\n    function bnSquare() { var r = nbi(); this.squareTo(r); return r; }\n\n    // (public) this / a\n    function bnDivide(a) { var r = nbi(); this.divRemTo(a,r,null); return r; }\n\n    // (public) this % a\n    function bnRemainder(a) { var r = nbi(); this.divRemTo(a,null,r); return r; }\n\n    // (public) [this/a,this%a]\n    function bnDivideAndRemainder(a) {\n      var q = nbi(), r = nbi();\n      this.divRemTo(a,q,r);\n      return new Array(q,r);\n    }\n\n    // (protected) this *= n, this >= 0, 1 < n < DV\n    function bnpDMultiply(n) {\n      this[this.t] = this.am(0,n-1,this,0,0,this.t);\n      ++this.t;\n      this.clamp();\n    }\n\n    // (protected) this += n << w words, this >= 0\n    function bnpDAddOffset(n,w) {\n      if(n == 0) return;\n      while(this.t <= w) this[this.t++] = 0;\n      this[w] += n;\n      while(this[w] >= this.DV) {\n        this[w] -= this.DV;\n        if(++w >= this.t) this[this.t++] = 0;\n        ++this[w];\n      }\n    }\n\n    // A \"null\" reducer\n    function NullExp() {}\n    function nNop(x) { return x; }\n    function nMulTo(x,y,r) { x.multiplyTo(y,r); }\n    function nSqrTo(x,r) { x.squareTo(r); }\n\n    NullExp.prototype.convert = nNop;\n    NullExp.prototype.revert = nNop;\n    NullExp.prototype.mulTo = nMulTo;\n    NullExp.prototype.sqrTo = nSqrTo;\n\n    // (public) this^e\n    function bnPow(e) { return this.exp(e,new NullExp()); }\n\n    // (protected) r = lower n words of \"this * a\", a.t <= n\n    // \"this\" should be the larger one if appropriate.\n    function bnpMultiplyLowerTo(a,n,r) {\n      var i = Math.min(this.t+a.t,n);\n      r.s = 0; // assumes a,this >= 0\n      r.t = i;\n      while(i > 0) r[--i] = 0;\n      var j;\n      for(j = r.t-this.t; i < j; ++i) r[i+this.t] = this.am(0,a[i],r,i,0,this.t);\n      for(j = Math.min(a.t,n); i < j; ++i) this.am(0,a[i],r,i,0,n-i);\n      r.clamp();\n    }\n\n    // (protected) r = \"this * a\" without lower n words, n > 0\n    // \"this\" should be the larger one if appropriate.\n    function bnpMultiplyUpperTo(a,n,r) {\n      --n;\n      var i = r.t = this.t+a.t-n;\n      r.s = 0; // assumes a,this >= 0\n      while(--i >= 0) r[i] = 0;\n      for(i = Math.max(n-this.t,0); i < a.t; ++i)\n        r[this.t+i-n] = this.am(n-i,a[i],r,0,0,this.t+i-n);\n      r.clamp();\n      r.drShiftTo(1,r);\n    }\n\n    // Barrett modular reduction\n    function Barrett(m) {\n      // setup Barrett\n      this.r2 = nbi();\n      this.q3 = nbi();\n      BigInteger.ONE.dlShiftTo(2*m.t,this.r2);\n      this.mu = this.r2.divide(m);\n      this.m = m;\n    }\n\n    function barrettConvert(x) {\n      if(x.s < 0 || x.t > 2*this.m.t) return x.mod(this.m);\n      else if(x.compareTo(this.m) < 0) return x;\n      else { var r = nbi(); x.copyTo(r); this.reduce(r); return r; }\n    }\n\n    function barrettRevert(x) { return x; }\n\n    // x = x mod m (HAC 14.42)\n    function barrettReduce(x) {\n      x.drShiftTo(this.m.t-1,this.r2);\n      if(x.t > this.m.t+1) { x.t = this.m.t+1; x.clamp(); }\n      this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3);\n      this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);\n      while(x.compareTo(this.r2) < 0) x.dAddOffset(1,this.m.t+1);\n      x.subTo(this.r2,x);\n      while(x.compareTo(this.m) >= 0) x.subTo(this.m,x);\n    }\n\n    // r = x^2 mod m; x != r\n    function barrettSqrTo(x,r) { x.squareTo(r); this.reduce(r); }\n\n    // r = x*y mod m; x,y != r\n    function barrettMulTo(x,y,r) { x.multiplyTo(y,r); this.reduce(r); }\n\n    Barrett.prototype.convert = barrettConvert;\n    Barrett.prototype.revert = barrettRevert;\n    Barrett.prototype.reduce = barrettReduce;\n    Barrett.prototype.mulTo = barrettMulTo;\n    Barrett.prototype.sqrTo = barrettSqrTo;\n\n    // (public) this^e % m (HAC 14.85)\n    function bnModPow(e,m) {\n      var i = e.bitLength(), k, r = nbv(1), z;\n      if(i <= 0) return r;\n      else if(i < 18) k = 1;\n      else if(i < 48) k = 3;\n      else if(i < 144) k = 4;\n      else if(i < 768) k = 5;\n      else k = 6;\n      if(i < 8)\n        z = new Classic(m);\n      else if(m.isEven())\n        z = new Barrett(m);\n      else\n        z = new Montgomery(m);\n\n      // precomputation\n      var g = new Array(), n = 3, k1 = k-1, km = (1<<k)-1;\n      g[1] = z.convert(this);\n      if(k > 1) {\n        var g2 = nbi();\n        z.sqrTo(g[1],g2);\n        while(n <= km) {\n          g[n] = nbi();\n          z.mulTo(g2,g[n-2],g[n]);\n          n += 2;\n        }\n      }\n\n      var j = e.t-1, w, is1 = true, r2 = nbi(), t;\n      i = nbits(e[j])-1;\n      while(j >= 0) {\n        if(i >= k1) w = (e[j]>>(i-k1))&km;\n        else {\n          w = (e[j]&((1<<(i+1))-1))<<(k1-i);\n          if(j > 0) w |= e[j-1]>>(this.DB+i-k1);\n        }\n\n        n = k;\n        while((w&1) == 0) { w >>= 1; --n; }\n        if((i -= n) < 0) { i += this.DB; --j; }\n        if(is1) {    // ret == 1, don't bother squaring or multiplying it\n          g[w].copyTo(r);\n          is1 = false;\n        }\n        else {\n          while(n > 1) { z.sqrTo(r,r2); z.sqrTo(r2,r); n -= 2; }\n          if(n > 0) z.sqrTo(r,r2); else { t = r; r = r2; r2 = t; }\n          z.mulTo(r2,g[w],r);\n        }\n\n        while(j >= 0 && (e[j]&(1<<i)) == 0) {\n          z.sqrTo(r,r2); t = r; r = r2; r2 = t;\n          if(--i < 0) { i = this.DB-1; --j; }\n        }\n      }\n      return z.revert(r);\n    }\n\n    // (public) gcd(this,a) (HAC 14.54)\n    function bnGCD(a) {\n      var x = (this.s<0)?this.negate():this.clone();\n      var y = (a.s<0)?a.negate():a.clone();\n      if(x.compareTo(y) < 0) { var t = x; x = y; y = t; }\n      var i = x.getLowestSetBit(), g = y.getLowestSetBit();\n      if(g < 0) return x;\n      if(i < g) g = i;\n      if(g > 0) {\n        x.rShiftTo(g,x);\n        y.rShiftTo(g,y);\n      }\n      while(x.signum() > 0) {\n        if((i = x.getLowestSetBit()) > 0) x.rShiftTo(i,x);\n        if((i = y.getLowestSetBit()) > 0) y.rShiftTo(i,y);\n        if(x.compareTo(y) >= 0) {\n          x.subTo(y,x);\n          x.rShiftTo(1,x);\n        }\n        else {\n          y.subTo(x,y);\n          y.rShiftTo(1,y);\n        }\n      }\n      if(g > 0) y.lShiftTo(g,y);\n      return y;\n    }\n\n    // (protected) this % n, n < 2^26\n    function bnpModInt(n) {\n      if(n <= 0) return 0;\n      var d = this.DV%n, r = (this.s<0)?n-1:0;\n      if(this.t > 0)\n        if(d == 0) r = this[0]%n;\n        else for(var i = this.t-1; i >= 0; --i) r = (d*r+this[i])%n;\n      return r;\n    }\n\n    // (public) 1/this % m (HAC 14.61)\n    function bnModInverse(m) {\n      var ac = m.isEven();\n      if((this.isEven() && ac) || m.signum() == 0) return BigInteger.ZERO;\n      var u = m.clone(), v = this.clone();\n      var a = nbv(1), b = nbv(0), c = nbv(0), d = nbv(1);\n      while(u.signum() != 0) {\n        while(u.isEven()) {\n          u.rShiftTo(1,u);\n          if(ac) {\n            if(!a.isEven() || !b.isEven()) { a.addTo(this,a); b.subTo(m,b); }\n            a.rShiftTo(1,a);\n          }\n          else if(!b.isEven()) b.subTo(m,b);\n          b.rShiftTo(1,b);\n        }\n        while(v.isEven()) {\n          v.rShiftTo(1,v);\n          if(ac) {\n            if(!c.isEven() || !d.isEven()) { c.addTo(this,c); d.subTo(m,d); }\n            c.rShiftTo(1,c);\n          }\n          else if(!d.isEven()) d.subTo(m,d);\n          d.rShiftTo(1,d);\n        }\n        if(u.compareTo(v) >= 0) {\n          u.subTo(v,u);\n          if(ac) a.subTo(c,a);\n          b.subTo(d,b);\n        }\n        else {\n          v.subTo(u,v);\n          if(ac) c.subTo(a,c);\n          d.subTo(b,d);\n        }\n      }\n      if(v.compareTo(BigInteger.ONE) != 0) return BigInteger.ZERO;\n      if(d.compareTo(m) >= 0) return d.subtract(m);\n      if(d.signum() < 0) d.addTo(m,d); else return d;\n      if(d.signum() < 0) return d.add(m); else return d;\n    }\n\n    var lowprimes = [2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997];\n    var lplim = (1<<26)/lowprimes[lowprimes.length-1];\n\n    // (public) test primality with certainty >= 1-.5^t\n    function bnIsProbablePrime(t) {\n      var i, x = this.abs();\n      if(x.t == 1 && x[0] <= lowprimes[lowprimes.length-1]) {\n        for(i = 0; i < lowprimes.length; ++i)\n          if(x[0] == lowprimes[i]) return true;\n        return false;\n      }\n      if(x.isEven()) return false;\n      i = 1;\n      while(i < lowprimes.length) {\n        var m = lowprimes[i], j = i+1;\n        while(j < lowprimes.length && m < lplim) m *= lowprimes[j++];\n        m = x.modInt(m);\n        while(i < j) if(m%lowprimes[i++] == 0) return false;\n      }\n      return x.millerRabin(t);\n    }\n\n    // (protected) true if probably prime (HAC 4.24, Miller-Rabin)\n    function bnpMillerRabin(t) {\n      var n1 = this.subtract(BigInteger.ONE);\n      var k = n1.getLowestSetBit();\n      if(k <= 0) return false;\n      var r = n1.shiftRight(k);\n      t = (t+1)>>1;\n      if(t > lowprimes.length) t = lowprimes.length;\n      var a = nbi();\n      for(var i = 0; i < t; ++i) {\n        //Pick bases at random, instead of starting at 2\n        a.fromInt(lowprimes[Math.floor(Math.random()*lowprimes.length)]);\n        var y = a.modPow(r,this);\n        if(y.compareTo(BigInteger.ONE) != 0 && y.compareTo(n1) != 0) {\n          var j = 1;\n          while(j++ < k && y.compareTo(n1) != 0) {\n            y = y.modPowInt(2,this);\n            if(y.compareTo(BigInteger.ONE) == 0) return false;\n          }\n          if(y.compareTo(n1) != 0) return false;\n        }\n      }\n      return true;\n    }\n\n    // protected\n    BigInteger.prototype.chunkSize = bnpChunkSize;\n    BigInteger.prototype.toRadix = bnpToRadix;\n    BigInteger.prototype.fromRadix = bnpFromRadix;\n    BigInteger.prototype.fromNumber = bnpFromNumber;\n    BigInteger.prototype.bitwiseTo = bnpBitwiseTo;\n    BigInteger.prototype.changeBit = bnpChangeBit;\n    BigInteger.prototype.addTo = bnpAddTo;\n    BigInteger.prototype.dMultiply = bnpDMultiply;\n    BigInteger.prototype.dAddOffset = bnpDAddOffset;\n    BigInteger.prototype.multiplyLowerTo = bnpMultiplyLowerTo;\n    BigInteger.prototype.multiplyUpperTo = bnpMultiplyUpperTo;\n    BigInteger.prototype.modInt = bnpModInt;\n    BigInteger.prototype.millerRabin = bnpMillerRabin;\n\n    // public\n    BigInteger.prototype.clone = bnClone;\n    BigInteger.prototype.intValue = bnIntValue;\n    BigInteger.prototype.byteValue = bnByteValue;\n    BigInteger.prototype.shortValue = bnShortValue;\n    BigInteger.prototype.signum = bnSigNum;\n    BigInteger.prototype.toByteArray = bnToByteArray;\n    BigInteger.prototype.equals = bnEquals;\n    BigInteger.prototype.min = bnMin;\n    BigInteger.prototype.max = bnMax;\n    BigInteger.prototype.and = bnAnd;\n    BigInteger.prototype.or = bnOr;\n    BigInteger.prototype.xor = bnXor;\n    BigInteger.prototype.andNot = bnAndNot;\n    BigInteger.prototype.not = bnNot;\n    BigInteger.prototype.shiftLeft = bnShiftLeft;\n    BigInteger.prototype.shiftRight = bnShiftRight;\n    BigInteger.prototype.getLowestSetBit = bnGetLowestSetBit;\n    BigInteger.prototype.bitCount = bnBitCount;\n    BigInteger.prototype.testBit = bnTestBit;\n    BigInteger.prototype.setBit = bnSetBit;\n    BigInteger.prototype.clearBit = bnClearBit;\n    BigInteger.prototype.flipBit = bnFlipBit;\n    BigInteger.prototype.add = bnAdd;\n    BigInteger.prototype.subtract = bnSubtract;\n    BigInteger.prototype.multiply = bnMultiply;\n    BigInteger.prototype.divide = bnDivide;\n    BigInteger.prototype.remainder = bnRemainder;\n    BigInteger.prototype.divideAndRemainder = bnDivideAndRemainder;\n    BigInteger.prototype.modPow = bnModPow;\n    BigInteger.prototype.modInverse = bnModInverse;\n    BigInteger.prototype.pow = bnPow;\n    BigInteger.prototype.gcd = bnGCD;\n    BigInteger.prototype.isProbablePrime = bnIsProbablePrime;\n\n    // JSBN-specific extension\n    BigInteger.prototype.square = bnSquare;\n\n    // Expose the Barrett function\n    BigInteger.prototype.Barrett = Barrett\n\n    // BigInteger interfaces not implemented in jsbn:\n\n    // BigInteger(int signum, byte[] magnitude)\n    // double doubleValue()\n    // float floatValue()\n    // int hashCode()\n    // long longValue()\n    // static BigInteger valueOf(long val)\n\n    // Random number generator - requires a PRNG backend, e.g. prng4.js\n\n    // For best results, put code like\n    // <body onClick='rng_seed_time();' onKeyPress='rng_seed_time();'>\n    // in your main HTML document.\n\n    var rng_state;\n    var rng_pool;\n    var rng_pptr;\n\n    // Mix in a 32-bit integer into the pool\n    function rng_seed_int(x) {\n      rng_pool[rng_pptr++] ^= x & 255;\n      rng_pool[rng_pptr++] ^= (x >> 8) & 255;\n      rng_pool[rng_pptr++] ^= (x >> 16) & 255;\n      rng_pool[rng_pptr++] ^= (x >> 24) & 255;\n      if(rng_pptr >= rng_psize) rng_pptr -= rng_psize;\n    }\n\n    // Mix in the current time (w/milliseconds) into the pool\n    function rng_seed_time() {\n      rng_seed_int(new Date().getTime());\n    }\n\n    // Initialize the pool with junk if needed.\n    if(rng_pool == null) {\n      rng_pool = new Array();\n      rng_pptr = 0;\n      var t;\n      if(typeof window !== \"undefined\" && window.crypto) {\n        if (window.crypto.getRandomValues) {\n          // Use webcrypto if available\n          var ua = new Uint8Array(32);\n          window.crypto.getRandomValues(ua);\n          for(t = 0; t < 32; ++t)\n            rng_pool[rng_pptr++] = ua[t];\n        }\n        else if(navigator.appName == \"Netscape\" && navigator.appVersion < \"5\") {\n          // Extract entropy (256 bits) from NS4 RNG if available\n          var z = window.crypto.random(32);\n          for(t = 0; t < z.length; ++t)\n            rng_pool[rng_pptr++] = z.charCodeAt(t) & 255;\n        }\n      }\n      while(rng_pptr < rng_psize) {  // extract some randomness from Math.random()\n        t = Math.floor(65536 * Math.random());\n        rng_pool[rng_pptr++] = t >>> 8;\n        rng_pool[rng_pptr++] = t & 255;\n      }\n      rng_pptr = 0;\n      rng_seed_time();\n      //rng_seed_int(window.screenX);\n      //rng_seed_int(window.screenY);\n    }\n\n    function rng_get_byte() {\n      if(rng_state == null) {\n        rng_seed_time();\n        rng_state = prng_newstate();\n        rng_state.init(rng_pool);\n        for(rng_pptr = 0; rng_pptr < rng_pool.length; ++rng_pptr)\n          rng_pool[rng_pptr] = 0;\n        rng_pptr = 0;\n        //rng_pool = null;\n      }\n      // TODO: allow reseeding after first request\n      return rng_state.next();\n    }\n\n    function rng_get_bytes(ba) {\n      var i;\n      for(i = 0; i < ba.length; ++i) ba[i] = rng_get_byte();\n    }\n\n    function SecureRandom() {}\n\n    SecureRandom.prototype.nextBytes = rng_get_bytes;\n\n    // prng4.js - uses Arcfour as a PRNG\n\n    function Arcfour() {\n      this.i = 0;\n      this.j = 0;\n      this.S = new Array();\n    }\n\n    // Initialize arcfour context from key, an array of ints, each from [0..255]\n    function ARC4init(key) {\n      var i, j, t;\n      for(i = 0; i < 256; ++i)\n        this.S[i] = i;\n      j = 0;\n      for(i = 0; i < 256; ++i) {\n        j = (j + this.S[i] + key[i % key.length]) & 255;\n        t = this.S[i];\n        this.S[i] = this.S[j];\n        this.S[j] = t;\n      }\n      this.i = 0;\n      this.j = 0;\n    }\n\n    function ARC4next() {\n      var t;\n      this.i = (this.i + 1) & 255;\n      this.j = (this.j + this.S[this.i]) & 255;\n      t = this.S[this.i];\n      this.S[this.i] = this.S[this.j];\n      this.S[this.j] = t;\n      return this.S[(t + this.S[this.i]) & 255];\n    }\n\n    Arcfour.prototype.init = ARC4init;\n    Arcfour.prototype.next = ARC4next;\n\n    // Plug in your RNG constructor here\n    function prng_newstate() {\n      return new Arcfour();\n    }\n\n    // Pool size must be a multiple of 4 and greater than 32.\n    // An array of bytes the size of the pool will be passed to init()\n    var rng_psize = 256;\n\n    if (typeof exports !== 'undefined') {\n        exports = module.exports = {\n            default: BigInteger,\n            BigInteger: BigInteger,\n            SecureRandom: SecureRandom,\n        };\n    } else {\n        this.jsbn = {\n          BigInteger: BigInteger,\n          SecureRandom: SecureRandom\n        };\n    }\n\n}).call(this);\n", "/* eslint-disable class-methods-use-this */\r\nconst {BigInteger} = require('jsbn')\r\n\r\nfunction bigintToValue(bigint) {\r\n  let h = bigint.toString(16)\r\n  if (h[0] !== '-') {\r\n    // 正数\r\n    if (h.length % 2 === 1) h = '0' + h // 补齐到整字节\r\n    else if (!h.match(/^[0-7]/)) h = '00' + h // 非0开头，则补一个全0字节\r\n  } else {\r\n    // 负数\r\n    h = h.substr(1)\r\n\r\n    let len = h.length\r\n    if (len % 2 === 1) len += 1 // 补齐到整字节\r\n    else if (!h.match(/^[0-7]/)) len += 2 // 非0开头，则补一个全0字节\r\n\r\n    let mask = ''\r\n    for (let i = 0; i < len; i++) mask += 'f'\r\n    mask = new BigInteger(mask, 16)\r\n\r\n    // 对绝对值取反，加1\r\n    h = mask.xor(bigint).add(BigInteger.ONE)\r\n    h = h.toString(16).replace(/^-/, '')\r\n  }\r\n  return h\r\n}\r\n\r\nclass ASN1Object {\r\n  constructor() {\r\n    this.tlv = null\r\n    this.t = '00'\r\n    this.l = '00'\r\n    this.v = ''\r\n  }\r\n\r\n  /**\r\n   * 获取 der 编码比特流16进制串\r\n   */\r\n  getEncodedHex() {\r\n    if (!this.tlv) {\r\n      this.v = this.getValue()\r\n      this.l = this.getLength()\r\n      this.tlv = this.t + this.l + this.v\r\n    }\r\n    return this.tlv\r\n  }\r\n\r\n  getLength() {\r\n    const n = this.v.length / 2 // 字节数\r\n    let nHex = n.toString(16)\r\n    if (nHex.length % 2 === 1) nHex = '0' + nHex // 补齐到整字节\r\n\r\n    if (n < 128) {\r\n      // 短格式，以 0 开头\r\n      return nHex\r\n    } else {\r\n      // 长格式，以 1 开头\r\n      const head = 128 + nHex.length / 2 // 1(1位) + 真正的长度占用字节数(7位) + 真正的长度\r\n      return head.toString(16) + nHex\r\n    }\r\n  }\r\n\r\n  getValue() {\r\n    return ''\r\n  }\r\n}\r\n\r\nclass DERInteger extends ASN1Object {\r\n  constructor(bigint) {\r\n    super()\r\n\r\n    this.t = '02' // 整型标签说明\r\n    if (bigint) this.v = bigintToValue(bigint)\r\n  }\r\n\r\n  getValue() {\r\n    return this.v\r\n  }\r\n}\r\n\r\nclass DERSequence extends ASN1Object {\r\n  constructor(asn1Array) {\r\n    super()\r\n\r\n    this.t = '30' // 序列标签说明\r\n    this.asn1Array = asn1Array\r\n  }\r\n\r\n  getValue() {\r\n    this.v = this.asn1Array.map(asn1Object => asn1Object.getEncodedHex()).join('')\r\n    return this.v\r\n  }\r\n}\r\n\r\n/**\r\n * 获取 l 占用字节数\r\n */\r\nfunction getLenOfL(str, start) {\r\n  if (+str[start + 2] < 8) return 1 // l 以0开头，则表示短格式，只占一个字节\r\n  return +str.substr(start + 2, 2) & 0x7f + 1 // 长格式，取第一个字节后7位作为长度真正占用字节数，再加上本身\r\n}\r\n\r\n/**\r\n * 获取 l\r\n */\r\nfunction getL(str, start) {\r\n  // 获取 l\r\n  const len = getLenOfL(str, start)\r\n  const l = str.substr(start + 2, len * 2)\r\n\r\n  if (!l) return -1\r\n  const bigint = +l[0] < 8 ? new BigInteger(l, 16) : new BigInteger(l.substr(2), 16)\r\n\r\n  return bigint.intValue()\r\n}\r\n\r\n/**\r\n * 获取 v 的位置\r\n */\r\nfunction getStartOfV(str, start) {\r\n  const len = getLenOfL(str, start)\r\n  return start + (len + 1) * 2\r\n}\r\n\r\nmodule.exports = {\r\n  /**\r\n   * ASN.1 der 编码，针对 sm2 签名\r\n   */\r\n  encodeDer(r, s) {\r\n    const derR = new DERInteger(r)\r\n    const derS = new DERInteger(s)\r\n    const derSeq = new DERSequence([derR, derS])\r\n\r\n    return derSeq.getEncodedHex()\r\n  },\r\n\r\n  /**\r\n   * 解析 ASN.1 der，针对 sm2 验签\r\n   */\r\n  decodeDer(input) {\r\n    // 结构：\r\n    // input = | tSeq | lSeq | vSeq |\r\n    // vSeq = | tR | lR | vR | tS | lS | vS |\r\n    const start = getStartOfV(input, 0)\r\n\r\n    const vIndexR = getStartOfV(input, start)\r\n    const lR = getL(input, start)\r\n    const vR = input.substr(vIndexR, lR * 2)\r\n\r\n    const nextStart = vIndexR + vR.length\r\n    const vIndexS = getStartOfV(input, nextStart)\r\n    const lS = getL(input, nextStart)\r\n    const vS = input.substr(vIndexS, lS * 2)\r\n\r\n    const r = new BigInteger(vR, 16)\r\n    const s = new BigInteger(vS, 16)\r\n\r\n    return {r, s}\r\n  }\r\n}\r\n", "/* eslint-disable no-case-declarations, max-len */\r\nconst {BigInteger} = require('jsbn')\r\n\r\n/**\r\n * thanks for <PERSON> : http://www-cs-students.stanford.edu/~tjw/jsbn/\r\n *\r\n * Basic Javascript Elliptic Curve implementation\r\n * Ported loosely from BouncyCastle's Java EC code\r\n * Only Fp curves implemented for now\r\n */\r\n\r\nconst TWO = new BigInteger('2')\r\nconst THREE = new BigInteger('3')\r\n\r\n/**\r\n * 椭圆曲线域元素\r\n */\r\nclass ECFieldElementFp {\r\n  constructor(q, x) {\r\n    this.x = x\r\n    this.q = q\r\n    // TODO if (x.compareTo(q) >= 0) error\r\n  }\r\n\r\n  /**\r\n   * 判断相等\r\n   */\r\n  equals(other) {\r\n    if (other === this) return true\r\n    return (this.q.equals(other.q) && this.x.equals(other.x))\r\n  }\r\n\r\n  /**\r\n   * 返回具体数值\r\n   */\r\n  toBigInteger() {\r\n    return this.x\r\n  }\r\n\r\n  /**\r\n   * 取反\r\n   */\r\n  negate() {\r\n    return new ECFieldElementFp(this.q, this.x.negate().mod(this.q))\r\n  }\r\n\r\n  /**\r\n   * 相加\r\n   */\r\n  add(b) {\r\n    return new ECFieldElementFp(this.q, this.x.add(b.toBigInteger()).mod(this.q))\r\n  }\r\n\r\n  /**\r\n   * 相减\r\n   */\r\n  subtract(b) {\r\n    return new ECFieldElementFp(this.q, this.x.subtract(b.toBigInteger()).mod(this.q))\r\n  }\r\n\r\n  /**\r\n   * 相乘\r\n   */\r\n  multiply(b) {\r\n    return new ECFieldElementFp(this.q, this.x.multiply(b.toBigInteger()).mod(this.q))\r\n  }\r\n\r\n  /**\r\n   * 相除\r\n   */\r\n  divide(b) {\r\n    return new ECFieldElementFp(this.q, this.x.multiply(b.toBigInteger().modInverse(this.q)).mod(this.q))\r\n  }\r\n\r\n  /**\r\n   * 平方\r\n   */\r\n  square() {\r\n    return new ECFieldElementFp(this.q, this.x.square().mod(this.q))\r\n  }\r\n}\r\n\r\nclass ECPointFp {\r\n  constructor(curve, x, y, z) {\r\n    this.curve = curve\r\n    this.x = x\r\n    this.y = y\r\n    // 标准射影坐标系：zinv == null 或 z * zinv == 1\r\n    this.z = z == null ? BigInteger.ONE : z\r\n    this.zinv = null\r\n    // TODO: compression flag\r\n  }\r\n\r\n  getX() {\r\n    if (this.zinv === null) this.zinv = this.z.modInverse(this.curve.q)\r\n\r\n    return this.curve.fromBigInteger(this.x.toBigInteger().multiply(this.zinv).mod(this.curve.q))\r\n  }\r\n\r\n  getY() {\r\n    if (this.zinv === null) this.zinv = this.z.modInverse(this.curve.q)\r\n\r\n    return this.curve.fromBigInteger(this.y.toBigInteger().multiply(this.zinv).mod(this.curve.q))\r\n  }\r\n\r\n  /**\r\n   * 判断相等\r\n   */\r\n  equals(other) {\r\n    if (other === this) return true\r\n    if (this.isInfinity()) return other.isInfinity()\r\n    if (other.isInfinity()) return this.isInfinity()\r\n\r\n    // u = y2 * z1 - y1 * z2\r\n    const u = other.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(other.z)).mod(this.curve.q)\r\n    if (!u.equals(BigInteger.ZERO)) return false\r\n\r\n    // v = x2 * z1 - x1 * z2\r\n    const v = other.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(other.z)).mod(this.curve.q)\r\n    return v.equals(BigInteger.ZERO)\r\n  }\r\n\r\n  /**\r\n   * 是否是无穷远点\r\n   */\r\n  isInfinity() {\r\n    if ((this.x === null) && (this.y === null)) return true\r\n    return this.z.equals(BigInteger.ZERO) && !this.y.toBigInteger().equals(BigInteger.ZERO)\r\n  }\r\n\r\n  /**\r\n   * 取反，x 轴对称点\r\n   */\r\n  negate() {\r\n    return new ECPointFp(this.curve, this.x, this.y.negate(), this.z)\r\n  }\r\n\r\n  /**\r\n   * 相加\r\n   *\r\n   * 标准射影坐标系：\r\n   *\r\n   * λ1 = x1 * z2\r\n   * λ2 = x2 * z1\r\n   * λ3 = λ1 − λ2\r\n   * λ4 = y1 * z2\r\n   * λ5 = y2 * z1\r\n   * λ6 = λ4 − λ5\r\n   * λ7 = λ1 + λ2\r\n   * λ8 = z1 * z2\r\n   * λ9 = λ3^2\r\n   * λ10 = λ3 * λ9\r\n   * λ11 = λ8 * λ6^2 − λ7 * λ9\r\n   * x3 = λ3 * λ11\r\n   * y3 = λ6 * (λ9 * λ1 − λ11) − λ4 * λ10\r\n   * z3 = λ10 * λ8\r\n   */\r\n  add(b) {\r\n    if (this.isInfinity()) return b\r\n    if (b.isInfinity()) return this\r\n\r\n    const x1 = this.x.toBigInteger()\r\n    const y1 = this.y.toBigInteger()\r\n    const z1 = this.z\r\n    const x2 = b.x.toBigInteger()\r\n    const y2 = b.y.toBigInteger()\r\n    const z2 = b.z\r\n    const q = this.curve.q\r\n\r\n    const w1 = x1.multiply(z2).mod(q)\r\n    const w2 = x2.multiply(z1).mod(q)\r\n    const w3 = w1.subtract(w2)\r\n    const w4 = y1.multiply(z2).mod(q)\r\n    const w5 = y2.multiply(z1).mod(q)\r\n    const w6 = w4.subtract(w5)\r\n\r\n    if (BigInteger.ZERO.equals(w3)) {\r\n      if (BigInteger.ZERO.equals(w6)) {\r\n        return this.twice() // this == b，计算自加\r\n      }\r\n      return this.curve.infinity // this == -b，则返回无穷远点\r\n    }\r\n\r\n    const w7 = w1.add(w2)\r\n    const w8 = z1.multiply(z2).mod(q)\r\n    const w9 = w3.square().mod(q)\r\n    const w10 = w3.multiply(w9).mod(q)\r\n    const w11 = w8.multiply(w6.square()).subtract(w7.multiply(w9)).mod(q)\r\n\r\n    const x3 = w3.multiply(w11).mod(q)\r\n    const y3 = w6.multiply(w9.multiply(w1).subtract(w11)).subtract(w4.multiply(w10)).mod(q)\r\n    const z3 = w10.multiply(w8).mod(q)\r\n\r\n    return new ECPointFp(this.curve, this.curve.fromBigInteger(x3), this.curve.fromBigInteger(y3), z3)\r\n  }\r\n\r\n  /**\r\n   * 自加\r\n   *\r\n   * 标准射影坐标系：\r\n   *\r\n   * λ1 = 3 * x1^2 + a * z1^2\r\n   * λ2 = 2 * y1 * z1\r\n   * λ3 = y1^2\r\n   * λ4 = λ3 * x1 * z1\r\n   * λ5 = λ2^2\r\n   * λ6 = λ1^2 − 8 * λ4\r\n   * x3 = λ2 * λ6\r\n   * y3 = λ1 * (4 * λ4 − λ6) − 2 * λ5 * λ3\r\n   * z3 = λ2 * λ5\r\n   */\r\n  twice() {\r\n    if (this.isInfinity()) return this\r\n    if (!this.y.toBigInteger().signum()) return this.curve.infinity\r\n\r\n    const x1 = this.x.toBigInteger()\r\n    const y1 = this.y.toBigInteger()\r\n    const z1 = this.z\r\n    const q = this.curve.q\r\n    const a = this.curve.a.toBigInteger()\r\n\r\n    const w1 = x1.square().multiply(THREE).add(a.multiply(z1.square())).mod(q)\r\n    const w2 = y1.shiftLeft(1).multiply(z1).mod(q)\r\n    const w3 = y1.square().mod(q)\r\n    const w4 = w3.multiply(x1).multiply(z1).mod(q)\r\n    const w5 = w2.square().mod(q)\r\n    const w6 = w1.square().subtract(w4.shiftLeft(3)).mod(q)\r\n\r\n    const x3 = w2.multiply(w6).mod(q)\r\n    const y3 = w1.multiply(w4.shiftLeft(2).subtract(w6)).subtract(w5.shiftLeft(1).multiply(w3)).mod(q)\r\n    const z3 = w2.multiply(w5).mod(q)\r\n\r\n    return new ECPointFp(this.curve, this.curve.fromBigInteger(x3), this.curve.fromBigInteger(y3), z3)\r\n  }\r\n\r\n  /**\r\n   * 倍点计算\r\n   */\r\n  multiply(k) {\r\n    if (this.isInfinity()) return this\r\n    if (!k.signum()) return this.curve.infinity\r\n\r\n    // 使用加减法\r\n    const k3 = k.multiply(THREE)\r\n    const neg = this.negate()\r\n    let Q = this\r\n\r\n    for (let i = k3.bitLength() - 2; i > 0; i--) {\r\n      Q = Q.twice()\r\n\r\n      const k3Bit = k3.testBit(i)\r\n      const kBit = k.testBit(i)\r\n\r\n      if (k3Bit !== kBit) {\r\n        Q = Q.add(k3Bit ? this : neg)\r\n      }\r\n    }\r\n\r\n    return Q\r\n  }\r\n}\r\n\r\n/**\r\n * 椭圆曲线 y^2 = x^3 + ax + b\r\n */\r\nclass ECCurveFp {\r\n  constructor(q, a, b) {\r\n    this.q = q\r\n    this.a = this.fromBigInteger(a)\r\n    this.b = this.fromBigInteger(b)\r\n    this.infinity = new ECPointFp(this, null, null) // 无穷远点\r\n  }\r\n\r\n  /**\r\n   * 判断两个椭圆曲线是否相等\r\n   */\r\n  equals(other) {\r\n    if (other === this) return true\r\n    return (this.q.equals(other.q) && this.a.equals(other.a) && this.b.equals(other.b))\r\n  }\r\n\r\n  /**\r\n   * 生成椭圆曲线域元素\r\n   */\r\n  fromBigInteger(x) {\r\n    return new ECFieldElementFp(this.q, x)\r\n  }\r\n\r\n  /**\r\n   * 解析 16 进制串为椭圆曲线点\r\n   */\r\n  decodePointHex(s) {\r\n    switch (parseInt(s.substr(0, 2), 16)) {\r\n      // 第一个字节\r\n      case 0:\r\n        return this.infinity\r\n      case 2:\r\n      case 3:\r\n        // 压缩\r\n        const x = this.fromBigInteger(new BigInteger(s.substr(2), 16))\r\n        // 对 p ≡ 3 (mod4)，即存在正整数 u，使得 p = 4u + 3\r\n        // 计算 y = (√ (x^3 + ax + b) % p)^(u + 1) modp\r\n        let y = this.fromBigInteger(x.multiply(x.square()).add(\r\n          x.multiply(this.a)\r\n        ).add(this.b).toBigInteger()\r\n          .modPow(\r\n            this.q.divide(new BigInteger('4')).add(BigInteger.ONE), this.q\r\n          ))\r\n        // 算出结果 2 进制最后 1 位不等于第 1 个字节减 2 则取反\r\n        if (!y.toBigInteger().mod(TWO).equals(new BigInteger(s.substr(0, 2), 16).subtract(TWO))) {\r\n          y = y.negate()\r\n        }\r\n        return new ECPointFp(this, x, y)\r\n      case 4:\r\n      case 6:\r\n      case 7:\r\n        const len = (s.length - 2) / 2\r\n        const xHex = s.substr(2, len)\r\n        const yHex = s.substr(len + 2, len)\r\n\r\n        return new ECPointFp(this, this.fromBigInteger(new BigInteger(xHex, 16)), this.fromBigInteger(new BigInteger(yHex, 16)))\r\n      default:\r\n        // 不支持\r\n        return null\r\n    }\r\n  }\r\n}\r\n\r\nmodule.exports = {\r\n  ECPointFp,\r\n  ECCurveFp,\r\n}\r\n", "/* eslint-disable no-bitwise, no-mixed-operators, no-use-before-define, max-len */\nconst {BigInteger, SecureRandom} = require('jsbn')\nconst {ECCurveFp} = require('./ec')\n\nconst rng = new SecureRandom()\nconst {curve, G, n} = generateEcparam()\n\n/**\n * 获取公共椭圆曲线\n */\nfunction getGlobalCurve() {\n  return curve\n}\n\n/**\n * 生成ecparam\n */\nfunction generateEcparam() {\n  // 椭圆曲线\n  const p = new BigInteger('FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF', 16)\n  const a = new BigInteger('FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC', 16)\n  const b = new BigInteger('28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93', 16)\n  const curve = new ECCurveFp(p, a, b)\n\n  // 基点\n  const gxHex = '32C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7'\n  const gyHex = 'BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0'\n  const G = curve.decodePointHex('04' + gxHex + gyHex)\n\n  const n = new BigInteger('FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFF7203DF6B21C6052B53BBF40939D54123', 16)\n\n  return {curve, G, n}\n}\n\n/**\n * 生成密钥对：publicKey = privateKey * G\n */\nfunction generateKeyPairHex(a, b, c) {\n  const random = a ? new BigInteger(a, b, c) : new BigInteger(n.bitLength(), rng)\n  const d = random.mod(n.subtract(BigInteger.ONE)).add(BigInteger.ONE) // 随机数\n  const privateKey = leftPad(d.toString(16), 64)\n\n  const P = G.multiply(d) // P = dG，p 为公钥，d 为私钥\n  const Px = leftPad(P.getX().toBigInteger().toString(16), 64)\n  const Py = leftPad(P.getY().toBigInteger().toString(16), 64)\n  const publicKey = '04' + Px + Py\n\n  return {privateKey, publicKey}\n}\n\n/**\n * 生成压缩公钥\n */\nfunction compressPublicKeyHex(s) {\n  if (s.length !== 130) throw new Error('Invalid public key to compress')\n\n  const len = (s.length - 2) / 2\n  const xHex = s.substr(2, len)\n  const y = new BigInteger(s.substr(len + 2, len), 16)\n\n  let prefix = '03'\n  if (y.mod(new BigInteger('2')).equals(BigInteger.ZERO)) prefix = '02'\n\n  return prefix + xHex\n}\n\n/**\n * utf8串转16进制串\n */\nfunction utf8ToHex(input) {\n  input = unescape(encodeURIComponent(input))\n\n  const length = input.length\n\n  // 转换到字数组\n  const words = []\n  for (let i = 0; i < length; i++) {\n    words[i >>> 2] |= (input.charCodeAt(i) & 0xff) << (24 - (i % 4) * 8)\n  }\n\n  // 转换到16进制\n  const hexChars = []\n  for (let i = 0; i < length; i++) {\n    const bite = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff\n    hexChars.push((bite >>> 4).toString(16))\n    hexChars.push((bite & 0x0f).toString(16))\n  }\n\n  return hexChars.join('')\n}\n\n/**\n * 补全16进制字符串\n */\nfunction leftPad(input, num) {\n  if (input.length >= num) return input\n\n  return (new Array(num - input.length + 1)).join('0') + input\n}\n\n/**\n * 转成16进制串\n */\nfunction arrayToHex(arr) {\n  return arr.map(item => {\n    item = item.toString(16)\n    return item.length === 1 ? '0' + item : item\n  }).join('')\n}\n\n/**\n * 转成utf8串\n */\nfunction arrayToUtf8(arr) {\n  const words = []\n  let j = 0\n  for (let i = 0; i < arr.length * 2; i += 2) {\n    words[i >>> 3] |= parseInt(arr[j], 10) << (24 - (i % 8) * 4)\n    j++\n  }\n\n  try {\n    const latin1Chars = []\n\n    for (let i = 0; i < arr.length; i++) {\n      const bite = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff\n      latin1Chars.push(String.fromCharCode(bite))\n    }\n\n    return decodeURIComponent(escape(latin1Chars.join('')))\n  } catch (e) {\n    throw new Error('Malformed UTF-8 data')\n  }\n}\n\n/**\n * 转成字节数组\n */\nfunction hexToArray(hexStr) {\n  const words = []\n  let hexStrLength = hexStr.length\n\n  if (hexStrLength % 2 !== 0) {\n    hexStr = leftPad(hexStr, hexStrLength + 1)\n  }\n\n  hexStrLength = hexStr.length\n\n  for (let i = 0; i < hexStrLength; i += 2) {\n    words.push(parseInt(hexStr.substr(i, 2), 16))\n  }\n  return words\n}\n\n/**\n * 验证公钥是否为椭圆曲线上的点\n */\nfunction verifyPublicKey(publicKey) {\n  const point = curve.decodePointHex(publicKey)\n  if (!point) return false\n\n  const x = point.getX()\n  const y = point.getY()\n\n  // 验证 y^2 是否等于 x^3 + ax + b\n  return y.square().equals(x.multiply(x.square()).add(x.multiply(curve.a)).add(curve.b))\n}\n\n/**\n * 验证公钥是否等价，等价返回true\n */\nfunction comparePublicKeyHex(publicKey1, publicKey2) {\n  const point1 = curve.decodePointHex(publicKey1)\n  if (!point1) return false\n\n  const point2 = curve.decodePointHex(publicKey2)\n  if (!point2) return false\n\n  return point1.equals(point2)\n}\n\nmodule.exports = {\n  getGlobalCurve,\n  generateEcparam,\n  generateKeyPairHex,\n  compressPublicKeyHex,\n  utf8ToHex,\n  leftPad,\n  arrayToHex,\n  arrayToUtf8,\n  hexToArray,\n  verifyPublicKey,\n  comparePublicKeyHex,\n}\n", "// 消息扩展\nconst W = new Uint32Array(68)\nconst M = new Uint32Array(64) // W'\n\n/**\n * 循环左移\n */\nfunction rotl(x, n) {\n  const s = n & 31\n  return (x << s) | (x >>> (32 - s))\n}\n\n/**\n * 二进制异或运算\n */\nfunction xor(x, y) {\n  const result = []\n  for (let i = x.length - 1; i >= 0; i--) result[i] = (x[i] ^ y[i]) & 0xff\n  return result\n}\n\n/**\n * 压缩函数中的置换函数 P0(X) = X xor (X <<< 9) xor (X <<< 17)\n */\nfunction P0(X) {\n  return (X ^ rotl(X, 9)) ^ rotl(X, 17)\n}\n\n/**\n * 消息扩展中的置换函数 P1(X) = X xor (X <<< 15) xor (X <<< 23)\n */\nfunction P1(X) {\n  return (X ^ rotl(X, 15)) ^ rotl(X, 23)\n}\n\n/**\n * sm3 本体\n */\nfunction sm3(array) {\n  let len = array.length * 8\n\n  // k 是满足 len + 1 + k = 448mod512 的最小的非负整数\n  let k = len % 512\n  // 如果 448 <= (512 % len) < 512，需要多补充 (len % 448) 比特'0'以满足总比特长度为512的倍数\n  k = k >= 448 ? 512 - (k % 448) - 1 : 448 - k - 1\n\n  // 填充\n  const kArr = new Array((k - 7) / 8)\n  const lenArr = new Array(8)\n  for (let i = 0, len = kArr.length; i < len; i++) kArr[i] = 0\n  for (let i = 0, len = lenArr.length; i < len; i++) lenArr[i] = 0\n  len = len.toString(2)\n  for (let i = 7; i >= 0; i--) {\n    if (len.length > 8) {\n      const start = len.length - 8\n      lenArr[i] = parseInt(len.substr(start), 2)\n      len = len.substr(0, start)\n    } else if (len.length > 0) {\n      lenArr[i] = parseInt(len, 2)\n      len = ''\n    }\n  }\n  const m = new Uint8Array([...array, 0x80, ...kArr, ...lenArr])\n  const dataView = new DataView(m.buffer, 0)\n\n  // 迭代压缩\n  const n = m.length / 64\n  const V = new Uint32Array([0x7380166f, 0x4914b2b9, 0x172442d7, 0xda8a0600, 0xa96f30bc, 0x163138aa, 0xe38dee4d, 0xb0fb0e4e])\n  for (let i = 0; i < n; i++) {\n    W.fill(0)\n    M.fill(0)\n\n    // 将消息分组B划分为 16 个字 W0， W1，……，W15\n    const start = 16 * i\n    for (let j = 0; j < 16; j++) {\n      W[j] = dataView.getUint32((start + j) * 4, false)\n    }\n\n    // W16 ～ W67：W[j] <- P1(W[j−16] xor W[j−9] xor (W[j−3] <<< 15)) xor (W[j−13] <<< 7) xor W[j−6]\n    for (let j = 16; j < 68; j++) {\n      W[j] = (P1((W[j - 16] ^ W[j - 9]) ^ rotl(W[j - 3], 15)) ^ rotl(W[j - 13], 7)) ^ W[j - 6]\n    }\n\n    // W′0 ～ W′63：W′[j] = W[j] xor W[j+4]\n    for (let j = 0; j < 64; j++) {\n      M[j] = W[j] ^ W[j + 4]\n    }\n\n    // 压缩\n    const T1 = 0x79cc4519\n    const T2 = 0x7a879d8a\n    // 字寄存器\n    let A = V[0]\n    let B = V[1]\n    let C = V[2]\n    let D = V[3]\n    let E = V[4]\n    let F = V[5]\n    let G = V[6]\n    let H = V[7]\n    // 中间变量\n    let SS1\n    let SS2\n    let TT1\n    let TT2\n    let T\n    for (let j = 0; j < 64; j++) {\n      T = j >= 0 && j <= 15 ? T1 : T2\n      SS1 = rotl(rotl(A, 12) + E + rotl(T, j), 7)\n      SS2 = SS1 ^ rotl(A, 12)\n\n      TT1 = (j >= 0 && j <= 15 ? ((A ^ B) ^ C) : (((A & B) | (A & C)) | (B & C))) + D + SS2 + M[j]\n      TT2 = (j >= 0 && j <= 15 ? ((E ^ F) ^ G) : ((E & F) | ((~E) & G))) + H + SS1 + W[j]\n\n      D = C\n      C = rotl(B, 9)\n      B = A\n      A = TT1\n      H = G\n      G = rotl(F, 19)\n      F = E\n      E = P0(TT2)\n    }\n\n    V[0] ^= A\n    V[1] ^= B\n    V[2] ^= C\n    V[3] ^= D\n    V[4] ^= E\n    V[5] ^= F\n    V[6] ^= G\n    V[7] ^= H\n  }\n\n  // 转回 uint8\n  const result = []\n  for (let i = 0, len = V.length; i < len; i++) {\n    const word = V[i]\n    result.push((word & 0xff000000) >>> 24, (word & 0xff0000) >>> 16, (word & 0xff00) >>> 8, word & 0xff)\n  }\n\n  return result\n}\n\n/**\n * hmac 实现\n */\nconst blockLen = 64\nconst iPad = new Uint8Array(blockLen)\nconst oPad = new Uint8Array(blockLen)\nfor (let i = 0; i < blockLen; i++) {\n  iPad[i] = 0x36\n  oPad[i] = 0x5c\n}\nfunction hmac(input, key) {\n  // 密钥填充\n  if (key.length > blockLen) key = sm3(key)\n  while (key.length < blockLen) key.push(0)\n\n  const iPadKey = xor(key, iPad)\n  const oPadKey = xor(key, oPad)\n\n  const hash = sm3([...iPadKey, ...input])\n  return sm3([...oPadKey, ...hash])\n}\n\nmodule.exports = {\n  sm3,\n  hmac,\n}\n", "/* eslint-disable no-use-before-define */\nconst {BigInteger} = require('jsbn')\nconst {encodeDer, decodeDer} = require('./asn1')\nconst _ = require('./utils')\nconst sm3 = require('./sm3').sm3\n\nconst {G, curve, n} = _.generateEcparam()\nconst C1C2C3 = 0\n\n/**\n * 加密\n */\nfunction doEncrypt(msg, publicKey, cipherMode = 1) {\n  msg = typeof msg === 'string' ? _.hexToArray(_.utf8ToHex(msg)) : Array.prototype.slice.call(msg)\n  publicKey = _.getGlobalCurve().decodePointHex(publicKey) // 先将公钥转成点\n\n  const keypair = _.generateKeyPairHex()\n  const k = new BigInteger(keypair.privateKey, 16) // 随机数 k\n\n  // c1 = k * G\n  let c1 = keypair.publicKey\n  if (c1.length > 128) c1 = c1.substr(c1.length - 128)\n\n  // (x2, y2) = k * publicKey\n  const p = publicKey.multiply(k)\n  const x2 = _.hexToArray(_.leftPad(p.getX().toBigInteger().toRadix(16), 64))\n  const y2 = _.hexToArray(_.leftPad(p.getY().toBigInteger().toRadix(16), 64))\n\n  // c3 = hash(x2 || msg || y2)\n  const c3 = _.arrayToHex(sm3([].concat(x2, msg, y2)))\n\n  let ct = 1\n  let offset = 0\n  let t = [] // 256 位\n  const z = [].concat(x2, y2)\n  const nextT = () => {\n    // (1) Hai = hash(z || ct)\n    // (2) ct++\n    t = sm3([...z, ct >> 24 & 0x00ff, ct >> 16 & 0x00ff, ct >> 8 & 0x00ff, ct & 0x00ff])\n    ct++\n    offset = 0\n  }\n  nextT() // 先生成 Ha1\n\n  for (let i = 0, len = msg.length; i < len; i++) {\n    // t = Ha1 || Ha2 || Ha3 || Ha4\n    if (offset === t.length) nextT()\n\n    // c2 = msg ^ t\n    msg[i] ^= t[offset++] & 0xff\n  }\n  const c2 = _.arrayToHex(msg)\n\n  return cipherMode === C1C2C3 ? c1 + c2 + c3 : c1 + c3 + c2\n}\n\n/**\n * 解密\n */\nfunction doDecrypt(encryptData, privateKey, cipherMode = 1, {\n  output = 'string',\n} = {}) {\n  privateKey = new BigInteger(privateKey, 16)\n\n  let c3 = encryptData.substr(128, 64)\n  let c2 = encryptData.substr(128 + 64)\n\n  if (cipherMode === C1C2C3) {\n    c3 = encryptData.substr(encryptData.length - 64)\n    c2 = encryptData.substr(128, encryptData.length - 128 - 64)\n  }\n\n  const msg = _.hexToArray(c2)\n  const c1 = _.getGlobalCurve().decodePointHex('04' + encryptData.substr(0, 128))\n\n  const p = c1.multiply(privateKey)\n  const x2 = _.hexToArray(_.leftPad(p.getX().toBigInteger().toRadix(16), 64))\n  const y2 = _.hexToArray(_.leftPad(p.getY().toBigInteger().toRadix(16), 64))\n\n  let ct = 1\n  let offset = 0\n  let t = [] // 256 位\n  const z = [].concat(x2, y2)\n  const nextT = () => {\n    // (1) Hai = hash(z || ct)\n    // (2) ct++\n    t = sm3([...z, ct >> 24 & 0x00ff, ct >> 16 & 0x00ff, ct >> 8 & 0x00ff, ct & 0x00ff])\n    ct++\n    offset = 0\n  }\n  nextT() // 先生成 Ha1\n\n  for (let i = 0, len = msg.length; i < len; i++) {\n    // t = Ha1 || Ha2 || Ha3 || Ha4\n    if (offset === t.length) nextT()\n\n    // c2 = msg ^ t\n    msg[i] ^= t[offset++] & 0xff\n  }\n\n  // c3 = hash(x2 || msg || y2)\n  const checkC3 = _.arrayToHex(sm3([].concat(x2, msg, y2)))\n\n  if (checkC3 === c3.toLowerCase()) {\n    return output === 'array' ? msg : _.arrayToUtf8(msg)\n  } else {\n    return output === 'array' ? [] : ''\n  }\n}\n\n/**\n * 签名\n */\nfunction doSignature(msg, privateKey, {\n  pointPool, der, hash, publicKey, userId\n} = {}) {\n  let hashHex = typeof msg === 'string' ? _.utf8ToHex(msg) : _.arrayToHex(msg)\n\n  if (hash) {\n    // sm3杂凑\n    publicKey = publicKey || getPublicKeyFromPrivateKey(privateKey)\n    hashHex = getHash(hashHex, publicKey, userId)\n  }\n\n  const dA = new BigInteger(privateKey, 16)\n  const e = new BigInteger(hashHex, 16)\n\n  // k\n  let k = null\n  let r = null\n  let s = null\n\n  do {\n    do {\n      let point\n      if (pointPool && pointPool.length) {\n        point = pointPool.pop()\n      } else {\n        point = getPoint()\n      }\n      k = point.k\n\n      // r = (e + x1) mod n\n      r = e.add(point.x1).mod(n)\n    } while (r.equals(BigInteger.ZERO) || r.add(k).equals(n))\n\n    // s = ((1 + dA)^-1 * (k - r * dA)) mod n\n    s = dA.add(BigInteger.ONE).modInverse(n).multiply(k.subtract(r.multiply(dA))).mod(n)\n  } while (s.equals(BigInteger.ZERO))\n\n  if (der) return encodeDer(r, s) // asn.1 der 编码\n\n  return _.leftPad(r.toString(16), 64) + _.leftPad(s.toString(16), 64)\n}\n\n/**\n * 验签\n */\nfunction doVerifySignature(msg, signHex, publicKey, {der, hash, userId} = {}) {\n  let hashHex = typeof msg === 'string' ? _.utf8ToHex(msg) : _.arrayToHex(msg)\n\n  if (hash) {\n    // sm3杂凑\n    hashHex = getHash(hashHex, publicKey, userId)\n  }\n\n  let r; let\n    s\n  if (der) {\n    const decodeDerObj = decodeDer(signHex) // asn.1 der 解码\n    r = decodeDerObj.r\n    s = decodeDerObj.s\n  } else {\n    r = new BigInteger(signHex.substring(0, 64), 16)\n    s = new BigInteger(signHex.substring(64), 16)\n  }\n\n  const PA = curve.decodePointHex(publicKey)\n  const e = new BigInteger(hashHex, 16)\n\n  // t = (r + s) mod n\n  const t = r.add(s).mod(n)\n\n  if (t.equals(BigInteger.ZERO)) return false\n\n  // x1y1 = s * G + t * PA\n  const x1y1 = G.multiply(s).add(PA.multiply(t))\n\n  // R = (e + x1) mod n\n  const R = e.add(x1y1.getX().toBigInteger()).mod(n)\n\n  return r.equals(R)\n}\n\n/**\n * sm3杂凑算法\n */\nfunction getHash(hashHex, publicKey, userId = '1234567812345678') {\n  // z = hash(entl || userId || a || b || gx || gy || px || py)\n  userId = _.utf8ToHex(userId)\n  const a = _.leftPad(G.curve.a.toBigInteger().toRadix(16), 64)\n  const b = _.leftPad(G.curve.b.toBigInteger().toRadix(16), 64)\n  const gx = _.leftPad(G.getX().toBigInteger().toRadix(16), 64)\n  const gy = _.leftPad(G.getY().toBigInteger().toRadix(16), 64)\n  let px\n  let py\n  if (publicKey.length === 128) {\n    px = publicKey.substr(0, 64)\n    py = publicKey.substr(64, 64)\n  } else {\n    const point = G.curve.decodePointHex(publicKey)\n    px = _.leftPad(point.getX().toBigInteger().toRadix(16), 64)\n    py = _.leftPad(point.getY().toBigInteger().toRadix(16), 64)\n  }\n  const data = _.hexToArray(userId + a + b + gx + gy + px + py)\n\n  const entl = userId.length * 4\n  data.unshift(entl & 0x00ff)\n  data.unshift(entl >> 8 & 0x00ff)\n\n  const z = sm3(data)\n\n  // e = hash(z || msg)\n  return _.arrayToHex(sm3(z.concat(_.hexToArray(hashHex))))\n}\n\n/**\n * 计算公钥\n */\nfunction getPublicKeyFromPrivateKey(privateKey) {\n  const PA = G.multiply(new BigInteger(privateKey, 16))\n  const x = _.leftPad(PA.getX().toBigInteger().toString(16), 64)\n  const y = _.leftPad(PA.getY().toBigInteger().toString(16), 64)\n  return '04' + x + y\n}\n\n/**\n * 获取椭圆曲线点\n */\nfunction getPoint() {\n  const keypair = _.generateKeyPairHex()\n  const PA = curve.decodePointHex(keypair.publicKey)\n\n  keypair.k = new BigInteger(keypair.privateKey, 16)\n  keypair.x1 = PA.getX().toBigInteger()\n\n  return keypair\n}\n\nmodule.exports = {\n  generateKeyPairHex: _.generateKeyPairHex,\n  compressPublicKeyHex: _.compressPublicKeyHex,\n  comparePublicKeyHex: _.comparePublicKeyHex,\n  doEncrypt,\n  doDecrypt,\n  doSignature,\n  doVerifySignature,\n  getPublicKeyFromPrivateKey,\n  getPoint,\n  verifyPublicKey: _.verifyPublicKey,\n}\n", "const {sm3, hmac} = require('../sm2/sm3')\n\n/**\n * 补全16进制字符串\n */\nfunction leftPad(input, num) {\n  if (input.length >= num) return input\n\n  return (new Array(num - input.length + 1)).join('0') + input\n}\n\n/**\n * 字节数组转 16 进制串\n */\nfunction ArrayToHex(arr) {\n  return arr.map(item => {\n    item = item.toString(16)\n    return item.length === 1 ? '0' + item : item\n  }).join('')\n}\n\n/**\n * 转成字节数组\n */\nfunction hexToArray(hexStr) {\n  const words = []\n  let hexStrLength = hexStr.length\n\n  if (hexStrLength % 2 !== 0) {\n    hexStr = leftPad(hexStr, hexStrLength + 1)\n  }\n\n  hexStrLength = hexStr.length\n\n  for (let i = 0; i < hexStrLength; i += 2) {\n    words.push(parseInt(hexStr.substr(i, 2), 16))\n  }\n  return words\n}\n\n/**\n * utf8 串转字节数组\n */\nfunction utf8ToArray(str) {\n  const arr = []\n\n  for (let i = 0, len = str.length; i < len; i++) {\n    const point = str.codePointAt(i)\n\n    if (point <= 0x007f) {\n      // 单字节，标量值：00000000 00000000 0zzzzzzz\n      arr.push(point)\n    } else if (point <= 0x07ff) {\n      // 双字节，标量值：00000000 00000yyy yyzzzzzz\n      arr.push(0xc0 | (point >>> 6)) // 110yyyyy（0xc0-0xdf）\n      arr.push(0x80 | (point & 0x3f)) // 10zzzzzz（0x80-0xbf）\n    } else if (point <= 0xD7FF || (point >= 0xE000 && point <= 0xFFFF)) {\n      // 三字节：标量值：00000000 xxxxyyyy yyzzzzzz\n      arr.push(0xe0 | (point >>> 12)) // 1110xxxx（0xe0-0xef）\n      arr.push(0x80 | ((point >>> 6) & 0x3f)) // 10yyyyyy（0x80-0xbf）\n      arr.push(0x80 | (point & 0x3f)) // 10zzzzzz（0x80-0xbf）\n    } else if (point >= 0x010000 && point <= 0x10FFFF) {\n      // 四字节：标量值：000wwwxx xxxxyyyy yyzzzzzz\n      i++\n      arr.push((0xf0 | (point >>> 18) & 0x1c)) // 11110www（0xf0-0xf7）\n      arr.push((0x80 | ((point >>> 12) & 0x3f))) // 10xxxxxx（0x80-0xbf）\n      arr.push((0x80 | ((point >>> 6) & 0x3f))) // 10yyyyyy（0x80-0xbf）\n      arr.push((0x80 | (point & 0x3f))) // 10zzzzzz（0x80-0xbf）\n    } else {\n      // 五、六字节，暂时不支持\n      arr.push(point)\n      throw new Error('input is not supported')\n    }\n  }\n\n  return arr\n}\n\nmodule.exports = function (input, options) {\n  input = typeof input === 'string' ? utf8ToArray(input) : Array.prototype.slice.call(input)\n\n  if (options) {\n    const mode = options.mode || 'hmac'\n    if (mode !== 'hmac') throw new Error('invalid mode')\n\n    let key = options.key\n    if (!key) throw new Error('invalid key')\n\n    key = typeof key === 'string' ? hexToArray(key) : Array.prototype.slice.call(key)\n    return ArrayToHex(hmac(input, key))\n  }\n\n  return ArrayToHex(sm3(input))\n}\n", "/* eslint-disable no-bitwise, no-mixed-operators, complexity */\r\nconst DECRYPT = 0\r\nconst ROUND = 32\r\nconst BLOCK = 16\r\n\r\nconst Sbox = [\r\n  0xd6, 0x90, 0xe9, 0xfe, 0xcc, 0xe1, 0x3d, 0xb7, 0x16, 0xb6, 0x14, 0xc2, 0x28, 0xfb, 0x2c, 0x05,\r\n  0x2b, 0x67, 0x9a, 0x76, 0x2a, 0xbe, 0x04, 0xc3, 0xaa, 0x44, 0x13, 0x26, 0x49, 0x86, 0x06, 0x99,\r\n  0x9c, 0x42, 0x50, 0xf4, 0x91, 0xef, 0x98, 0x7a, 0x33, 0x54, 0x0b, 0x43, 0xed, 0xcf, 0xac, 0x62,\r\n  0xe4, 0xb3, 0x1c, 0xa9, 0xc9, 0x08, 0xe8, 0x95, 0x80, 0xdf, 0x94, 0xfa, 0x75, 0x8f, 0x3f, 0xa6,\r\n  0x47, 0x07, 0xa7, 0xfc, 0xf3, 0x73, 0x17, 0xba, 0x83, 0x59, 0x3c, 0x19, 0xe6, 0x85, 0x4f, 0xa8,\r\n  0x68, 0x6b, 0x81, 0xb2, 0x71, 0x64, 0xda, 0x8b, 0xf8, 0xeb, 0x0f, 0x4b, 0x70, 0x56, 0x9d, 0x35,\r\n  0x1e, 0x24, 0x0e, 0x5e, 0x63, 0x58, 0xd1, 0xa2, 0x25, 0x22, 0x7c, 0x3b, 0x01, 0x21, 0x78, 0x87,\r\n  0xd4, 0x00, 0x46, 0x57, 0x9f, 0xd3, 0x27, 0x52, 0x4c, 0x36, 0x02, 0xe7, 0xa0, 0xc4, 0xc8, 0x9e,\r\n  0xea, 0xbf, 0x8a, 0xd2, 0x40, 0xc7, 0x38, 0xb5, 0xa3, 0xf7, 0xf2, 0xce, 0xf9, 0x61, 0x15, 0xa1,\r\n  0xe0, 0xae, 0x5d, 0xa4, 0x9b, 0x34, 0x1a, 0x55, 0xad, 0x93, 0x32, 0x30, 0xf5, 0x8c, 0xb1, 0xe3,\r\n  0x1d, 0xf6, 0xe2, 0x2e, 0x82, 0x66, 0xca, 0x60, 0xc0, 0x29, 0x23, 0xab, 0x0d, 0x53, 0x4e, 0x6f,\r\n  0xd5, 0xdb, 0x37, 0x45, 0xde, 0xfd, 0x8e, 0x2f, 0x03, 0xff, 0x6a, 0x72, 0x6d, 0x6c, 0x5b, 0x51,\r\n  0x8d, 0x1b, 0xaf, 0x92, 0xbb, 0xdd, 0xbc, 0x7f, 0x11, 0xd9, 0x5c, 0x41, 0x1f, 0x10, 0x5a, 0xd8,\r\n  0x0a, 0xc1, 0x31, 0x88, 0xa5, 0xcd, 0x7b, 0xbd, 0x2d, 0x74, 0xd0, 0x12, 0xb8, 0xe5, 0xb4, 0xb0,\r\n  0x89, 0x69, 0x97, 0x4a, 0x0c, 0x96, 0x77, 0x7e, 0x65, 0xb9, 0xf1, 0x09, 0xc5, 0x6e, 0xc6, 0x84,\r\n  0x18, 0xf0, 0x7d, 0xec, 0x3a, 0xdc, 0x4d, 0x20, 0x79, 0xee, 0x5f, 0x3e, 0xd7, 0xcb, 0x39, 0x48\r\n]\r\n\r\nconst CK = [\r\n  0x00070e15, 0x1c232a31, 0x383f464d, 0x545b6269,\r\n  0x70777e85, 0x8c939aa1, 0xa8afb6bd, 0xc4cbd2d9,\r\n  0xe0e7eef5, 0xfc030a11, 0x181f262d, 0x343b4249,\r\n  0x50575e65, 0x6c737a81, 0x888f969d, 0xa4abb2b9,\r\n  0xc0c7ced5, 0xdce3eaf1, 0xf8ff060d, 0x141b2229,\r\n  0x30373e45, 0x4c535a61, 0x686f767d, 0x848b9299,\r\n  0xa0a7aeb5, 0xbcc3cad1, 0xd8dfe6ed, 0xf4fb0209,\r\n  0x10171e25, 0x2c333a41, 0x484f565d, 0x646b7279\r\n]\r\n\r\n/**\r\n * 16 进制串转字节数组\r\n */\r\nfunction hexToArray(str) {\r\n  const arr = []\r\n  for (let i = 0, len = str.length; i < len; i += 2) {\r\n    arr.push(parseInt(str.substr(i, 2), 16))\r\n  }\r\n  return arr\r\n}\r\n\r\n/**\r\n * 字节数组转 16 进制串\r\n */\r\nfunction ArrayToHex(arr) {\r\n  return arr.map(item => {\r\n    item = item.toString(16)\r\n    return item.length === 1 ? '0' + item : item\r\n  }).join('')\r\n}\r\n\r\n/**\r\n * utf8 串转字节数组\r\n */\r\nfunction utf8ToArray(str) {\r\n  const arr = []\r\n\r\n  for (let i = 0, len = str.length; i < len; i++) {\r\n    const point = str.codePointAt(i)\r\n\r\n    if (point <= 0x007f) {\r\n      // 单字节，标量值：00000000 00000000 0zzzzzzz\r\n      arr.push(point)\r\n    } else if (point <= 0x07ff) {\r\n      // 双字节，标量值：00000000 00000yyy yyzzzzzz\r\n      arr.push(0xc0 | (point >>> 6)) // 110yyyyy（0xc0-0xdf）\r\n      arr.push(0x80 | (point & 0x3f)) // 10zzzzzz（0x80-0xbf）\r\n    } else if (point <= 0xD7FF || (point >= 0xE000 && point <= 0xFFFF)) {\r\n      // 三字节：标量值：00000000 xxxxyyyy yyzzzzzz\r\n      arr.push(0xe0 | (point >>> 12)) // 1110xxxx（0xe0-0xef）\r\n      arr.push(0x80 | ((point >>> 6) & 0x3f)) // 10yyyyyy（0x80-0xbf）\r\n      arr.push(0x80 | (point & 0x3f)) // 10zzzzzz（0x80-0xbf）\r\n    } else if (point >= 0x010000 && point <= 0x10FFFF) {\r\n      // 四字节：标量值：000wwwxx xxxxyyyy yyzzzzzz\r\n      i++\r\n      arr.push((0xf0 | (point >>> 18) & 0x1c)) // 11110www（0xf0-0xf7）\r\n      arr.push((0x80 | ((point >>> 12) & 0x3f))) // 10xxxxxx（0x80-0xbf）\r\n      arr.push((0x80 | ((point >>> 6) & 0x3f))) // 10yyyyyy（0x80-0xbf）\r\n      arr.push((0x80 | (point & 0x3f))) // 10zzzzzz（0x80-0xbf）\r\n    } else {\r\n      // 五、六字节，暂时不支持\r\n      arr.push(point)\r\n      throw new Error('input is not supported')\r\n    }\r\n  }\r\n\r\n  return arr\r\n}\r\n\r\n/**\r\n * 字节数组转 utf8 串\r\n */\r\nfunction arrayToUtf8(arr) {\r\n  const str = []\r\n  for (let i = 0, len = arr.length; i < len; i++) {\r\n    if (arr[i] >= 0xf0 && arr[i] <= 0xf7) {\r\n      // 四字节\r\n      str.push(String.fromCodePoint(((arr[i] & 0x07) << 18) + ((arr[i + 1] & 0x3f) << 12) + ((arr[i + 2] & 0x3f) << 6) + (arr[i + 3] & 0x3f)))\r\n      i += 3\r\n    } else if (arr[i] >= 0xe0 && arr[i] <= 0xef) {\r\n      // 三字节\r\n      str.push(String.fromCodePoint(((arr[i] & 0x0f) << 12) + ((arr[i + 1] & 0x3f) << 6) + (arr[i + 2] & 0x3f)))\r\n      i += 2\r\n    } else if (arr[i] >= 0xc0 && arr[i] <= 0xdf) {\r\n      // 双字节\r\n      str.push(String.fromCodePoint(((arr[i] & 0x1f) << 6) + (arr[i + 1] & 0x3f)))\r\n      i++\r\n    } else {\r\n      // 单字节\r\n      str.push(String.fromCodePoint(arr[i]))\r\n    }\r\n  }\r\n\r\n  return str.join('')\r\n}\r\n\r\n/**\r\n * 32 比特循环左移\r\n */\r\nfunction rotl(x, n) {\r\n  const s = n & 31\r\n  return (x << s) | (x >>> (32 - s))\r\n}\r\n\r\n/**\r\n * 非线性变换\r\n */\r\nfunction byteSub(a) {\r\n  return (Sbox[a >>> 24 & 0xFF] & 0xFF) << 24 |\r\n    (Sbox[a >>> 16 & 0xFF] & 0xFF) << 16 |\r\n    (Sbox[a >>> 8 & 0xFF] & 0xFF) << 8 |\r\n    (Sbox[a & 0xFF] & 0xFF)\r\n}\r\n\r\n/**\r\n * 线性变换，加密/解密用\r\n */\r\nfunction l1(b) {\r\n  return b ^ rotl(b, 2) ^ rotl(b, 10) ^ rotl(b, 18) ^ rotl(b, 24)\r\n}\r\n\r\n/**\r\n * 线性变换，生成轮密钥用\r\n */\r\nfunction l2(b) {\r\n  return b ^ rotl(b, 13) ^ rotl(b, 23)\r\n}\r\n\r\n/**\r\n * 以一组 128 比特进行加密/解密操作\r\n */\r\nfunction sms4Crypt(input, output, roundKey) {\r\n  const x = new Array(4)\r\n\r\n  // 字节数组转成字数组（此处 1 字 = 32 比特）\r\n  const tmp = new Array(4)\r\n  for (let i = 0; i < 4; i++) {\r\n    tmp[0] = input[4 * i] & 0xff\r\n    tmp[1] = input[4 * i + 1] & 0xff\r\n    tmp[2] = input[4 * i + 2] & 0xff\r\n    tmp[3] = input[4 * i + 3] & 0xff\r\n    x[i] = tmp[0] << 24 | tmp[1] << 16 | tmp[2] << 8 | tmp[3]\r\n  }\r\n\r\n  // x[i + 4] = x[i] ^ l1(byteSub(x[i + 1] ^ x[i + 2] ^ x[i + 3] ^ roundKey[i]))\r\n  for (let r = 0, mid; r < 32; r += 4) {\r\n    mid = x[1] ^ x[2] ^ x[3] ^ roundKey[r + 0]\r\n    x[0] ^= l1(byteSub(mid)) // x[4]\r\n\r\n    mid = x[2] ^ x[3] ^ x[0] ^ roundKey[r + 1]\r\n    x[1] ^= l1(byteSub(mid)) // x[5]\r\n\r\n    mid = x[3] ^ x[0] ^ x[1] ^ roundKey[r + 2]\r\n    x[2] ^= l1(byteSub(mid)) // x[6]\r\n\r\n    mid = x[0] ^ x[1] ^ x[2] ^ roundKey[r + 3]\r\n    x[3] ^= l1(byteSub(mid)) // x[7]\r\n  }\r\n\r\n  // 反序变换\r\n  for (let j = 0; j < 16; j += 4) {\r\n    output[j] = x[3 - j / 4] >>> 24 & 0xff\r\n    output[j + 1] = x[3 - j / 4] >>> 16 & 0xff\r\n    output[j + 2] = x[3 - j / 4] >>> 8 & 0xff\r\n    output[j + 3] = x[3 - j / 4] & 0xff\r\n  }\r\n}\r\n\r\n/**\r\n * 密钥扩展算法\r\n */\r\nfunction sms4KeyExt(key, roundKey, cryptFlag) {\r\n  const x = new Array(4)\r\n\r\n  // 字节数组转成字数组（此处 1 字 = 32 比特）\r\n  const tmp = new Array(4)\r\n  for (let i = 0; i < 4; i++) {\r\n    tmp[0] = key[0 + 4 * i] & 0xff\r\n    tmp[1] = key[1 + 4 * i] & 0xff\r\n    tmp[2] = key[2 + 4 * i] & 0xff\r\n    tmp[3] = key[3 + 4 * i] & 0xff\r\n    x[i] = tmp[0] << 24 | tmp[1] << 16 | tmp[2] << 8 | tmp[3]\r\n  }\r\n\r\n  // 与系统参数做异或\r\n  x[0] ^= 0xa3b1bac6\r\n  x[1] ^= 0x56aa3350\r\n  x[2] ^= 0x677d9197\r\n  x[3] ^= 0xb27022dc\r\n\r\n  // roundKey[i] = x[i + 4] = x[i] ^ l2(byteSub(x[i + 1] ^ x[i + 2] ^ x[i + 3] ^ CK[i]))\r\n  for (let r = 0, mid; r < 32; r += 4) {\r\n    mid = x[1] ^ x[2] ^ x[3] ^ CK[r + 0]\r\n    roundKey[r + 0] = x[0] ^= l2(byteSub(mid)) // x[4]\r\n\r\n    mid = x[2] ^ x[3] ^ x[0] ^ CK[r + 1]\r\n    roundKey[r + 1] = x[1] ^= l2(byteSub(mid)) // x[5]\r\n\r\n    mid = x[3] ^ x[0] ^ x[1] ^ CK[r + 2]\r\n    roundKey[r + 2] = x[2] ^= l2(byteSub(mid)) // x[6]\r\n\r\n    mid = x[0] ^ x[1] ^ x[2] ^ CK[r + 3]\r\n    roundKey[r + 3] = x[3] ^= l2(byteSub(mid)) // x[7]\r\n  }\r\n\r\n  // 解密时使用反序的轮密钥\r\n  if (cryptFlag === DECRYPT) {\r\n    for (let r = 0, mid; r < 16; r++) {\r\n      mid = roundKey[r]\r\n      roundKey[r] = roundKey[31 - r]\r\n      roundKey[31 - r] = mid\r\n    }\r\n  }\r\n}\r\n\r\nfunction sm4(inArray, key, cryptFlag, {\r\n  padding = 'pkcs#7', mode, iv = [], output = 'string'\r\n} = {}) {\r\n  if (mode === 'cbc') {\r\n    // CBC 模式，默认走 ECB 模式\r\n    if (typeof iv === 'string') iv = hexToArray(iv)\r\n    if (iv.length !== (128 / 8)) {\r\n      // iv 不是 128 比特\r\n      throw new Error('iv is invalid')\r\n    }\r\n  }\r\n\r\n  // 检查 key\r\n  if (typeof key === 'string') key = hexToArray(key)\r\n  if (key.length !== (128 / 8)) {\r\n    // key 不是 128 比特\r\n    throw new Error('key is invalid')\r\n  }\r\n\r\n  // 检查输入\r\n  if (typeof inArray === 'string') {\r\n    if (cryptFlag !== DECRYPT) {\r\n      // 加密，输入为 utf8 串\r\n      inArray = utf8ToArray(inArray)\r\n    } else {\r\n      // 解密，输入为 16 进制串\r\n      inArray = hexToArray(inArray)\r\n    }\r\n  } else {\r\n    inArray = [...inArray]\r\n  }\r\n\r\n  // 新增填充，sm4 是 16 个字节一个分组，所以统一走到 pkcs#7\r\n  if ((padding === 'pkcs#5' || padding === 'pkcs#7') && cryptFlag !== DECRYPT) {\r\n    const paddingCount = BLOCK - inArray.length % BLOCK\r\n    for (let i = 0; i < paddingCount; i++) inArray.push(paddingCount)\r\n  }\r\n\r\n  // 生成轮密钥\r\n  const roundKey = new Array(ROUND)\r\n  sms4KeyExt(key, roundKey, cryptFlag)\r\n\r\n  const outArray = []\r\n  let lastVector = iv\r\n  let restLen = inArray.length\r\n  let point = 0\r\n  while (restLen >= BLOCK) {\r\n    const input = inArray.slice(point, point + 16)\r\n    const output = new Array(16)\r\n\r\n    if (mode === 'cbc') {\r\n      for (let i = 0; i < BLOCK; i++) {\r\n        if (cryptFlag !== DECRYPT) {\r\n          // 加密过程在组加密前进行异或\r\n          input[i] ^= lastVector[i]\r\n        }\r\n      }\r\n    }\r\n\r\n    sms4Crypt(input, output, roundKey)\r\n\r\n\r\n    for (let i = 0; i < BLOCK; i++) {\r\n      if (mode === 'cbc') {\r\n        if (cryptFlag === DECRYPT) {\r\n          // 解密过程在组解密后进行异或\r\n          output[i] ^= lastVector[i]\r\n        }\r\n      }\r\n\r\n      outArray[point + i] = output[i]\r\n    }\r\n\r\n    if (mode === 'cbc') {\r\n      if (cryptFlag !== DECRYPT) {\r\n        // 使用上一次输出作为加密向量\r\n        lastVector = output\r\n      } else {\r\n        // 使用上一次输入作为解密向量\r\n        lastVector = input\r\n      }\r\n    }\r\n\r\n    restLen -= BLOCK\r\n    point += BLOCK\r\n  }\r\n\r\n  // 去除填充，sm4 是 16 个字节一个分组，所以统一走到 pkcs#7\r\n  if ((padding === 'pkcs#5' || padding === 'pkcs#7') && cryptFlag === DECRYPT) {\r\n    const len = outArray.length\r\n    const paddingCount = outArray[len - 1]\r\n    for (let i = 1; i <= paddingCount; i++) {\r\n      if (outArray[len - i] !== paddingCount) throw new Error('padding is invalid')\r\n    }\r\n    outArray.splice(len - paddingCount, paddingCount)\r\n  }\r\n\r\n  // 调整输出\r\n  if (output !== 'array') {\r\n    if (cryptFlag !== DECRYPT) {\r\n      // 加密，输出转 16 进制串\r\n      return ArrayToHex(outArray)\r\n    } else {\r\n      // 解密，输出转 utf8 串\r\n      return arrayToUtf8(outArray)\r\n    }\r\n  } else {\r\n    return outArray\r\n  }\r\n}\r\n\r\nmodule.exports = {\r\n  encrypt(inArray, key, options) {\r\n    return sm4(inArray, key, 1, options)\r\n  },\r\n  decrypt(inArray, key, options) {\r\n    return sm4(inArray, key, 0, options)\r\n  }\r\n}\r\n", "module.exports = {\n  sm2: require('./sm2/index'),\n  sm3: require('./sm3/index'),\n  sm4: require('./sm4/index'),\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,KAAC,WAAU;AASP,UAAI;AAGJ,UAAI,SAAS;AACb,UAAI,QAAS,SAAO,aAAW;AAG/B,eAAS,WAAW,GAAE,GAAE,GAAG;AACzB,YAAG,KAAK;AACN,cAAG,YAAY,OAAO,EAAG,MAAK,WAAW,GAAE,GAAE,CAAC;AAAA,mBACtC,KAAK,QAAQ,YAAY,OAAO,EAAG,MAAK,WAAW,GAAE,GAAG;AAAA,cAC3D,MAAK,WAAW,GAAE,CAAC;AAAA,MAC5B;AAGA,eAAS,MAAM;AAAE,eAAO,IAAI,WAAW,IAAI;AAAA,MAAG;AAU9C,eAAS,IAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAG;AACxB,eAAM,EAAE,KAAK,GAAG;AACd,cAAI,IAAI,IAAE,KAAK,GAAG,IAAE,EAAE,CAAC,IAAE;AACzB,cAAI,KAAK,MAAM,IAAE,QAAS;AAC1B,YAAE,GAAG,IAAI,IAAE;AAAA,QACb;AACA,eAAO;AAAA,MACT;AAIA,eAAS,IAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAG;AACxB,YAAI,KAAK,IAAE,OAAQ,KAAK,KAAG;AAC3B,eAAM,EAAE,KAAK,GAAG;AACd,cAAI,IAAI,KAAK,CAAC,IAAE;AAChB,cAAI,IAAI,KAAK,GAAG,KAAG;AACnB,cAAI,IAAI,KAAG,IAAE,IAAE;AACf,cAAI,KAAG,MAAI,IAAE,UAAS,MAAI,EAAE,CAAC,KAAG,IAAE;AAClC,eAAK,MAAI,OAAK,MAAI,MAAI,KAAG,KAAG,MAAI;AAChC,YAAE,GAAG,IAAI,IAAE;AAAA,QACb;AACA,eAAO;AAAA,MACT;AAGA,eAAS,IAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAG;AACxB,YAAI,KAAK,IAAE,OAAQ,KAAK,KAAG;AAC3B,eAAM,EAAE,KAAK,GAAG;AACd,cAAI,IAAI,KAAK,CAAC,IAAE;AAChB,cAAI,IAAI,KAAK,GAAG,KAAG;AACnB,cAAI,IAAI,KAAG,IAAE,IAAE;AACf,cAAI,KAAG,MAAI,IAAE,UAAS,MAAI,EAAE,CAAC,IAAE;AAC/B,eAAK,KAAG,OAAK,KAAG,MAAI,KAAG;AACvB,YAAE,GAAG,IAAI,IAAE;AAAA,QACb;AACA,eAAO;AAAA,MACT;AACA,UAAI,YAAY,OAAO,cAAc;AACrC,UAAG,aAAa,QAAS,UAAU,WAAW,+BAAgC;AAC5E,mBAAW,UAAU,KAAK;AAC1B,gBAAQ;AAAA,MACV,WACQ,aAAa,QAAS,UAAU,WAAW,YAAa;AAC9D,mBAAW,UAAU,KAAK;AAC1B,gBAAQ;AAAA,MACV,OACK;AACH,mBAAW,UAAU,KAAK;AAC1B,gBAAQ;AAAA,MACV;AAEA,iBAAW,UAAU,KAAK;AAC1B,iBAAW,UAAU,MAAO,KAAG,SAAO;AACtC,iBAAW,UAAU,KAAM,KAAG;AAE9B,UAAI,QAAQ;AACZ,iBAAW,UAAU,KAAK,KAAK,IAAI,GAAE,KAAK;AAC1C,iBAAW,UAAU,KAAK,QAAM;AAChC,iBAAW,UAAU,KAAK,IAAE,QAAM;AAGlC,UAAI,QAAQ;AACZ,UAAI,QAAQ,IAAI,MAAM;AACtB,UAAI,IAAG;AACP,WAAK,IAAI,WAAW,CAAC;AACrB,WAAI,KAAK,GAAG,MAAM,GAAG,EAAE,GAAI,OAAM,IAAI,IAAI;AACzC,WAAK,IAAI,WAAW,CAAC;AACrB,WAAI,KAAK,IAAI,KAAK,IAAI,EAAE,GAAI,OAAM,IAAI,IAAI;AAC1C,WAAK,IAAI,WAAW,CAAC;AACrB,WAAI,KAAK,IAAI,KAAK,IAAI,EAAE,GAAI,OAAM,IAAI,IAAI;AAE1C,eAAS,SAAS,GAAG;AAAE,eAAO,MAAM,OAAO,CAAC;AAAA,MAAG;AAC/C,eAAS,MAAM,GAAE,GAAG;AAClB,YAAI,IAAI,MAAM,EAAE,WAAW,CAAC,CAAC;AAC7B,eAAQ,KAAG,OAAM,KAAG;AAAA,MACtB;AAGA,eAAS,UAAU,GAAG;AACpB,iBAAQ,IAAI,KAAK,IAAE,GAAG,KAAK,GAAG,EAAE,EAAG,GAAE,CAAC,IAAI,KAAK,CAAC;AAChD,UAAE,IAAI,KAAK;AACX,UAAE,IAAI,KAAK;AAAA,MACb;AAGA,eAAS,WAAW,GAAG;AACrB,aAAK,IAAI;AACT,aAAK,IAAK,IAAE,IAAG,KAAG;AAClB,YAAG,IAAI,EAAG,MAAK,CAAC,IAAI;AAAA,iBACZ,IAAI,GAAI,MAAK,CAAC,IAAI,IAAE,KAAK;AAAA,YAC5B,MAAK,IAAI;AAAA,MAChB;AAGA,eAAS,IAAI,GAAG;AAAE,YAAI,IAAI,IAAI;AAAG,UAAE,QAAQ,CAAC;AAAG,eAAO;AAAA,MAAG;AAGzD,eAAS,cAAc,GAAE,GAAG;AAC1B,YAAI;AACJ,YAAG,KAAK,GAAI,KAAI;AAAA,iBACR,KAAK,EAAG,KAAI;AAAA,iBACZ,KAAK,IAAK,KAAI;AAAA,iBACd,KAAK,EAAG,KAAI;AAAA,iBACZ,KAAK,GAAI,KAAI;AAAA,iBACb,KAAK,EAAG,KAAI;AAAA,aACf;AAAE,eAAK,UAAU,GAAE,CAAC;AAAG;AAAA,QAAQ;AACpC,aAAK,IAAI;AACT,aAAK,IAAI;AACT,YAAI,IAAI,EAAE,QAAQ,KAAK,OAAO,KAAK;AACnC,eAAM,EAAE,KAAK,GAAG;AACd,cAAI,IAAK,KAAG,IAAG,EAAE,CAAC,IAAE,MAAK,MAAM,GAAE,CAAC;AAClC,cAAG,IAAI,GAAG;AACR,gBAAG,EAAE,OAAO,CAAC,KAAK,IAAK,MAAK;AAC5B;AAAA,UACF;AACA,eAAK;AACL,cAAG,MAAM;AACP,iBAAK,KAAK,GAAG,IAAI;AAAA,mBACX,KAAG,IAAI,KAAK,IAAI;AACtB,iBAAK,KAAK,IAAE,CAAC,MAAM,KAAI,KAAI,KAAK,KAAG,MAAK,MAAK;AAC7C,iBAAK,KAAK,GAAG,IAAK,KAAI,KAAK,KAAG;AAAA,UAChC;AAEE,iBAAK,KAAK,IAAE,CAAC,KAAK,KAAG;AACvB,gBAAM;AACN,cAAG,MAAM,KAAK,GAAI,OAAM,KAAK;AAAA,QAC/B;AACA,YAAG,KAAK,MAAM,EAAE,CAAC,IAAE,QAAS,GAAG;AAC7B,eAAK,IAAI;AACT,cAAG,KAAK,EAAG,MAAK,KAAK,IAAE,CAAC,MAAO,KAAI,KAAK,KAAG,MAAK,KAAI;AAAA,QACtD;AACA,aAAK,MAAM;AACX,YAAG,GAAI,YAAW,KAAK,MAAM,MAAK,IAAI;AAAA,MACxC;AAGA,eAAS,WAAW;AAClB,YAAI,IAAI,KAAK,IAAE,KAAK;AACpB,eAAM,KAAK,IAAI,KAAK,KAAK,KAAK,IAAE,CAAC,KAAK,EAAG,GAAE,KAAK;AAAA,MAClD;AAGA,eAAS,WAAW,GAAG;AACrB,YAAG,KAAK,IAAI,EAAG,QAAO,MAAI,KAAK,OAAO,EAAE,SAAS,CAAC;AAClD,YAAI;AACJ,YAAG,KAAK,GAAI,KAAI;AAAA,iBACR,KAAK,EAAG,KAAI;AAAA,iBACZ,KAAK,EAAG,KAAI;AAAA,iBACZ,KAAK,GAAI,KAAI;AAAA,iBACb,KAAK,EAAG,KAAI;AAAA,YACf,QAAO,KAAK,QAAQ,CAAC;AAC1B,YAAI,MAAM,KAAG,KAAG,GAAG,GAAG,IAAI,OAAO,IAAI,IAAI,IAAI,KAAK;AAClD,YAAI,IAAI,KAAK,KAAI,IAAE,KAAK,KAAI;AAC5B,YAAG,MAAM,GAAG;AACV,cAAG,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC,KAAG,KAAK,GAAG;AAAE,gBAAI;AAAM,gBAAI,SAAS,CAAC;AAAA,UAAG;AACrE,iBAAM,KAAK,GAAG;AACZ,gBAAG,IAAI,GAAG;AACR,mBAAK,KAAK,CAAC,KAAI,KAAG,KAAG,MAAM,IAAE;AAC7B,mBAAK,KAAK,EAAE,CAAC,MAAI,KAAG,KAAK,KAAG;AAAA,YAC9B,OACK;AACH,kBAAK,KAAK,CAAC,MAAI,KAAG,KAAI;AACtB,kBAAG,KAAK,GAAG;AAAE,qBAAK,KAAK;AAAI,kBAAE;AAAA,cAAG;AAAA,YAClC;AACA,gBAAG,IAAI,EAAG,KAAI;AACd,gBAAG,EAAG,MAAK,SAAS,CAAC;AAAA,UACvB;AAAA,QACF;AACA,eAAO,IAAE,IAAE;AAAA,MACb;AAGA,eAAS,WAAW;AAAE,YAAI,IAAI,IAAI;AAAG,mBAAW,KAAK,MAAM,MAAK,CAAC;AAAG,eAAO;AAAA,MAAG;AAG9E,eAAS,QAAQ;AAAE,eAAQ,KAAK,IAAE,IAAG,KAAK,OAAO,IAAE;AAAA,MAAM;AAGzD,eAAS,YAAY,GAAG;AACtB,YAAI,IAAI,KAAK,IAAE,EAAE;AACjB,YAAG,KAAK,EAAG,QAAO;AAClB,YAAI,IAAI,KAAK;AACb,YAAI,IAAE,EAAE;AACR,YAAG,KAAK,EAAG,QAAQ,KAAK,IAAE,IAAG,CAAC,IAAE;AAChC,eAAM,EAAE,KAAK,EAAG,MAAI,IAAE,KAAK,CAAC,IAAE,EAAE,CAAC,MAAM,EAAG,QAAO;AACjD,eAAO;AAAA,MACT;AAGA,eAAS,MAAM,GAAG;AAChB,YAAI,IAAI,GAAGA;AACX,aAAIA,KAAE,MAAI,OAAO,GAAG;AAAE,cAAIA;AAAG,eAAK;AAAA,QAAI;AACtC,aAAIA,KAAE,KAAG,MAAM,GAAG;AAAE,cAAIA;AAAG,eAAK;AAAA,QAAG;AACnC,aAAIA,KAAE,KAAG,MAAM,GAAG;AAAE,cAAIA;AAAG,eAAK;AAAA,QAAG;AACnC,aAAIA,KAAE,KAAG,MAAM,GAAG;AAAE,cAAIA;AAAG,eAAK;AAAA,QAAG;AACnC,aAAIA,KAAE,KAAG,MAAM,GAAG;AAAE,cAAIA;AAAG,eAAK;AAAA,QAAG;AACnC,eAAO;AAAA,MACT;AAGA,eAAS,cAAc;AACrB,YAAG,KAAK,KAAK,EAAG,QAAO;AACvB,eAAO,KAAK,MAAI,KAAK,IAAE,KAAG,MAAM,KAAK,KAAK,IAAE,CAAC,IAAG,KAAK,IAAE,KAAK,EAAG;AAAA,MACjE;AAGA,eAAS,aAAa,GAAE,GAAG;AACzB,YAAI;AACJ,aAAI,IAAI,KAAK,IAAE,GAAG,KAAK,GAAG,EAAE,EAAG,GAAE,IAAE,CAAC,IAAI,KAAK,CAAC;AAC9C,aAAI,IAAI,IAAE,GAAG,KAAK,GAAG,EAAE,EAAG,GAAE,CAAC,IAAI;AACjC,UAAE,IAAI,KAAK,IAAE;AACb,UAAE,IAAI,KAAK;AAAA,MACb;AAGA,eAAS,aAAa,GAAE,GAAG;AACzB,iBAAQ,IAAI,GAAG,IAAI,KAAK,GAAG,EAAE,EAAG,GAAE,IAAE,CAAC,IAAI,KAAK,CAAC;AAC/C,UAAE,IAAI,KAAK,IAAI,KAAK,IAAE,GAAE,CAAC;AACzB,UAAE,IAAI,KAAK;AAAA,MACb;AAGA,eAAS,YAAY,GAAE,GAAG;AACxB,YAAI,KAAK,IAAE,KAAK;AAChB,YAAI,MAAM,KAAK,KAAG;AAClB,YAAI,MAAM,KAAG,OAAK;AAClB,YAAI,KAAK,KAAK,MAAM,IAAE,KAAK,EAAE,GAAG,IAAK,KAAK,KAAG,KAAI,KAAK,IAAI;AAC1D,aAAI,IAAI,KAAK,IAAE,GAAG,KAAK,GAAG,EAAE,GAAG;AAC7B,YAAE,IAAE,KAAG,CAAC,IAAK,KAAK,CAAC,KAAG,MAAK;AAC3B,eAAK,KAAK,CAAC,IAAE,OAAK;AAAA,QACpB;AACA,aAAI,IAAI,KAAG,GAAG,KAAK,GAAG,EAAE,EAAG,GAAE,CAAC,IAAI;AAClC,UAAE,EAAE,IAAI;AACR,UAAE,IAAI,KAAK,IAAE,KAAG;AAChB,UAAE,IAAI,KAAK;AACX,UAAE,MAAM;AAAA,MACV;AAGA,eAAS,YAAY,GAAE,GAAG;AACxB,UAAE,IAAI,KAAK;AACX,YAAI,KAAK,KAAK,MAAM,IAAE,KAAK,EAAE;AAC7B,YAAG,MAAM,KAAK,GAAG;AAAE,YAAE,IAAI;AAAG;AAAA,QAAQ;AACpC,YAAI,KAAK,IAAE,KAAK;AAChB,YAAI,MAAM,KAAK,KAAG;AAClB,YAAI,MAAM,KAAG,MAAI;AACjB,UAAE,CAAC,IAAI,KAAK,EAAE,KAAG;AACjB,iBAAQ,IAAI,KAAG,GAAG,IAAI,KAAK,GAAG,EAAE,GAAG;AACjC,YAAE,IAAE,KAAG,CAAC,MAAM,KAAK,CAAC,IAAE,OAAK;AAC3B,YAAE,IAAE,EAAE,IAAI,KAAK,CAAC,KAAG;AAAA,QACrB;AACA,YAAG,KAAK,EAAG,GAAE,KAAK,IAAE,KAAG,CAAC,MAAM,KAAK,IAAE,OAAK;AAC1C,UAAE,IAAI,KAAK,IAAE;AACb,UAAE,MAAM;AAAA,MACV;AAGA,eAAS,SAAS,GAAE,GAAG;AACrB,YAAI,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,IAAI,EAAE,GAAE,KAAK,CAAC;AACzC,eAAM,IAAI,GAAG;AACX,eAAK,KAAK,CAAC,IAAE,EAAE,CAAC;AAChB,YAAE,GAAG,IAAI,IAAE,KAAK;AAChB,gBAAM,KAAK;AAAA,QACb;AACA,YAAG,EAAE,IAAI,KAAK,GAAG;AACf,eAAK,EAAE;AACP,iBAAM,IAAI,KAAK,GAAG;AAChB,iBAAK,KAAK,CAAC;AACX,cAAE,GAAG,IAAI,IAAE,KAAK;AAChB,kBAAM,KAAK;AAAA,UACb;AACA,eAAK,KAAK;AAAA,QACZ,OACK;AACH,eAAK,KAAK;AACV,iBAAM,IAAI,EAAE,GAAG;AACb,iBAAK,EAAE,CAAC;AACR,cAAE,GAAG,IAAI,IAAE,KAAK;AAChB,kBAAM,KAAK;AAAA,UACb;AACA,eAAK,EAAE;AAAA,QACT;AACA,UAAE,IAAK,IAAE,IAAG,KAAG;AACf,YAAG,IAAI,GAAI,GAAE,GAAG,IAAI,KAAK,KAAG;AAAA,iBACpB,IAAI,EAAG,GAAE,GAAG,IAAI;AACxB,UAAE,IAAI;AACN,UAAE,MAAM;AAAA,MACV;AAIA,eAAS,cAAc,GAAE,GAAG;AAC1B,YAAI,IAAI,KAAK,IAAI,GAAG,IAAI,EAAE,IAAI;AAC9B,YAAI,IAAI,EAAE;AACV,UAAE,IAAI,IAAE,EAAE;AACV,eAAM,EAAE,KAAK,EAAG,GAAE,CAAC,IAAI;AACvB,aAAI,IAAI,GAAG,IAAI,EAAE,GAAG,EAAE,EAAG,GAAE,IAAE,EAAE,CAAC,IAAI,EAAE,GAAG,GAAE,EAAE,CAAC,GAAE,GAAE,GAAE,GAAE,EAAE,CAAC;AACzD,UAAE,IAAI;AACN,UAAE,MAAM;AACR,YAAG,KAAK,KAAK,EAAE,EAAG,YAAW,KAAK,MAAM,GAAE,CAAC;AAAA,MAC7C;AAGA,eAAS,YAAY,GAAG;AACtB,YAAI,IAAI,KAAK,IAAI;AACjB,YAAI,IAAI,EAAE,IAAI,IAAE,EAAE;AAClB,eAAM,EAAE,KAAK,EAAG,GAAE,CAAC,IAAI;AACvB,aAAI,IAAI,GAAG,IAAI,EAAE,IAAE,GAAG,EAAE,GAAG;AACzB,cAAI,IAAI,EAAE,GAAG,GAAE,EAAE,CAAC,GAAE,GAAE,IAAE,GAAE,GAAE,CAAC;AAC7B,eAAI,EAAE,IAAE,EAAE,CAAC,KAAG,EAAE,GAAG,IAAE,GAAE,IAAE,EAAE,CAAC,GAAE,GAAE,IAAE,IAAE,GAAE,GAAE,EAAE,IAAE,IAAE,CAAC,MAAM,EAAE,IAAI;AACzD,cAAE,IAAE,EAAE,CAAC,KAAK,EAAE;AACd,cAAE,IAAE,EAAE,IAAE,CAAC,IAAI;AAAA,UACf;AAAA,QACF;AACA,YAAG,EAAE,IAAI,EAAG,GAAE,EAAE,IAAE,CAAC,KAAK,EAAE,GAAG,GAAE,EAAE,CAAC,GAAE,GAAE,IAAE,GAAE,GAAE,CAAC;AAC7C,UAAE,IAAI;AACN,UAAE,MAAM;AAAA,MACV;AAIA,eAAS,YAAY,GAAE,GAAE,GAAG;AAC1B,YAAI,KAAK,EAAE,IAAI;AACf,YAAG,GAAG,KAAK,EAAG;AACd,YAAI,KAAK,KAAK,IAAI;AAClB,YAAG,GAAG,IAAI,GAAG,GAAG;AACd,cAAG,KAAK,KAAM,GAAE,QAAQ,CAAC;AACzB,cAAG,KAAK,KAAM,MAAK,OAAO,CAAC;AAC3B;AAAA,QACF;AACA,YAAG,KAAK,KAAM,KAAI,IAAI;AACtB,YAAI,IAAI,IAAI,GAAG,KAAK,KAAK,GAAG,KAAK,EAAE;AACnC,YAAI,MAAM,KAAK,KAAG,MAAM,GAAG,GAAG,IAAE,CAAC,CAAC;AAClC,YAAG,MAAM,GAAG;AAAE,aAAG,SAAS,KAAI,CAAC;AAAG,aAAG,SAAS,KAAI,CAAC;AAAA,QAAG,OACjD;AAAE,aAAG,OAAO,CAAC;AAAG,aAAG,OAAO,CAAC;AAAA,QAAG;AACnC,YAAI,KAAK,EAAE;AACX,YAAI,KAAK,EAAE,KAAG,CAAC;AACf,YAAG,MAAM,EAAG;AACZ,YAAI,KAAK,MAAI,KAAG,KAAK,OAAM,KAAG,IAAG,EAAE,KAAG,CAAC,KAAG,KAAK,KAAG;AAClD,YAAI,KAAK,KAAK,KAAG,IAAI,MAAM,KAAG,KAAK,MAAI,IAAI,IAAI,KAAG,KAAK;AACvD,YAAI,IAAI,EAAE,GAAG,IAAI,IAAE,IAAIA,KAAK,KAAG,OAAM,IAAI,IAAE;AAC3C,UAAE,UAAU,GAAEA,EAAC;AACf,YAAG,EAAE,UAAUA,EAAC,KAAK,GAAG;AACtB,YAAE,EAAE,GAAG,IAAI;AACX,YAAE,MAAMA,IAAE,CAAC;AAAA,QACb;AACA,mBAAW,IAAI,UAAU,IAAGA,EAAC;AAC7B,QAAAA,GAAE,MAAM,GAAE,CAAC;AACX,eAAM,EAAE,IAAI,GAAI,GAAE,EAAE,GAAG,IAAI;AAC3B,eAAM,EAAE,KAAK,GAAG;AAEd,cAAI,KAAM,EAAE,EAAE,CAAC,KAAG,KAAI,KAAK,KAAG,KAAK,MAAM,EAAE,CAAC,IAAE,MAAI,EAAE,IAAE,CAAC,IAAE,KAAG,EAAE;AAC9D,eAAI,EAAE,CAAC,KAAG,EAAE,GAAG,GAAE,IAAG,GAAE,GAAE,GAAE,EAAE,KAAK,IAAI;AACnC,cAAE,UAAU,GAAEA,EAAC;AACf,cAAE,MAAMA,IAAE,CAAC;AACX,mBAAM,EAAE,CAAC,IAAI,EAAE,GAAI,GAAE,MAAMA,IAAE,CAAC;AAAA,UAChC;AAAA,QACF;AACA,YAAG,KAAK,MAAM;AACZ,YAAE,UAAU,IAAG,CAAC;AAChB,cAAG,MAAM,GAAI,YAAW,KAAK,MAAM,GAAE,CAAC;AAAA,QACxC;AACA,UAAE,IAAI;AACN,UAAE,MAAM;AACR,YAAG,MAAM,EAAG,GAAE,SAAS,KAAI,CAAC;AAC5B,YAAG,KAAK,EAAG,YAAW,KAAK,MAAM,GAAE,CAAC;AAAA,MACtC;AAGA,eAAS,MAAM,GAAG;AAChB,YAAI,IAAI,IAAI;AACZ,aAAK,IAAI,EAAE,SAAS,GAAE,MAAK,CAAC;AAC5B,YAAG,KAAK,IAAI,KAAK,EAAE,UAAU,WAAW,IAAI,IAAI,EAAG,GAAE,MAAM,GAAE,CAAC;AAC9D,eAAO;AAAA,MACT;AAGA,eAAS,QAAQ,GAAG;AAAE,aAAK,IAAI;AAAA,MAAG;AAClC,eAAS,SAAS,GAAG;AACnB,YAAG,EAAE,IAAI,KAAK,EAAE,UAAU,KAAK,CAAC,KAAK,EAAG,QAAO,EAAE,IAAI,KAAK,CAAC;AAAA,YACtD,QAAO;AAAA,MACd;AACA,eAAS,QAAQ,GAAG;AAAE,eAAO;AAAA,MAAG;AAChC,eAAS,QAAQ,GAAG;AAAE,UAAE,SAAS,KAAK,GAAE,MAAK,CAAC;AAAA,MAAG;AACjD,eAAS,OAAO,GAAE,GAAE,GAAG;AAAE,UAAE,WAAW,GAAE,CAAC;AAAG,aAAK,OAAO,CAAC;AAAA,MAAG;AAC5D,eAAS,OAAO,GAAE,GAAG;AAAE,UAAE,SAAS,CAAC;AAAG,aAAK,OAAO,CAAC;AAAA,MAAG;AAEtD,cAAQ,UAAU,UAAU;AAC5B,cAAQ,UAAU,SAAS;AAC3B,cAAQ,UAAU,SAAS;AAC3B,cAAQ,UAAU,QAAQ;AAC1B,cAAQ,UAAU,QAAQ;AAY1B,eAAS,cAAc;AACrB,YAAG,KAAK,IAAI,EAAG,QAAO;AACtB,YAAI,IAAI,KAAK,CAAC;AACd,aAAI,IAAE,MAAM,EAAG,QAAO;AACtB,YAAI,IAAI,IAAE;AACV,YAAK,KAAG,KAAG,IAAE,MAAK,KAAI;AACtB,YAAK,KAAG,KAAG,IAAE,OAAM,KAAI;AACvB,YAAK,KAAG,MAAK,IAAE,SAAQ,IAAG,UAAU;AAGpC,YAAK,KAAG,IAAE,IAAE,IAAE,KAAK,MAAK,KAAK;AAE7B,eAAQ,IAAE,IAAG,KAAK,KAAG,IAAE,CAAC;AAAA,MAC1B;AAGA,eAAS,WAAW,GAAG;AACrB,aAAK,IAAI;AACT,aAAK,KAAK,EAAE,SAAS;AACrB,aAAK,MAAM,KAAK,KAAG;AACnB,aAAK,MAAM,KAAK,MAAI;AACpB,aAAK,MAAM,KAAI,EAAE,KAAG,MAAK;AACzB,aAAK,MAAM,IAAE,EAAE;AAAA,MACjB;AAGA,eAAS,YAAY,GAAG;AACtB,YAAI,IAAI,IAAI;AACZ,UAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAE,CAAC;AAC5B,UAAE,SAAS,KAAK,GAAE,MAAK,CAAC;AACxB,YAAG,EAAE,IAAI,KAAK,EAAE,UAAU,WAAW,IAAI,IAAI,EAAG,MAAK,EAAE,MAAM,GAAE,CAAC;AAChE,eAAO;AAAA,MACT;AAGA,eAAS,WAAW,GAAG;AACrB,YAAI,IAAI,IAAI;AACZ,UAAE,OAAO,CAAC;AACV,aAAK,OAAO,CAAC;AACb,eAAO;AAAA,MACT;AAGA,eAAS,WAAW,GAAG;AACrB,eAAM,EAAE,KAAK,KAAK;AAChB,YAAE,EAAE,GAAG,IAAI;AACb,iBAAQ,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG,EAAE,GAAG;AAEhC,cAAI,IAAI,EAAE,CAAC,IAAE;AACb,cAAI,KAAM,IAAE,KAAK,QAAO,IAAE,KAAK,OAAK,EAAE,CAAC,KAAG,MAAI,KAAK,MAAK,KAAK,OAAK,MAAK,EAAE;AAEzE,cAAI,IAAE,KAAK,EAAE;AACb,YAAE,CAAC,KAAK,KAAK,EAAE,GAAG,GAAE,IAAG,GAAE,GAAE,GAAE,KAAK,EAAE,CAAC;AAErC,iBAAM,EAAE,CAAC,KAAK,EAAE,IAAI;AAAE,cAAE,CAAC,KAAK,EAAE;AAAI,cAAE,EAAE,CAAC;AAAA,UAAK;AAAA,QAChD;AACA,UAAE,MAAM;AACR,UAAE,UAAU,KAAK,EAAE,GAAE,CAAC;AACtB,YAAG,EAAE,UAAU,KAAK,CAAC,KAAK,EAAG,GAAE,MAAM,KAAK,GAAE,CAAC;AAAA,MAC/C;AAGA,eAAS,UAAU,GAAE,GAAG;AAAE,UAAE,SAAS,CAAC;AAAG,aAAK,OAAO,CAAC;AAAA,MAAG;AAGzD,eAAS,UAAU,GAAE,GAAE,GAAG;AAAE,UAAE,WAAW,GAAE,CAAC;AAAG,aAAK,OAAO,CAAC;AAAA,MAAG;AAE/D,iBAAW,UAAU,UAAU;AAC/B,iBAAW,UAAU,SAAS;AAC9B,iBAAW,UAAU,SAAS;AAC9B,iBAAW,UAAU,QAAQ;AAC7B,iBAAW,UAAU,QAAQ;AAG7B,eAAS,YAAY;AAAE,gBAAS,KAAK,IAAE,IAAI,KAAK,CAAC,IAAE,IAAG,KAAK,MAAM;AAAA,MAAG;AAGpE,eAAS,OAAO,GAAEC,IAAG;AACnB,YAAG,IAAI,cAAc,IAAI,EAAG,QAAO,WAAW;AAC9C,YAAI,IAAI,IAAI,GAAG,KAAK,IAAI,GAAG,IAAIA,GAAE,QAAQ,IAAI,GAAG,IAAI,MAAM,CAAC,IAAE;AAC7D,UAAE,OAAO,CAAC;AACV,eAAM,EAAE,KAAK,GAAG;AACd,UAAAA,GAAE,MAAM,GAAE,EAAE;AACZ,eAAI,IAAG,KAAG,KAAM,EAAG,CAAAA,GAAE,MAAM,IAAG,GAAE,CAAC;AAAA,eAC5B;AAAE,gBAAID,KAAI;AAAG,gBAAI;AAAI,iBAAKA;AAAA,UAAG;AAAA,QACpC;AACA,eAAOC,GAAE,OAAO,CAAC;AAAA,MACnB;AAGA,eAAS,YAAY,GAAE,GAAG;AACxB,YAAIA;AACJ,YAAG,IAAI,OAAO,EAAE,OAAO,EAAG,CAAAA,KAAI,IAAI,QAAQ,CAAC;AAAA,YAAQ,CAAAA,KAAI,IAAI,WAAW,CAAC;AACvE,eAAO,KAAK,IAAI,GAAEA,EAAC;AAAA,MACrB;AAGA,iBAAW,UAAU,SAAS;AAC9B,iBAAW,UAAU,UAAU;AAC/B,iBAAW,UAAU,aAAa;AAClC,iBAAW,UAAU,QAAQ;AAC7B,iBAAW,UAAU,YAAY;AACjC,iBAAW,UAAU,YAAY;AACjC,iBAAW,UAAU,WAAW;AAChC,iBAAW,UAAU,WAAW;AAChC,iBAAW,UAAU,QAAQ;AAC7B,iBAAW,UAAU,aAAa;AAClC,iBAAW,UAAU,WAAW;AAChC,iBAAW,UAAU,WAAW;AAChC,iBAAW,UAAU,WAAW;AAChC,iBAAW,UAAU,SAAS;AAC9B,iBAAW,UAAU,MAAM;AAG3B,iBAAW,UAAU,WAAW;AAChC,iBAAW,UAAU,SAAS;AAC9B,iBAAW,UAAU,MAAM;AAC3B,iBAAW,UAAU,YAAY;AACjC,iBAAW,UAAU,YAAY;AACjC,iBAAW,UAAU,MAAM;AAC3B,iBAAW,UAAU,YAAY;AAGjC,iBAAW,OAAO,IAAI,CAAC;AACvB,iBAAW,MAAM,IAAI,CAAC;AAYtB,eAAS,UAAU;AAAE,YAAI,IAAI,IAAI;AAAG,aAAK,OAAO,CAAC;AAAG,eAAO;AAAA,MAAG;AAG9D,eAAS,aAAa;AACpB,YAAG,KAAK,IAAI,GAAG;AACb,cAAG,KAAK,KAAK,EAAG,QAAO,KAAK,CAAC,IAAE,KAAK;AAAA,mBAC5B,KAAK,KAAK,EAAG,QAAO;AAAA,QAC9B,WACQ,KAAK,KAAK,EAAG,QAAO,KAAK,CAAC;AAAA,iBAC1B,KAAK,KAAK,EAAG,QAAO;AAE5B,gBAAS,KAAK,CAAC,KAAI,KAAI,KAAG,KAAK,MAAK,MAAK,KAAK,KAAI,KAAK,CAAC;AAAA,MAC1D;AAGA,eAAS,cAAc;AAAE,eAAQ,KAAK,KAAG,IAAG,KAAK,IAAG,KAAK,CAAC,KAAG,MAAK;AAAA,MAAI;AAGtE,eAAS,eAAe;AAAE,eAAQ,KAAK,KAAG,IAAG,KAAK,IAAG,KAAK,CAAC,KAAG,MAAK;AAAA,MAAI;AAGvE,eAAS,aAAa,GAAG;AAAE,eAAO,KAAK,MAAM,KAAK,MAAI,KAAK,KAAG,KAAK,IAAI,CAAC,CAAC;AAAA,MAAG;AAG5E,eAAS,WAAW;AAClB,YAAG,KAAK,IAAI,EAAG,QAAO;AAAA,iBACd,KAAK,KAAK,KAAM,KAAK,KAAK,KAAK,KAAK,CAAC,KAAK,EAAI,QAAO;AAAA,YACxD,QAAO;AAAA,MACd;AAGA,eAAS,WAAW,GAAG;AACrB,YAAG,KAAK,KAAM,KAAI;AAClB,YAAG,KAAK,OAAO,KAAK,KAAK,IAAI,KAAK,IAAI,GAAI,QAAO;AACjD,YAAI,KAAK,KAAK,UAAU,CAAC;AACzB,YAAI,IAAI,KAAK,IAAI,GAAE,EAAE;AACrB,YAAI,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,GAAGA,KAAI,IAAI,GAAG,IAAI;AAC1C,aAAK,SAAS,GAAE,GAAEA,EAAC;AACnB,eAAM,EAAE,OAAO,IAAI,GAAG;AACpB,eAAK,IAAEA,GAAE,SAAS,GAAG,SAAS,CAAC,EAAE,OAAO,CAAC,IAAI;AAC7C,YAAE,SAAS,GAAE,GAAEA,EAAC;AAAA,QAClB;AACA,eAAOA,GAAE,SAAS,EAAE,SAAS,CAAC,IAAI;AAAA,MACpC;AAGA,eAAS,aAAa,GAAE,GAAG;AACzB,aAAK,QAAQ,CAAC;AACd,YAAG,KAAK,KAAM,KAAI;AAClB,YAAI,KAAK,KAAK,UAAU,CAAC;AACzB,YAAI,IAAI,KAAK,IAAI,GAAE,EAAE,GAAG,KAAK,OAAO,IAAI,GAAG,IAAI;AAC/C,iBAAQ,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAG;AAChC,cAAI,IAAI,MAAM,GAAE,CAAC;AACjB,cAAG,IAAI,GAAG;AACR,gBAAG,EAAE,OAAO,CAAC,KAAK,OAAO,KAAK,OAAO,KAAK,EAAG,MAAK;AAClD;AAAA,UACF;AACA,cAAI,IAAE,IAAE;AACR,cAAG,EAAE,KAAK,IAAI;AACZ,iBAAK,UAAU,CAAC;AAChB,iBAAK,WAAW,GAAE,CAAC;AACnB,gBAAI;AACJ,gBAAI;AAAA,UACN;AAAA,QACF;AACA,YAAG,IAAI,GAAG;AACR,eAAK,UAAU,KAAK,IAAI,GAAE,CAAC,CAAC;AAC5B,eAAK,WAAW,GAAE,CAAC;AAAA,QACrB;AACA,YAAG,GAAI,YAAW,KAAK,MAAM,MAAK,IAAI;AAAA,MACxC;AAGA,eAAS,cAAc,GAAE,GAAE,GAAG;AAC5B,YAAG,YAAY,OAAO,GAAG;AAEvB,cAAG,IAAI,EAAG,MAAK,QAAQ,CAAC;AAAA,eACnB;AACH,iBAAK,WAAW,GAAE,CAAC;AACnB,gBAAG,CAAC,KAAK,QAAQ,IAAE,CAAC;AAClB,mBAAK,UAAU,WAAW,IAAI,UAAU,IAAE,CAAC,GAAE,OAAM,IAAI;AACzD,gBAAG,KAAK,OAAO,EAAG,MAAK,WAAW,GAAE,CAAC;AACrC,mBAAM,CAAC,KAAK,gBAAgB,CAAC,GAAG;AAC9B,mBAAK,WAAW,GAAE,CAAC;AACnB,kBAAG,KAAK,UAAU,IAAI,EAAG,MAAK,MAAM,WAAW,IAAI,UAAU,IAAE,CAAC,GAAE,IAAI;AAAA,YACxE;AAAA,UACF;AAAA,QACF,OACK;AAEH,cAAI,IAAI,IAAI,MAAM,GAAGD,KAAI,IAAE;AAC3B,YAAE,UAAU,KAAG,KAAG;AAClB,YAAE,UAAU,CAAC;AACb,cAAGA,KAAI,EAAG,GAAE,CAAC,MAAO,KAAGA,MAAG;AAAA,cAAS,GAAE,CAAC,IAAI;AAC1C,eAAK,WAAW,GAAE,GAAG;AAAA,QACvB;AAAA,MACF;AAGA,eAAS,gBAAgB;AACvB,YAAI,IAAI,KAAK,GAAG,IAAI,IAAI,MAAM;AAC9B,UAAE,CAAC,IAAI,KAAK;AACZ,YAAI,IAAI,KAAK,KAAI,IAAE,KAAK,KAAI,GAAG,GAAG,IAAI;AACtC,YAAG,MAAM,GAAG;AACV,cAAG,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC,KAAG,OAAO,KAAK,IAAE,KAAK,OAAK;AACtD,cAAE,GAAG,IAAI,IAAG,KAAK,KAAI,KAAK,KAAG;AAC/B,iBAAM,KAAK,GAAG;AACZ,gBAAG,IAAI,GAAG;AACR,mBAAK,KAAK,CAAC,KAAI,KAAG,KAAG,MAAM,IAAE;AAC7B,mBAAK,KAAK,EAAE,CAAC,MAAI,KAAG,KAAK,KAAG;AAAA,YAC9B,OACK;AACH,kBAAK,KAAK,CAAC,MAAI,KAAG,KAAI;AACtB,kBAAG,KAAK,GAAG;AAAE,qBAAK,KAAK;AAAI,kBAAE;AAAA,cAAG;AAAA,YAClC;AACA,iBAAI,IAAE,QAAS,EAAG,MAAK;AACvB,gBAAG,KAAK,MAAM,KAAK,IAAE,SAAU,IAAE,KAAO,GAAE;AAC1C,gBAAG,IAAI,KAAK,KAAK,KAAK,EAAG,GAAE,GAAG,IAAI;AAAA,UACpC;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAEA,eAAS,SAAS,GAAG;AAAE,eAAO,KAAK,UAAU,CAAC,KAAG;AAAA,MAAI;AACrD,eAAS,MAAM,GAAG;AAAE,eAAO,KAAK,UAAU,CAAC,IAAE,IAAG,OAAK;AAAA,MAAG;AACxD,eAAS,MAAM,GAAG;AAAE,eAAO,KAAK,UAAU,CAAC,IAAE,IAAG,OAAK;AAAA,MAAG;AAGxD,eAAS,aAAa,GAAE,IAAG,GAAG;AAC5B,YAAI,GAAG,GAAG,IAAI,KAAK,IAAI,EAAE,GAAE,KAAK,CAAC;AACjC,aAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,GAAE,CAAC,IAAI,GAAG,KAAK,CAAC,GAAE,EAAE,CAAC,CAAC;AAC7C,YAAG,EAAE,IAAI,KAAK,GAAG;AACf,cAAI,EAAE,IAAE,KAAK;AACb,eAAI,IAAI,GAAG,IAAI,KAAK,GAAG,EAAE,EAAG,GAAE,CAAC,IAAI,GAAG,KAAK,CAAC,GAAE,CAAC;AAC/C,YAAE,IAAI,KAAK;AAAA,QACb,OACK;AACH,cAAI,KAAK,IAAE,KAAK;AAChB,eAAI,IAAI,GAAG,IAAI,EAAE,GAAG,EAAE,EAAG,GAAE,CAAC,IAAI,GAAG,GAAE,EAAE,CAAC,CAAC;AACzC,YAAE,IAAI,EAAE;AAAA,QACV;AACA,UAAE,IAAI,GAAG,KAAK,GAAE,EAAE,CAAC;AACnB,UAAE,MAAM;AAAA,MACV;AAGA,eAAS,OAAO,GAAE,GAAG;AAAE,eAAO,IAAE;AAAA,MAAG;AACnC,eAAS,MAAM,GAAG;AAAE,YAAI,IAAI,IAAI;AAAG,aAAK,UAAU,GAAE,QAAO,CAAC;AAAG,eAAO;AAAA,MAAG;AAGzE,eAAS,MAAM,GAAE,GAAG;AAAE,eAAO,IAAE;AAAA,MAAG;AAClC,eAAS,KAAK,GAAG;AAAE,YAAI,IAAI,IAAI;AAAG,aAAK,UAAU,GAAE,OAAM,CAAC;AAAG,eAAO;AAAA,MAAG;AAGvE,eAAS,OAAO,GAAE,GAAG;AAAE,eAAO,IAAE;AAAA,MAAG;AACnC,eAAS,MAAM,GAAG;AAAE,YAAI,IAAI,IAAI;AAAG,aAAK,UAAU,GAAE,QAAO,CAAC;AAAG,eAAO;AAAA,MAAG;AAGzE,eAAS,UAAU,GAAE,GAAG;AAAE,eAAO,IAAE,CAAC;AAAA,MAAG;AACvC,eAAS,SAAS,GAAG;AAAE,YAAI,IAAI,IAAI;AAAG,aAAK,UAAU,GAAE,WAAU,CAAC;AAAG,eAAO;AAAA,MAAG;AAG/E,eAAS,QAAQ;AACf,YAAI,IAAI,IAAI;AACZ,iBAAQ,IAAI,GAAG,IAAI,KAAK,GAAG,EAAE,EAAG,GAAE,CAAC,IAAI,KAAK,KAAG,CAAC,KAAK,CAAC;AACtD,UAAE,IAAI,KAAK;AACX,UAAE,IAAI,CAAC,KAAK;AACZ,eAAO;AAAA,MACT;AAGA,eAAS,YAAY,GAAG;AACtB,YAAI,IAAI,IAAI;AACZ,YAAG,IAAI,EAAG,MAAK,SAAS,CAAC,GAAE,CAAC;AAAA,YAAQ,MAAK,SAAS,GAAE,CAAC;AACrD,eAAO;AAAA,MACT;AAGA,eAAS,aAAa,GAAG;AACvB,YAAI,IAAI,IAAI;AACZ,YAAG,IAAI,EAAG,MAAK,SAAS,CAAC,GAAE,CAAC;AAAA,YAAQ,MAAK,SAAS,GAAE,CAAC;AACrD,eAAO;AAAA,MACT;AAGA,eAAS,KAAK,GAAG;AACf,YAAG,KAAK,EAAG,QAAO;AAClB,YAAI,IAAI;AACR,aAAI,IAAE,UAAW,GAAG;AAAE,gBAAM;AAAI,eAAK;AAAA,QAAI;AACzC,aAAI,IAAE,QAAS,GAAG;AAAE,gBAAM;AAAG,eAAK;AAAA,QAAG;AACrC,aAAI,IAAE,OAAQ,GAAG;AAAE,gBAAM;AAAG,eAAK;AAAA,QAAG;AACpC,aAAI,IAAE,MAAM,GAAG;AAAE,gBAAM;AAAG,eAAK;AAAA,QAAG;AAClC,aAAI,IAAE,MAAM,EAAG,GAAE;AACjB,eAAO;AAAA,MACT;AAGA,eAAS,oBAAoB;AAC3B,iBAAQ,IAAI,GAAG,IAAI,KAAK,GAAG,EAAE;AAC3B,cAAG,KAAK,CAAC,KAAK,EAAG,QAAO,IAAE,KAAK,KAAG,KAAK,KAAK,CAAC,CAAC;AAChD,YAAG,KAAK,IAAI,EAAG,QAAO,KAAK,IAAE,KAAK;AAClC,eAAO;AAAA,MACT;AAGA,eAAS,KAAK,GAAG;AACf,YAAI,IAAI;AACR,eAAM,KAAK,GAAG;AAAE,eAAK,IAAE;AAAG,YAAE;AAAA,QAAG;AAC/B,eAAO;AAAA,MACT;AAGA,eAAS,aAAa;AACpB,YAAI,IAAI,GAAG,IAAI,KAAK,IAAE,KAAK;AAC3B,iBAAQ,IAAI,GAAG,IAAI,KAAK,GAAG,EAAE,EAAG,MAAK,KAAK,KAAK,CAAC,IAAE,CAAC;AACnD,eAAO;AAAA,MACT;AAGA,eAAS,UAAU,GAAG;AACpB,YAAI,IAAI,KAAK,MAAM,IAAE,KAAK,EAAE;AAC5B,YAAG,KAAK,KAAK,EAAG,QAAO,KAAK,KAAG;AAC/B,gBAAQ,KAAK,CAAC,IAAG,KAAI,IAAE,KAAK,OAAO;AAAA,MACrC;AAGA,eAAS,aAAa,GAAE,IAAI;AAC1B,YAAI,IAAI,WAAW,IAAI,UAAU,CAAC;AAClC,aAAK,UAAU,GAAE,IAAG,CAAC;AACrB,eAAO;AAAA,MACT;AAGA,eAAS,SAAS,GAAG;AAAE,eAAO,KAAK,UAAU,GAAE,KAAK;AAAA,MAAG;AAGvD,eAAS,WAAW,GAAG;AAAE,eAAO,KAAK,UAAU,GAAE,SAAS;AAAA,MAAG;AAG7D,eAAS,UAAU,GAAG;AAAE,eAAO,KAAK,UAAU,GAAE,MAAM;AAAA,MAAG;AAGzD,eAAS,SAAS,GAAE,GAAG;AACrB,YAAI,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,IAAI,EAAE,GAAE,KAAK,CAAC;AACzC,eAAM,IAAI,GAAG;AACX,eAAK,KAAK,CAAC,IAAE,EAAE,CAAC;AAChB,YAAE,GAAG,IAAI,IAAE,KAAK;AAChB,gBAAM,KAAK;AAAA,QACb;AACA,YAAG,EAAE,IAAI,KAAK,GAAG;AACf,eAAK,EAAE;AACP,iBAAM,IAAI,KAAK,GAAG;AAChB,iBAAK,KAAK,CAAC;AACX,cAAE,GAAG,IAAI,IAAE,KAAK;AAChB,kBAAM,KAAK;AAAA,UACb;AACA,eAAK,KAAK;AAAA,QACZ,OACK;AACH,eAAK,KAAK;AACV,iBAAM,IAAI,EAAE,GAAG;AACb,iBAAK,EAAE,CAAC;AACR,cAAE,GAAG,IAAI,IAAE,KAAK;AAChB,kBAAM,KAAK;AAAA,UACb;AACA,eAAK,EAAE;AAAA,QACT;AACA,UAAE,IAAK,IAAE,IAAG,KAAG;AACf,YAAG,IAAI,EAAG,GAAE,GAAG,IAAI;AAAA,iBACX,IAAI,GAAI,GAAE,GAAG,IAAI,KAAK,KAAG;AACjC,UAAE,IAAI;AACN,UAAE,MAAM;AAAA,MACV;AAGA,eAAS,MAAM,GAAG;AAAE,YAAI,IAAI,IAAI;AAAG,aAAK,MAAM,GAAE,CAAC;AAAG,eAAO;AAAA,MAAG;AAG9D,eAAS,WAAW,GAAG;AAAE,YAAI,IAAI,IAAI;AAAG,aAAK,MAAM,GAAE,CAAC;AAAG,eAAO;AAAA,MAAG;AAGnE,eAAS,WAAW,GAAG;AAAE,YAAI,IAAI,IAAI;AAAG,aAAK,WAAW,GAAE,CAAC;AAAG,eAAO;AAAA,MAAG;AAGxE,eAAS,WAAW;AAAE,YAAI,IAAI,IAAI;AAAG,aAAK,SAAS,CAAC;AAAG,eAAO;AAAA,MAAG;AAGjE,eAAS,SAAS,GAAG;AAAE,YAAI,IAAI,IAAI;AAAG,aAAK,SAAS,GAAE,GAAE,IAAI;AAAG,eAAO;AAAA,MAAG;AAGzE,eAAS,YAAY,GAAG;AAAE,YAAI,IAAI,IAAI;AAAG,aAAK,SAAS,GAAE,MAAK,CAAC;AAAG,eAAO;AAAA,MAAG;AAG5E,eAAS,qBAAqB,GAAG;AAC/B,YAAI,IAAI,IAAI,GAAG,IAAI,IAAI;AACvB,aAAK,SAAS,GAAE,GAAE,CAAC;AACnB,eAAO,IAAI,MAAM,GAAE,CAAC;AAAA,MACtB;AAGA,eAAS,aAAa,GAAG;AACvB,aAAK,KAAK,CAAC,IAAI,KAAK,GAAG,GAAE,IAAE,GAAE,MAAK,GAAE,GAAE,KAAK,CAAC;AAC5C,UAAE,KAAK;AACP,aAAK,MAAM;AAAA,MACb;AAGA,eAAS,cAAc,GAAE,GAAG;AAC1B,YAAG,KAAK,EAAG;AACX,eAAM,KAAK,KAAK,EAAG,MAAK,KAAK,GAAG,IAAI;AACpC,aAAK,CAAC,KAAK;AACX,eAAM,KAAK,CAAC,KAAK,KAAK,IAAI;AACxB,eAAK,CAAC,KAAK,KAAK;AAChB,cAAG,EAAE,KAAK,KAAK,EAAG,MAAK,KAAK,GAAG,IAAI;AACnC,YAAE,KAAK,CAAC;AAAA,QACV;AAAA,MACF;AAGA,eAAS,UAAU;AAAA,MAAC;AACpB,eAAS,KAAK,GAAG;AAAE,eAAO;AAAA,MAAG;AAC7B,eAAS,OAAO,GAAE,GAAE,GAAG;AAAE,UAAE,WAAW,GAAE,CAAC;AAAA,MAAG;AAC5C,eAAS,OAAO,GAAE,GAAG;AAAE,UAAE,SAAS,CAAC;AAAA,MAAG;AAEtC,cAAQ,UAAU,UAAU;AAC5B,cAAQ,UAAU,SAAS;AAC3B,cAAQ,UAAU,QAAQ;AAC1B,cAAQ,UAAU,QAAQ;AAG1B,eAAS,MAAM,GAAG;AAAE,eAAO,KAAK,IAAI,GAAE,IAAI,QAAQ,CAAC;AAAA,MAAG;AAItD,eAAS,mBAAmB,GAAE,GAAE,GAAG;AACjC,YAAI,IAAI,KAAK,IAAI,KAAK,IAAE,EAAE,GAAE,CAAC;AAC7B,UAAE,IAAI;AACN,UAAE,IAAI;AACN,eAAM,IAAI,EAAG,GAAE,EAAE,CAAC,IAAI;AACtB,YAAI;AACJ,aAAI,IAAI,EAAE,IAAE,KAAK,GAAG,IAAI,GAAG,EAAE,EAAG,GAAE,IAAE,KAAK,CAAC,IAAI,KAAK,GAAG,GAAE,EAAE,CAAC,GAAE,GAAE,GAAE,GAAE,KAAK,CAAC;AACzE,aAAI,IAAI,KAAK,IAAI,EAAE,GAAE,CAAC,GAAG,IAAI,GAAG,EAAE,EAAG,MAAK,GAAG,GAAE,EAAE,CAAC,GAAE,GAAE,GAAE,GAAE,IAAE,CAAC;AAC7D,UAAE,MAAM;AAAA,MACV;AAIA,eAAS,mBAAmB,GAAE,GAAE,GAAG;AACjC,UAAE;AACF,YAAI,IAAI,EAAE,IAAI,KAAK,IAAE,EAAE,IAAE;AACzB,UAAE,IAAI;AACN,eAAM,EAAE,KAAK,EAAG,GAAE,CAAC,IAAI;AACvB,aAAI,IAAI,KAAK,IAAI,IAAE,KAAK,GAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE;AACvC,YAAE,KAAK,IAAE,IAAE,CAAC,IAAI,KAAK,GAAG,IAAE,GAAE,EAAE,CAAC,GAAE,GAAE,GAAE,GAAE,KAAK,IAAE,IAAE,CAAC;AACnD,UAAE,MAAM;AACR,UAAE,UAAU,GAAE,CAAC;AAAA,MACjB;AAGA,eAAS,QAAQ,GAAG;AAElB,aAAK,KAAK,IAAI;AACd,aAAK,KAAK,IAAI;AACd,mBAAW,IAAI,UAAU,IAAE,EAAE,GAAE,KAAK,EAAE;AACtC,aAAK,KAAK,KAAK,GAAG,OAAO,CAAC;AAC1B,aAAK,IAAI;AAAA,MACX;AAEA,eAAS,eAAe,GAAG;AACzB,YAAG,EAAE,IAAI,KAAK,EAAE,IAAI,IAAE,KAAK,EAAE,EAAG,QAAO,EAAE,IAAI,KAAK,CAAC;AAAA,iBAC3C,EAAE,UAAU,KAAK,CAAC,IAAI,EAAG,QAAO;AAAA,aACnC;AAAE,cAAI,IAAI,IAAI;AAAG,YAAE,OAAO,CAAC;AAAG,eAAK,OAAO,CAAC;AAAG,iBAAO;AAAA,QAAG;AAAA,MAC/D;AAEA,eAAS,cAAc,GAAG;AAAE,eAAO;AAAA,MAAG;AAGtC,eAAS,cAAc,GAAG;AACxB,UAAE,UAAU,KAAK,EAAE,IAAE,GAAE,KAAK,EAAE;AAC9B,YAAG,EAAE,IAAI,KAAK,EAAE,IAAE,GAAG;AAAE,YAAE,IAAI,KAAK,EAAE,IAAE;AAAG,YAAE,MAAM;AAAA,QAAG;AACpD,aAAK,GAAG,gBAAgB,KAAK,IAAG,KAAK,EAAE,IAAE,GAAE,KAAK,EAAE;AAClD,aAAK,EAAE,gBAAgB,KAAK,IAAG,KAAK,EAAE,IAAE,GAAE,KAAK,EAAE;AACjD,eAAM,EAAE,UAAU,KAAK,EAAE,IAAI,EAAG,GAAE,WAAW,GAAE,KAAK,EAAE,IAAE,CAAC;AACzD,UAAE,MAAM,KAAK,IAAG,CAAC;AACjB,eAAM,EAAE,UAAU,KAAK,CAAC,KAAK,EAAG,GAAE,MAAM,KAAK,GAAE,CAAC;AAAA,MAClD;AAGA,eAAS,aAAa,GAAE,GAAG;AAAE,UAAE,SAAS,CAAC;AAAG,aAAK,OAAO,CAAC;AAAA,MAAG;AAG5D,eAAS,aAAa,GAAE,GAAE,GAAG;AAAE,UAAE,WAAW,GAAE,CAAC;AAAG,aAAK,OAAO,CAAC;AAAA,MAAG;AAElE,cAAQ,UAAU,UAAU;AAC5B,cAAQ,UAAU,SAAS;AAC3B,cAAQ,UAAU,SAAS;AAC3B,cAAQ,UAAU,QAAQ;AAC1B,cAAQ,UAAU,QAAQ;AAG1B,eAAS,SAAS,GAAE,GAAG;AACrB,YAAI,IAAI,EAAE,UAAU,GAAG,GAAG,IAAI,IAAI,CAAC,GAAGC;AACtC,YAAG,KAAK,EAAG,QAAO;AAAA,iBACV,IAAI,GAAI,KAAI;AAAA,iBACZ,IAAI,GAAI,KAAI;AAAA,iBACZ,IAAI,IAAK,KAAI;AAAA,iBACb,IAAI,IAAK,KAAI;AAAA,YAChB,KAAI;AACT,YAAG,IAAI;AACL,UAAAA,KAAI,IAAI,QAAQ,CAAC;AAAA,iBACX,EAAE,OAAO;AACf,UAAAA,KAAI,IAAI,QAAQ,CAAC;AAAA;AAEjB,UAAAA,KAAI,IAAI,WAAW,CAAC;AAGtB,YAAI,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,KAAK,IAAE,GAAG,MAAM,KAAG,KAAG;AAClD,UAAE,CAAC,IAAIA,GAAE,QAAQ,IAAI;AACrB,YAAG,IAAI,GAAG;AACR,cAAI,KAAK,IAAI;AACb,UAAAA,GAAE,MAAM,EAAE,CAAC,GAAE,EAAE;AACf,iBAAM,KAAK,IAAI;AACb,cAAE,CAAC,IAAI,IAAI;AACX,YAAAA,GAAE,MAAM,IAAG,EAAE,IAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AACtB,iBAAK;AAAA,UACP;AAAA,QACF;AAEA,YAAI,IAAI,EAAE,IAAE,GAAG,GAAG,MAAM,MAAM,KAAK,IAAI,GAAGD;AAC1C,YAAI,MAAM,EAAE,CAAC,CAAC,IAAE;AAChB,eAAM,KAAK,GAAG;AACZ,cAAG,KAAK,GAAI,KAAK,EAAE,CAAC,KAAI,IAAE,KAAK;AAAA,eAC1B;AACH,iBAAK,EAAE,CAAC,KAAI,KAAI,IAAE,KAAI,MAAM,KAAG;AAC/B,gBAAG,IAAI,EAAG,MAAK,EAAE,IAAE,CAAC,KAAI,KAAK,KAAG,IAAE;AAAA,UACpC;AAEA,cAAI;AACJ,kBAAO,IAAE,MAAM,GAAG;AAAE,kBAAM;AAAG,cAAE;AAAA,UAAG;AAClC,eAAI,KAAK,KAAK,GAAG;AAAE,iBAAK,KAAK;AAAI,cAAE;AAAA,UAAG;AACtC,cAAG,KAAK;AACN,cAAE,CAAC,EAAE,OAAO,CAAC;AACb,kBAAM;AAAA,UACR,OACK;AACH,mBAAM,IAAI,GAAG;AAAE,cAAAC,GAAE,MAAM,GAAE,EAAE;AAAG,cAAAA,GAAE,MAAM,IAAG,CAAC;AAAG,mBAAK;AAAA,YAAG;AACrD,gBAAG,IAAI,EAAG,CAAAA,GAAE,MAAM,GAAE,EAAE;AAAA,iBAAQ;AAAE,cAAAD,KAAI;AAAG,kBAAI;AAAI,mBAAKA;AAAA,YAAG;AACvD,YAAAC,GAAE,MAAM,IAAG,EAAE,CAAC,GAAE,CAAC;AAAA,UACnB;AAEA,iBAAM,KAAK,MAAM,EAAE,CAAC,IAAG,KAAG,MAAO,GAAG;AAClC,YAAAA,GAAE,MAAM,GAAE,EAAE;AAAG,YAAAD,KAAI;AAAG,gBAAI;AAAI,iBAAKA;AACnC,gBAAG,EAAE,IAAI,GAAG;AAAE,kBAAI,KAAK,KAAG;AAAG,gBAAE;AAAA,YAAG;AAAA,UACpC;AAAA,QACF;AACA,eAAOC,GAAE,OAAO,CAAC;AAAA,MACnB;AAGA,eAAS,MAAM,GAAG;AAChB,YAAI,IAAK,KAAK,IAAE,IAAG,KAAK,OAAO,IAAE,KAAK,MAAM;AAC5C,YAAI,IAAK,EAAE,IAAE,IAAG,EAAE,OAAO,IAAE,EAAE,MAAM;AACnC,YAAG,EAAE,UAAU,CAAC,IAAI,GAAG;AAAE,cAAID,KAAI;AAAG,cAAI;AAAG,cAAIA;AAAA,QAAG;AAClD,YAAI,IAAI,EAAE,gBAAgB,GAAG,IAAI,EAAE,gBAAgB;AACnD,YAAG,IAAI,EAAG,QAAO;AACjB,YAAG,IAAI,EAAG,KAAI;AACd,YAAG,IAAI,GAAG;AACR,YAAE,SAAS,GAAE,CAAC;AACd,YAAE,SAAS,GAAE,CAAC;AAAA,QAChB;AACA,eAAM,EAAE,OAAO,IAAI,GAAG;AACpB,eAAI,IAAI,EAAE,gBAAgB,KAAK,EAAG,GAAE,SAAS,GAAE,CAAC;AAChD,eAAI,IAAI,EAAE,gBAAgB,KAAK,EAAG,GAAE,SAAS,GAAE,CAAC;AAChD,cAAG,EAAE,UAAU,CAAC,KAAK,GAAG;AACtB,cAAE,MAAM,GAAE,CAAC;AACX,cAAE,SAAS,GAAE,CAAC;AAAA,UAChB,OACK;AACH,cAAE,MAAM,GAAE,CAAC;AACX,cAAE,SAAS,GAAE,CAAC;AAAA,UAChB;AAAA,QACF;AACA,YAAG,IAAI,EAAG,GAAE,SAAS,GAAE,CAAC;AACxB,eAAO;AAAA,MACT;AAGA,eAAS,UAAU,GAAG;AACpB,YAAG,KAAK,EAAG,QAAO;AAClB,YAAI,IAAI,KAAK,KAAG,GAAG,IAAK,KAAK,IAAE,IAAG,IAAE,IAAE;AACtC,YAAG,KAAK,IAAI;AACV,cAAG,KAAK,EAAG,KAAI,KAAK,CAAC,IAAE;AAAA,cAClB,UAAQ,IAAI,KAAK,IAAE,GAAG,KAAK,GAAG,EAAE,EAAG,MAAK,IAAE,IAAE,KAAK,CAAC,KAAG;AAC5D,eAAO;AAAA,MACT;AAGA,eAAS,aAAa,GAAG;AACvB,YAAI,KAAK,EAAE,OAAO;AAClB,YAAI,KAAK,OAAO,KAAK,MAAO,EAAE,OAAO,KAAK,EAAG,QAAO,WAAW;AAC/D,YAAI,IAAI,EAAE,MAAM,GAAG,IAAI,KAAK,MAAM;AAClC,YAAI,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC;AACjD,eAAM,EAAE,OAAO,KAAK,GAAG;AACrB,iBAAM,EAAE,OAAO,GAAG;AAChB,cAAE,SAAS,GAAE,CAAC;AACd,gBAAG,IAAI;AACL,kBAAG,CAAC,EAAE,OAAO,KAAK,CAAC,EAAE,OAAO,GAAG;AAAE,kBAAE,MAAM,MAAK,CAAC;AAAG,kBAAE,MAAM,GAAE,CAAC;AAAA,cAAG;AAChE,gBAAE,SAAS,GAAE,CAAC;AAAA,YAChB,WACQ,CAAC,EAAE,OAAO,EAAG,GAAE,MAAM,GAAE,CAAC;AAChC,cAAE,SAAS,GAAE,CAAC;AAAA,UAChB;AACA,iBAAM,EAAE,OAAO,GAAG;AAChB,cAAE,SAAS,GAAE,CAAC;AACd,gBAAG,IAAI;AACL,kBAAG,CAAC,EAAE,OAAO,KAAK,CAAC,EAAE,OAAO,GAAG;AAAE,kBAAE,MAAM,MAAK,CAAC;AAAG,kBAAE,MAAM,GAAE,CAAC;AAAA,cAAG;AAChE,gBAAE,SAAS,GAAE,CAAC;AAAA,YAChB,WACQ,CAAC,EAAE,OAAO,EAAG,GAAE,MAAM,GAAE,CAAC;AAChC,cAAE,SAAS,GAAE,CAAC;AAAA,UAChB;AACA,cAAG,EAAE,UAAU,CAAC,KAAK,GAAG;AACtB,cAAE,MAAM,GAAE,CAAC;AACX,gBAAG,GAAI,GAAE,MAAM,GAAE,CAAC;AAClB,cAAE,MAAM,GAAE,CAAC;AAAA,UACb,OACK;AACH,cAAE,MAAM,GAAE,CAAC;AACX,gBAAG,GAAI,GAAE,MAAM,GAAE,CAAC;AAClB,cAAE,MAAM,GAAE,CAAC;AAAA,UACb;AAAA,QACF;AACA,YAAG,EAAE,UAAU,WAAW,GAAG,KAAK,EAAG,QAAO,WAAW;AACvD,YAAG,EAAE,UAAU,CAAC,KAAK,EAAG,QAAO,EAAE,SAAS,CAAC;AAC3C,YAAG,EAAE,OAAO,IAAI,EAAG,GAAE,MAAM,GAAE,CAAC;AAAA,YAAQ,QAAO;AAC7C,YAAG,EAAE,OAAO,IAAI,EAAG,QAAO,EAAE,IAAI,CAAC;AAAA,YAAQ,QAAO;AAAA,MAClD;AAEA,UAAI,YAAY,CAAC,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,GAAG;AACnpB,UAAI,SAAS,KAAG,MAAI,UAAU,UAAU,SAAO,CAAC;AAGhD,eAAS,kBAAkBA,IAAG;AAC5B,YAAI,GAAG,IAAI,KAAK,IAAI;AACpB,YAAG,EAAE,KAAK,KAAK,EAAE,CAAC,KAAK,UAAU,UAAU,SAAO,CAAC,GAAG;AACpD,eAAI,IAAI,GAAG,IAAI,UAAU,QAAQ,EAAE;AACjC,gBAAG,EAAE,CAAC,KAAK,UAAU,CAAC,EAAG,QAAO;AAClC,iBAAO;AAAA,QACT;AACA,YAAG,EAAE,OAAO,EAAG,QAAO;AACtB,YAAI;AACJ,eAAM,IAAI,UAAU,QAAQ;AAC1B,cAAI,IAAI,UAAU,CAAC,GAAG,IAAI,IAAE;AAC5B,iBAAM,IAAI,UAAU,UAAU,IAAI,MAAO,MAAK,UAAU,GAAG;AAC3D,cAAI,EAAE,OAAO,CAAC;AACd,iBAAM,IAAI,EAAG,KAAG,IAAE,UAAU,GAAG,KAAK,EAAG,QAAO;AAAA,QAChD;AACA,eAAO,EAAE,YAAYA,EAAC;AAAA,MACxB;AAGA,eAAS,eAAeA,IAAG;AACzB,YAAI,KAAK,KAAK,SAAS,WAAW,GAAG;AACrC,YAAI,IAAI,GAAG,gBAAgB;AAC3B,YAAG,KAAK,EAAG,QAAO;AAClB,YAAI,IAAI,GAAG,WAAW,CAAC;AACvB,QAAAA,KAAKA,KAAE,KAAI;AACX,YAAGA,KAAI,UAAU,OAAQ,CAAAA,KAAI,UAAU;AACvC,YAAI,IAAI,IAAI;AACZ,iBAAQ,IAAI,GAAG,IAAIA,IAAG,EAAE,GAAG;AAEzB,YAAE,QAAQ,UAAU,KAAK,MAAM,KAAK,OAAO,IAAE,UAAU,MAAM,CAAC,CAAC;AAC/D,cAAI,IAAI,EAAE,OAAO,GAAE,IAAI;AACvB,cAAG,EAAE,UAAU,WAAW,GAAG,KAAK,KAAK,EAAE,UAAU,EAAE,KAAK,GAAG;AAC3D,gBAAI,IAAI;AACR,mBAAM,MAAM,KAAK,EAAE,UAAU,EAAE,KAAK,GAAG;AACrC,kBAAI,EAAE,UAAU,GAAE,IAAI;AACtB,kBAAG,EAAE,UAAU,WAAW,GAAG,KAAK,EAAG,QAAO;AAAA,YAC9C;AACA,gBAAG,EAAE,UAAU,EAAE,KAAK,EAAG,QAAO;AAAA,UAClC;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAGA,iBAAW,UAAU,YAAY;AACjC,iBAAW,UAAU,UAAU;AAC/B,iBAAW,UAAU,YAAY;AACjC,iBAAW,UAAU,aAAa;AAClC,iBAAW,UAAU,YAAY;AACjC,iBAAW,UAAU,YAAY;AACjC,iBAAW,UAAU,QAAQ;AAC7B,iBAAW,UAAU,YAAY;AACjC,iBAAW,UAAU,aAAa;AAClC,iBAAW,UAAU,kBAAkB;AACvC,iBAAW,UAAU,kBAAkB;AACvC,iBAAW,UAAU,SAAS;AAC9B,iBAAW,UAAU,cAAc;AAGnC,iBAAW,UAAU,QAAQ;AAC7B,iBAAW,UAAU,WAAW;AAChC,iBAAW,UAAU,YAAY;AACjC,iBAAW,UAAU,aAAa;AAClC,iBAAW,UAAU,SAAS;AAC9B,iBAAW,UAAU,cAAc;AACnC,iBAAW,UAAU,SAAS;AAC9B,iBAAW,UAAU,MAAM;AAC3B,iBAAW,UAAU,MAAM;AAC3B,iBAAW,UAAU,MAAM;AAC3B,iBAAW,UAAU,KAAK;AAC1B,iBAAW,UAAU,MAAM;AAC3B,iBAAW,UAAU,SAAS;AAC9B,iBAAW,UAAU,MAAM;AAC3B,iBAAW,UAAU,YAAY;AACjC,iBAAW,UAAU,aAAa;AAClC,iBAAW,UAAU,kBAAkB;AACvC,iBAAW,UAAU,WAAW;AAChC,iBAAW,UAAU,UAAU;AAC/B,iBAAW,UAAU,SAAS;AAC9B,iBAAW,UAAU,WAAW;AAChC,iBAAW,UAAU,UAAU;AAC/B,iBAAW,UAAU,MAAM;AAC3B,iBAAW,UAAU,WAAW;AAChC,iBAAW,UAAU,WAAW;AAChC,iBAAW,UAAU,SAAS;AAC9B,iBAAW,UAAU,YAAY;AACjC,iBAAW,UAAU,qBAAqB;AAC1C,iBAAW,UAAU,SAAS;AAC9B,iBAAW,UAAU,aAAa;AAClC,iBAAW,UAAU,MAAM;AAC3B,iBAAW,UAAU,MAAM;AAC3B,iBAAW,UAAU,kBAAkB;AAGvC,iBAAW,UAAU,SAAS;AAG9B,iBAAW,UAAU,UAAU;AAiB/B,UAAI;AACJ,UAAI;AACJ,UAAI;AAGJ,eAAS,aAAa,GAAG;AACvB,iBAAS,UAAU,KAAK,IAAI;AAC5B,iBAAS,UAAU,KAAM,KAAK,IAAK;AACnC,iBAAS,UAAU,KAAM,KAAK,KAAM;AACpC,iBAAS,UAAU,KAAM,KAAK,KAAM;AACpC,YAAG,YAAY,UAAW,aAAY;AAAA,MACxC;AAGA,eAAS,gBAAgB;AACvB,sBAAa,oBAAI,KAAK,GAAE,QAAQ,CAAC;AAAA,MACnC;AAGA,UAAG,YAAY,MAAM;AACnB,mBAAW,IAAI,MAAM;AACrB,mBAAW;AACX,YAAI;AACJ,YAAG,OAAO,WAAW,eAAe,OAAO,QAAQ;AACjD,cAAI,OAAO,OAAO,iBAAiB;AAEjC,gBAAI,KAAK,IAAI,WAAW,EAAE;AAC1B,mBAAO,OAAO,gBAAgB,EAAE;AAChC,iBAAI,IAAI,GAAG,IAAI,IAAI,EAAE;AACnB,uBAAS,UAAU,IAAI,GAAG,CAAC;AAAA,UAC/B,WACQ,UAAU,WAAW,cAAc,UAAU,aAAa,KAAK;AAErE,gBAAI,IAAI,OAAO,OAAO,OAAO,EAAE;AAC/B,iBAAI,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAE;AACzB,uBAAS,UAAU,IAAI,EAAE,WAAW,CAAC,IAAI;AAAA,UAC7C;AAAA,QACF;AACA,eAAM,WAAW,WAAW;AAC1B,cAAI,KAAK,MAAM,QAAQ,KAAK,OAAO,CAAC;AACpC,mBAAS,UAAU,IAAI,MAAM;AAC7B,mBAAS,UAAU,IAAI,IAAI;AAAA,QAC7B;AACA,mBAAW;AACX,sBAAc;AAAA,MAGhB;AAEA,eAAS,eAAe;AACtB,YAAG,aAAa,MAAM;AACpB,wBAAc;AACd,sBAAY,cAAc;AAC1B,oBAAU,KAAK,QAAQ;AACvB,eAAI,WAAW,GAAG,WAAW,SAAS,QAAQ,EAAE;AAC9C,qBAAS,QAAQ,IAAI;AACvB,qBAAW;AAAA,QAEb;AAEA,eAAO,UAAU,KAAK;AAAA,MACxB;AAEA,eAAS,cAAc,IAAI;AACzB,YAAI;AACJ,aAAI,IAAI,GAAG,IAAI,GAAG,QAAQ,EAAE,EAAG,IAAG,CAAC,IAAI,aAAa;AAAA,MACtD;AAEA,eAAS,eAAe;AAAA,MAAC;AAEzB,mBAAa,UAAU,YAAY;AAInC,eAAS,UAAU;AACjB,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI,IAAI,MAAM;AAAA,MACrB;AAGA,eAAS,SAAS,KAAK;AACrB,YAAI,GAAG,GAAGA;AACV,aAAI,IAAI,GAAG,IAAI,KAAK,EAAE;AACpB,eAAK,EAAE,CAAC,IAAI;AACd,YAAI;AACJ,aAAI,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AACvB,cAAK,IAAI,KAAK,EAAE,CAAC,IAAI,IAAI,IAAI,IAAI,MAAM,IAAK;AAC5C,UAAAA,KAAI,KAAK,EAAE,CAAC;AACZ,eAAK,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC;AACpB,eAAK,EAAE,CAAC,IAAIA;AAAA,QACd;AACA,aAAK,IAAI;AACT,aAAK,IAAI;AAAA,MACX;AAEA,eAAS,WAAW;AAClB,YAAIA;AACJ,aAAK,IAAK,KAAK,IAAI,IAAK;AACxB,aAAK,IAAK,KAAK,IAAI,KAAK,EAAE,KAAK,CAAC,IAAK;AACrC,QAAAA,KAAI,KAAK,EAAE,KAAK,CAAC;AACjB,aAAK,EAAE,KAAK,CAAC,IAAI,KAAK,EAAE,KAAK,CAAC;AAC9B,aAAK,EAAE,KAAK,CAAC,IAAIA;AACjB,eAAO,KAAK,EAAGA,KAAI,KAAK,EAAE,KAAK,CAAC,IAAK,GAAG;AAAA,MAC1C;AAEA,cAAQ,UAAU,OAAO;AACzB,cAAQ,UAAU,OAAO;AAGzB,eAAS,gBAAgB;AACvB,eAAO,IAAI,QAAQ;AAAA,MACrB;AAIA,UAAI,YAAY;AAEhB,UAAI,OAAO,YAAY,aAAa;AAChC,kBAAU,OAAO,UAAU;AAAA,UACvB,SAAS;AAAA,UACT;AAAA,UACA;AAAA,QACJ;AAAA,MACJ,OAAO;AACH,aAAK,OAAO;AAAA,UACV;AAAA,UACA;AAAA,QACF;AAAA,MACJ;AAAA,IAEJ,GAAG,KAAK,OAAI;AAAA;AAAA;;;ACh1CZ;AAAA;AACA,QAAM,EAAC,WAAU,IAAI;AAErB,aAAS,cAAc,QAAQ;AAC7B,UAAI,IAAI,OAAO,SAAS,EAAE;AAC1B,UAAI,EAAE,CAAC,MAAM,KAAK;AAEhB,YAAI,EAAE,SAAS,MAAM,EAAG,KAAI,MAAM;AAAA,iBACzB,CAAC,EAAE,MAAM,QAAQ,EAAG,KAAI,OAAO;AAAA,MAC1C,OAAO;AAEL,YAAI,EAAE,OAAO,CAAC;AAEd,YAAI,MAAM,EAAE;AACZ,YAAI,MAAM,MAAM,EAAG,QAAO;AAAA,iBACjB,CAAC,EAAE,MAAM,QAAQ,EAAG,QAAO;AAEpC,YAAI,OAAO;AACX,iBAAS,IAAI,GAAG,IAAI,KAAK,IAAK,SAAQ;AACtC,eAAO,IAAI,WAAW,MAAM,EAAE;AAG9B,YAAI,KAAK,IAAI,MAAM,EAAE,IAAI,WAAW,GAAG;AACvC,YAAI,EAAE,SAAS,EAAE,EAAE,QAAQ,MAAM,EAAE;AAAA,MACrC;AACA,aAAO;AAAA,IACT;AAEA,QAAM,aAAN,MAAiB;AAAA,MACf,cAAc;AACZ,aAAK,MAAM;AACX,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AAAA,MACX;AAAA;AAAA;AAAA;AAAA,MAKA,gBAAgB;AACd,YAAI,CAAC,KAAK,KAAK;AACb,eAAK,IAAI,KAAK,SAAS;AACvB,eAAK,IAAI,KAAK,UAAU;AACxB,eAAK,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK;AAAA,QACpC;AACA,eAAO,KAAK;AAAA,MACd;AAAA,MAEA,YAAY;AACV,cAAM,IAAI,KAAK,EAAE,SAAS;AAC1B,YAAI,OAAO,EAAE,SAAS,EAAE;AACxB,YAAI,KAAK,SAAS,MAAM,EAAG,QAAO,MAAM;AAExC,YAAI,IAAI,KAAK;AAEX,iBAAO;AAAA,QACT,OAAO;AAEL,gBAAM,OAAO,MAAM,KAAK,SAAS;AACjC,iBAAO,KAAK,SAAS,EAAE,IAAI;AAAA,QAC7B;AAAA,MACF;AAAA,MAEA,WAAW;AACT,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAM,aAAN,cAAyB,WAAW;AAAA,MAClC,YAAY,QAAQ;AAClB,cAAM;AAEN,aAAK,IAAI;AACT,YAAI,OAAQ,MAAK,IAAI,cAAc,MAAM;AAAA,MAC3C;AAAA,MAEA,WAAW;AACT,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAEA,QAAM,cAAN,cAA0B,WAAW;AAAA,MACnC,YAAY,WAAW;AACrB,cAAM;AAEN,aAAK,IAAI;AACT,aAAK,YAAY;AAAA,MACnB;AAAA,MAEA,WAAW;AACT,aAAK,IAAI,KAAK,UAAU,IAAI,gBAAc,WAAW,cAAc,CAAC,EAAE,KAAK,EAAE;AAC7E,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAKA,aAAS,UAAU,KAAK,OAAO;AAC7B,UAAI,CAAC,IAAI,QAAQ,CAAC,IAAI,EAAG,QAAO;AAChC,aAAO,CAAC,IAAI,OAAO,QAAQ,GAAG,CAAC,IAAI,MAAO;AAAA,IAC5C;AAKA,aAAS,KAAK,KAAK,OAAO;AAExB,YAAM,MAAM,UAAU,KAAK,KAAK;AAChC,YAAM,IAAI,IAAI,OAAO,QAAQ,GAAG,MAAM,CAAC;AAEvC,UAAI,CAAC,EAAG,QAAO;AACf,YAAM,SAAS,CAAC,EAAE,CAAC,IAAI,IAAI,IAAI,WAAW,GAAG,EAAE,IAAI,IAAI,WAAW,EAAE,OAAO,CAAC,GAAG,EAAE;AAEjF,aAAO,OAAO,SAAS;AAAA,IACzB;AAKA,aAAS,YAAY,KAAK,OAAO;AAC/B,YAAM,MAAM,UAAU,KAAK,KAAK;AAChC,aAAO,SAAS,MAAM,KAAK;AAAA,IAC7B;AAEA,WAAO,UAAU;AAAA;AAAA;AAAA;AAAA,MAIf,UAAU,GAAG,GAAG;AACd,cAAM,OAAO,IAAI,WAAW,CAAC;AAC7B,cAAM,OAAO,IAAI,WAAW,CAAC;AAC7B,cAAM,SAAS,IAAI,YAAY,CAAC,MAAM,IAAI,CAAC;AAE3C,eAAO,OAAO,cAAc;AAAA,MAC9B;AAAA;AAAA;AAAA;AAAA,MAKA,UAAU,OAAO;AAIf,cAAM,QAAQ,YAAY,OAAO,CAAC;AAElC,cAAM,UAAU,YAAY,OAAO,KAAK;AACxC,cAAM,KAAK,KAAK,OAAO,KAAK;AAC5B,cAAM,KAAK,MAAM,OAAO,SAAS,KAAK,CAAC;AAEvC,cAAM,YAAY,UAAU,GAAG;AAC/B,cAAM,UAAU,YAAY,OAAO,SAAS;AAC5C,cAAM,KAAK,KAAK,OAAO,SAAS;AAChC,cAAM,KAAK,MAAM,OAAO,SAAS,KAAK,CAAC;AAEvC,cAAM,IAAI,IAAI,WAAW,IAAI,EAAE;AAC/B,cAAM,IAAI,IAAI,WAAW,IAAI,EAAE;AAE/B,eAAO,EAAC,GAAG,EAAC;AAAA,MACd;AAAA,IACF;AAAA;AAAA;;;AChKA;AAAA;AACA,QAAM,EAAC,WAAU,IAAI;AAUrB,QAAM,MAAM,IAAI,WAAW,GAAG;AAC9B,QAAM,QAAQ,IAAI,WAAW,GAAG;AAKhC,QAAM,mBAAN,MAAM,kBAAiB;AAAA,MACrB,YAAY,GAAG,GAAG;AAChB,aAAK,IAAI;AACT,aAAK,IAAI;AAAA,MAEX;AAAA;AAAA;AAAA;AAAA,MAKA,OAAO,OAAO;AACZ,YAAI,UAAU,KAAM,QAAO;AAC3B,eAAQ,KAAK,EAAE,OAAO,MAAM,CAAC,KAAK,KAAK,EAAE,OAAO,MAAM,CAAC;AAAA,MACzD;AAAA;AAAA;AAAA;AAAA,MAKA,eAAe;AACb,eAAO,KAAK;AAAA,MACd;AAAA;AAAA;AAAA;AAAA,MAKA,SAAS;AACP,eAAO,IAAI,kBAAiB,KAAK,GAAG,KAAK,EAAE,OAAO,EAAE,IAAI,KAAK,CAAC,CAAC;AAAA,MACjE;AAAA;AAAA;AAAA;AAAA,MAKA,IAAI,GAAG;AACL,eAAO,IAAI,kBAAiB,KAAK,GAAG,KAAK,EAAE,IAAI,EAAE,aAAa,CAAC,EAAE,IAAI,KAAK,CAAC,CAAC;AAAA,MAC9E;AAAA;AAAA;AAAA;AAAA,MAKA,SAAS,GAAG;AACV,eAAO,IAAI,kBAAiB,KAAK,GAAG,KAAK,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,IAAI,KAAK,CAAC,CAAC;AAAA,MACnF;AAAA;AAAA;AAAA;AAAA,MAKA,SAAS,GAAG;AACV,eAAO,IAAI,kBAAiB,KAAK,GAAG,KAAK,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,IAAI,KAAK,CAAC,CAAC;AAAA,MACnF;AAAA;AAAA;AAAA;AAAA,MAKA,OAAO,GAAG;AACR,eAAO,IAAI,kBAAiB,KAAK,GAAG,KAAK,EAAE,SAAS,EAAE,aAAa,EAAE,WAAW,KAAK,CAAC,CAAC,EAAE,IAAI,KAAK,CAAC,CAAC;AAAA,MACtG;AAAA;AAAA;AAAA;AAAA,MAKA,SAAS;AACP,eAAO,IAAI,kBAAiB,KAAK,GAAG,KAAK,EAAE,OAAO,EAAE,IAAI,KAAK,CAAC,CAAC;AAAA,MACjE;AAAA,IACF;AAEA,QAAM,YAAN,MAAM,WAAU;AAAA,MACd,YAAY,OAAO,GAAG,GAAG,GAAG;AAC1B,aAAK,QAAQ;AACb,aAAK,IAAI;AACT,aAAK,IAAI;AAET,aAAK,IAAI,KAAK,OAAO,WAAW,MAAM;AACtC,aAAK,OAAO;AAAA,MAEd;AAAA,MAEA,OAAO;AACL,YAAI,KAAK,SAAS,KAAM,MAAK,OAAO,KAAK,EAAE,WAAW,KAAK,MAAM,CAAC;AAElE,eAAO,KAAK,MAAM,eAAe,KAAK,EAAE,aAAa,EAAE,SAAS,KAAK,IAAI,EAAE,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,MAC9F;AAAA,MAEA,OAAO;AACL,YAAI,KAAK,SAAS,KAAM,MAAK,OAAO,KAAK,EAAE,WAAW,KAAK,MAAM,CAAC;AAElE,eAAO,KAAK,MAAM,eAAe,KAAK,EAAE,aAAa,EAAE,SAAS,KAAK,IAAI,EAAE,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,MAC9F;AAAA;AAAA;AAAA;AAAA,MAKA,OAAO,OAAO;AACZ,YAAI,UAAU,KAAM,QAAO;AAC3B,YAAI,KAAK,WAAW,EAAG,QAAO,MAAM,WAAW;AAC/C,YAAI,MAAM,WAAW,EAAG,QAAO,KAAK,WAAW;AAG/C,cAAM,IAAI,MAAM,EAAE,aAAa,EAAE,SAAS,KAAK,CAAC,EAAE,SAAS,KAAK,EAAE,aAAa,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,IAAI,KAAK,MAAM,CAAC;AACpH,YAAI,CAAC,EAAE,OAAO,WAAW,IAAI,EAAG,QAAO;AAGvC,cAAM,IAAI,MAAM,EAAE,aAAa,EAAE,SAAS,KAAK,CAAC,EAAE,SAAS,KAAK,EAAE,aAAa,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,IAAI,KAAK,MAAM,CAAC;AACpH,eAAO,EAAE,OAAO,WAAW,IAAI;AAAA,MACjC;AAAA;AAAA;AAAA;AAAA,MAKA,aAAa;AACX,YAAK,KAAK,MAAM,QAAU,KAAK,MAAM,KAAO,QAAO;AACnD,eAAO,KAAK,EAAE,OAAO,WAAW,IAAI,KAAK,CAAC,KAAK,EAAE,aAAa,EAAE,OAAO,WAAW,IAAI;AAAA,MACxF;AAAA;AAAA;AAAA;AAAA,MAKA,SAAS;AACP,eAAO,IAAI,WAAU,KAAK,OAAO,KAAK,GAAG,KAAK,EAAE,OAAO,GAAG,KAAK,CAAC;AAAA,MAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAsBA,IAAI,GAAG;AACL,YAAI,KAAK,WAAW,EAAG,QAAO;AAC9B,YAAI,EAAE,WAAW,EAAG,QAAO;AAE3B,cAAM,KAAK,KAAK,EAAE,aAAa;AAC/B,cAAM,KAAK,KAAK,EAAE,aAAa;AAC/B,cAAM,KAAK,KAAK;AAChB,cAAM,KAAK,EAAE,EAAE,aAAa;AAC5B,cAAM,KAAK,EAAE,EAAE,aAAa;AAC5B,cAAM,KAAK,EAAE;AACb,cAAM,IAAI,KAAK,MAAM;AAErB,cAAM,KAAK,GAAG,SAAS,EAAE,EAAE,IAAI,CAAC;AAChC,cAAM,KAAK,GAAG,SAAS,EAAE,EAAE,IAAI,CAAC;AAChC,cAAM,KAAK,GAAG,SAAS,EAAE;AACzB,cAAM,KAAK,GAAG,SAAS,EAAE,EAAE,IAAI,CAAC;AAChC,cAAM,KAAK,GAAG,SAAS,EAAE,EAAE,IAAI,CAAC;AAChC,cAAM,KAAK,GAAG,SAAS,EAAE;AAEzB,YAAI,WAAW,KAAK,OAAO,EAAE,GAAG;AAC9B,cAAI,WAAW,KAAK,OAAO,EAAE,GAAG;AAC9B,mBAAO,KAAK,MAAM;AAAA,UACpB;AACA,iBAAO,KAAK,MAAM;AAAA,QACpB;AAEA,cAAM,KAAK,GAAG,IAAI,EAAE;AACpB,cAAM,KAAK,GAAG,SAAS,EAAE,EAAE,IAAI,CAAC;AAChC,cAAM,KAAK,GAAG,OAAO,EAAE,IAAI,CAAC;AAC5B,cAAM,MAAM,GAAG,SAAS,EAAE,EAAE,IAAI,CAAC;AACjC,cAAM,MAAM,GAAG,SAAS,GAAG,OAAO,CAAC,EAAE,SAAS,GAAG,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC;AAEpE,cAAM,KAAK,GAAG,SAAS,GAAG,EAAE,IAAI,CAAC;AACjC,cAAM,KAAK,GAAG,SAAS,GAAG,SAAS,EAAE,EAAE,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,SAAS,GAAG,CAAC,EAAE,IAAI,CAAC;AACtF,cAAM,KAAK,IAAI,SAAS,EAAE,EAAE,IAAI,CAAC;AAEjC,eAAO,IAAI,WAAU,KAAK,OAAO,KAAK,MAAM,eAAe,EAAE,GAAG,KAAK,MAAM,eAAe,EAAE,GAAG,EAAE;AAAA,MACnG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAiBA,QAAQ;AACN,YAAI,KAAK,WAAW,EAAG,QAAO;AAC9B,YAAI,CAAC,KAAK,EAAE,aAAa,EAAE,OAAO,EAAG,QAAO,KAAK,MAAM;AAEvD,cAAM,KAAK,KAAK,EAAE,aAAa;AAC/B,cAAM,KAAK,KAAK,EAAE,aAAa;AAC/B,cAAM,KAAK,KAAK;AAChB,cAAM,IAAI,KAAK,MAAM;AACrB,cAAM,IAAI,KAAK,MAAM,EAAE,aAAa;AAEpC,cAAM,KAAK,GAAG,OAAO,EAAE,SAAS,KAAK,EAAE,IAAI,EAAE,SAAS,GAAG,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;AACzE,cAAM,KAAK,GAAG,UAAU,CAAC,EAAE,SAAS,EAAE,EAAE,IAAI,CAAC;AAC7C,cAAM,KAAK,GAAG,OAAO,EAAE,IAAI,CAAC;AAC5B,cAAM,KAAK,GAAG,SAAS,EAAE,EAAE,SAAS,EAAE,EAAE,IAAI,CAAC;AAC7C,cAAM,KAAK,GAAG,OAAO,EAAE,IAAI,CAAC;AAC5B,cAAM,KAAK,GAAG,OAAO,EAAE,SAAS,GAAG,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC;AAEtD,cAAM,KAAK,GAAG,SAAS,EAAE,EAAE,IAAI,CAAC;AAChC,cAAM,KAAK,GAAG,SAAS,GAAG,UAAU,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,SAAS,GAAG,UAAU,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC;AACjG,cAAM,KAAK,GAAG,SAAS,EAAE,EAAE,IAAI,CAAC;AAEhC,eAAO,IAAI,WAAU,KAAK,OAAO,KAAK,MAAM,eAAe,EAAE,GAAG,KAAK,MAAM,eAAe,EAAE,GAAG,EAAE;AAAA,MACnG;AAAA;AAAA;AAAA;AAAA,MAKA,SAAS,GAAG;AACV,YAAI,KAAK,WAAW,EAAG,QAAO;AAC9B,YAAI,CAAC,EAAE,OAAO,EAAG,QAAO,KAAK,MAAM;AAGnC,cAAM,KAAK,EAAE,SAAS,KAAK;AAC3B,cAAM,MAAM,KAAK,OAAO;AACxB,YAAI,IAAI;AAER,iBAAS,IAAI,GAAG,UAAU,IAAI,GAAG,IAAI,GAAG,KAAK;AAC3C,cAAI,EAAE,MAAM;AAEZ,gBAAM,QAAQ,GAAG,QAAQ,CAAC;AAC1B,gBAAM,OAAO,EAAE,QAAQ,CAAC;AAExB,cAAI,UAAU,MAAM;AAClB,gBAAI,EAAE,IAAI,QAAQ,OAAO,GAAG;AAAA,UAC9B;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAKA,QAAM,YAAN,MAAgB;AAAA,MACd,YAAY,GAAG,GAAG,GAAG;AACnB,aAAK,IAAI;AACT,aAAK,IAAI,KAAK,eAAe,CAAC;AAC9B,aAAK,IAAI,KAAK,eAAe,CAAC;AAC9B,aAAK,WAAW,IAAI,UAAU,MAAM,MAAM,IAAI;AAAA,MAChD;AAAA;AAAA;AAAA;AAAA,MAKA,OAAO,OAAO;AACZ,YAAI,UAAU,KAAM,QAAO;AAC3B,eAAQ,KAAK,EAAE,OAAO,MAAM,CAAC,KAAK,KAAK,EAAE,OAAO,MAAM,CAAC,KAAK,KAAK,EAAE,OAAO,MAAM,CAAC;AAAA,MACnF;AAAA;AAAA;AAAA;AAAA,MAKA,eAAe,GAAG;AAChB,eAAO,IAAI,iBAAiB,KAAK,GAAG,CAAC;AAAA,MACvC;AAAA;AAAA;AAAA;AAAA,MAKA,eAAe,GAAG;AAChB,gBAAQ,SAAS,EAAE,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG;AAAA;AAAA,UAEpC,KAAK;AACH,mBAAO,KAAK;AAAA,UACd,KAAK;AAAA,UACL,KAAK;AAEH,kBAAM,IAAI,KAAK,eAAe,IAAI,WAAW,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;AAG7D,gBAAI,IAAI,KAAK,eAAe,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE;AAAA,cACjD,EAAE,SAAS,KAAK,CAAC;AAAA,YACnB,EAAE,IAAI,KAAK,CAAC,EAAE,aAAa,EACxB;AAAA,cACC,KAAK,EAAE,OAAO,IAAI,WAAW,GAAG,CAAC,EAAE,IAAI,WAAW,GAAG;AAAA,cAAG,KAAK;AAAA,YAC/D,CAAC;AAEH,gBAAI,CAAC,EAAE,aAAa,EAAE,IAAI,GAAG,EAAE,OAAO,IAAI,WAAW,EAAE,OAAO,GAAG,CAAC,GAAG,EAAE,EAAE,SAAS,GAAG,CAAC,GAAG;AACvF,kBAAI,EAAE,OAAO;AAAA,YACf;AACA,mBAAO,IAAI,UAAU,MAAM,GAAG,CAAC;AAAA,UACjC,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,kBAAM,OAAO,EAAE,SAAS,KAAK;AAC7B,kBAAM,OAAO,EAAE,OAAO,GAAG,GAAG;AAC5B,kBAAM,OAAO,EAAE,OAAO,MAAM,GAAG,GAAG;AAElC,mBAAO,IAAI,UAAU,MAAM,KAAK,eAAe,IAAI,WAAW,MAAM,EAAE,CAAC,GAAG,KAAK,eAAe,IAAI,WAAW,MAAM,EAAE,CAAC,CAAC;AAAA,UACzH;AAEE,mBAAO;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;AC3UA;AAAA;AACA,QAAM,EAAC,YAAY,aAAY,IAAI;AACnC,QAAM,EAAC,UAAS,IAAI;AAEpB,QAAM,MAAM,IAAI,aAAa;AAC7B,QAAM,EAAC,OAAO,GAAG,EAAC,IAAI,gBAAgB;AAKtC,aAAS,iBAAiB;AACxB,aAAO;AAAA,IACT;AAKA,aAAS,kBAAkB;AAEzB,YAAM,IAAI,IAAI,WAAW,oEAAoE,EAAE;AAC/F,YAAM,IAAI,IAAI,WAAW,oEAAoE,EAAE;AAC/F,YAAM,IAAI,IAAI,WAAW,oEAAoE,EAAE;AAC/F,YAAME,SAAQ,IAAI,UAAU,GAAG,GAAG,CAAC;AAGnC,YAAM,QAAQ;AACd,YAAM,QAAQ;AACd,YAAMC,KAAID,OAAM,eAAe,OAAO,QAAQ,KAAK;AAEnD,YAAME,KAAI,IAAI,WAAW,oEAAoE,EAAE;AAE/F,aAAO,EAAC,OAAAF,QAAO,GAAAC,IAAG,GAAAC,GAAC;AAAA,IACrB;AAKA,aAAS,mBAAmB,GAAG,GAAG,GAAG;AACnC,YAAM,SAAS,IAAI,IAAI,WAAW,GAAG,GAAG,CAAC,IAAI,IAAI,WAAW,EAAE,UAAU,GAAG,GAAG;AAC9E,YAAM,IAAI,OAAO,IAAI,EAAE,SAAS,WAAW,GAAG,CAAC,EAAE,IAAI,WAAW,GAAG;AACnE,YAAM,aAAa,QAAQ,EAAE,SAAS,EAAE,GAAG,EAAE;AAE7C,YAAM,IAAI,EAAE,SAAS,CAAC;AACtB,YAAM,KAAK,QAAQ,EAAE,KAAK,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,EAAE;AAC3D,YAAM,KAAK,QAAQ,EAAE,KAAK,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,EAAE;AAC3D,YAAM,YAAY,OAAO,KAAK;AAE9B,aAAO,EAAC,YAAY,UAAS;AAAA,IAC/B;AAKA,aAAS,qBAAqB,GAAG;AAC/B,UAAI,EAAE,WAAW,IAAK,OAAM,IAAI,MAAM,gCAAgC;AAEtE,YAAM,OAAO,EAAE,SAAS,KAAK;AAC7B,YAAM,OAAO,EAAE,OAAO,GAAG,GAAG;AAC5B,YAAM,IAAI,IAAI,WAAW,EAAE,OAAO,MAAM,GAAG,GAAG,GAAG,EAAE;AAEnD,UAAI,SAAS;AACb,UAAI,EAAE,IAAI,IAAI,WAAW,GAAG,CAAC,EAAE,OAAO,WAAW,IAAI,EAAG,UAAS;AAEjE,aAAO,SAAS;AAAA,IAClB;AAKA,aAAS,UAAU,OAAO;AACxB,cAAQ,SAAS,mBAAmB,KAAK,CAAC;AAE1C,YAAM,SAAS,MAAM;AAGrB,YAAM,QAAQ,CAAC;AACf,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAM,MAAM,CAAC,MAAM,MAAM,WAAW,CAAC,IAAI,QAAU,KAAM,IAAI,IAAK;AAAA,MACpE;AAGA,YAAM,WAAW,CAAC;AAClB,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAM,OAAQ,MAAM,MAAM,CAAC,MAAO,KAAM,IAAI,IAAK,IAAM;AACvD,iBAAS,MAAM,SAAS,GAAG,SAAS,EAAE,CAAC;AACvC,iBAAS,MAAM,OAAO,IAAM,SAAS,EAAE,CAAC;AAAA,MAC1C;AAEA,aAAO,SAAS,KAAK,EAAE;AAAA,IACzB;AAKA,aAAS,QAAQ,OAAO,KAAK;AAC3B,UAAI,MAAM,UAAU,IAAK,QAAO;AAEhC,aAAQ,IAAI,MAAM,MAAM,MAAM,SAAS,CAAC,EAAG,KAAK,GAAG,IAAI;AAAA,IACzD;AAKA,aAAS,WAAW,KAAK;AACvB,aAAO,IAAI,IAAI,UAAQ;AACrB,eAAO,KAAK,SAAS,EAAE;AACvB,eAAO,KAAK,WAAW,IAAI,MAAM,OAAO;AAAA,MAC1C,CAAC,EAAE,KAAK,EAAE;AAAA,IACZ;AAKA,aAAS,YAAY,KAAK;AACxB,YAAM,QAAQ,CAAC;AACf,UAAI,IAAI;AACR,eAAS,IAAI,GAAG,IAAI,IAAI,SAAS,GAAG,KAAK,GAAG;AAC1C,cAAM,MAAM,CAAC,KAAK,SAAS,IAAI,CAAC,GAAG,EAAE,KAAM,KAAM,IAAI,IAAK;AAC1D;AAAA,MACF;AAEA,UAAI;AACF,cAAM,cAAc,CAAC;AAErB,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,gBAAM,OAAQ,MAAM,MAAM,CAAC,MAAO,KAAM,IAAI,IAAK,IAAM;AACvD,sBAAY,KAAK,OAAO,aAAa,IAAI,CAAC;AAAA,QAC5C;AAEA,eAAO,mBAAmB,OAAO,YAAY,KAAK,EAAE,CAAC,CAAC;AAAA,MACxD,SAAS,GAAG;AACV,cAAM,IAAI,MAAM,sBAAsB;AAAA,MACxC;AAAA,IACF;AAKA,aAAS,WAAW,QAAQ;AAC1B,YAAM,QAAQ,CAAC;AACf,UAAI,eAAe,OAAO;AAE1B,UAAI,eAAe,MAAM,GAAG;AAC1B,iBAAS,QAAQ,QAAQ,eAAe,CAAC;AAAA,MAC3C;AAEA,qBAAe,OAAO;AAEtB,eAAS,IAAI,GAAG,IAAI,cAAc,KAAK,GAAG;AACxC,cAAM,KAAK,SAAS,OAAO,OAAO,GAAG,CAAC,GAAG,EAAE,CAAC;AAAA,MAC9C;AACA,aAAO;AAAA,IACT;AAKA,aAAS,gBAAgB,WAAW;AAClC,YAAM,QAAQ,MAAM,eAAe,SAAS;AAC5C,UAAI,CAAC,MAAO,QAAO;AAEnB,YAAM,IAAI,MAAM,KAAK;AACrB,YAAM,IAAI,MAAM,KAAK;AAGrB,aAAO,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,IAAI,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,IAAI,MAAM,CAAC,CAAC;AAAA,IACvF;AAKA,aAAS,oBAAoB,YAAY,YAAY;AACnD,YAAM,SAAS,MAAM,eAAe,UAAU;AAC9C,UAAI,CAAC,OAAQ,QAAO;AAEpB,YAAM,SAAS,MAAM,eAAe,UAAU;AAC9C,UAAI,CAAC,OAAQ,QAAO;AAEpB,aAAO,OAAO,OAAO,MAAM;AAAA,IAC7B;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;ACjMA;AAAA;AACA,QAAM,IAAI,IAAI,YAAY,EAAE;AAC5B,QAAM,IAAI,IAAI,YAAY,EAAE;AAK5B,aAAS,KAAK,GAAG,GAAG;AAClB,YAAM,IAAI,IAAI;AACd,aAAQ,KAAK,IAAM,MAAO,KAAK;AAAA,IACjC;AAKA,aAAS,IAAI,GAAG,GAAG;AACjB,YAAM,SAAS,CAAC;AAChB,eAAS,IAAI,EAAE,SAAS,GAAG,KAAK,GAAG,IAAK,QAAO,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK;AACpE,aAAO;AAAA,IACT;AAKA,aAAS,GAAG,GAAG;AACb,aAAQ,IAAI,KAAK,GAAG,CAAC,IAAK,KAAK,GAAG,EAAE;AAAA,IACtC;AAKA,aAAS,GAAG,GAAG;AACb,aAAQ,IAAI,KAAK,GAAG,EAAE,IAAK,KAAK,GAAG,EAAE;AAAA,IACvC;AAKA,aAAS,IAAI,OAAO;AAClB,UAAI,MAAM,MAAM,SAAS;AAGzB,UAAI,IAAI,MAAM;AAEd,UAAI,KAAK,MAAM,MAAO,IAAI,MAAO,IAAI,MAAM,IAAI;AAG/C,YAAM,OAAO,IAAI,OAAO,IAAI,KAAK,CAAC;AAClC,YAAM,SAAS,IAAI,MAAM,CAAC;AAC1B,eAAS,IAAI,GAAGC,OAAM,KAAK,QAAQ,IAAIA,MAAK,IAAK,MAAK,CAAC,IAAI;AAC3D,eAAS,IAAI,GAAGA,OAAM,OAAO,QAAQ,IAAIA,MAAK,IAAK,QAAO,CAAC,IAAI;AAC/D,YAAM,IAAI,SAAS,CAAC;AACpB,eAAS,IAAI,GAAG,KAAK,GAAG,KAAK;AAC3B,YAAI,IAAI,SAAS,GAAG;AAClB,gBAAM,QAAQ,IAAI,SAAS;AAC3B,iBAAO,CAAC,IAAI,SAAS,IAAI,OAAO,KAAK,GAAG,CAAC;AACzC,gBAAM,IAAI,OAAO,GAAG,KAAK;AAAA,QAC3B,WAAW,IAAI,SAAS,GAAG;AACzB,iBAAO,CAAC,IAAI,SAAS,KAAK,CAAC;AAC3B,gBAAM;AAAA,QACR;AAAA,MACF;AACA,YAAM,IAAI,IAAI,WAAW,CAAC,GAAG,OAAO,KAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AAC7D,YAAM,WAAW,IAAI,SAAS,EAAE,QAAQ,CAAC;AAGzC,YAAM,IAAI,EAAE,SAAS;AACrB,YAAM,IAAI,IAAI,YAAY,CAAC,YAAY,YAAY,WAAY,YAAY,YAAY,WAAY,YAAY,UAAU,CAAC;AAC1H,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,UAAE,KAAK,CAAC;AACR,UAAE,KAAK,CAAC;AAGR,cAAM,QAAQ,KAAK;AACnB,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,YAAE,CAAC,IAAI,SAAS,WAAW,QAAQ,KAAK,GAAG,KAAK;AAAA,QAClD;AAGA,iBAAS,IAAI,IAAI,IAAI,IAAI,KAAK;AAC5B,YAAE,CAAC,IAAK,GAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAK,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC,IAAK,EAAE,IAAI,CAAC;AAAA,QACzF;AAGA,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,YAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAAA,QACvB;AAGA,cAAM,KAAK;AACX,cAAM,KAAK;AAEX,YAAI,IAAI,EAAE,CAAC;AACX,YAAI,IAAI,EAAE,CAAC;AACX,YAAI,IAAI,EAAE,CAAC;AACX,YAAI,IAAI,EAAE,CAAC;AACX,YAAI,IAAI,EAAE,CAAC;AACX,YAAI,IAAI,EAAE,CAAC;AACX,YAAI,IAAI,EAAE,CAAC;AACX,YAAI,IAAI,EAAE,CAAC;AAEX,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,cAAI,KAAK,KAAK,KAAK,KAAK,KAAK;AAC7B,gBAAM,KAAK,KAAK,GAAG,EAAE,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC;AAC1C,gBAAM,MAAM,KAAK,GAAG,EAAE;AAEtB,iBAAO,KAAK,KAAK,KAAK,KAAO,IAAI,IAAK,IAAQ,IAAI,IAAM,IAAI,IAAO,IAAI,KAAO,IAAI,MAAM,EAAE,CAAC;AAC3F,iBAAO,KAAK,KAAK,KAAK,KAAO,IAAI,IAAK,IAAO,IAAI,IAAO,CAAC,IAAK,KAAO,IAAI,MAAM,EAAE,CAAC;AAElF,cAAI;AACJ,cAAI,KAAK,GAAG,CAAC;AACb,cAAI;AACJ,cAAI;AACJ,cAAI;AACJ,cAAI,KAAK,GAAG,EAAE;AACd,cAAI;AACJ,cAAI,GAAG,GAAG;AAAA,QACZ;AAEA,UAAE,CAAC,KAAK;AACR,UAAE,CAAC,KAAK;AACR,UAAE,CAAC,KAAK;AACR,UAAE,CAAC,KAAK;AACR,UAAE,CAAC,KAAK;AACR,UAAE,CAAC,KAAK;AACR,UAAE,CAAC,KAAK;AACR,UAAE,CAAC,KAAK;AAAA,MACV;AAGA,YAAM,SAAS,CAAC;AAChB,eAAS,IAAI,GAAGA,OAAM,EAAE,QAAQ,IAAIA,MAAK,KAAK;AAC5C,cAAM,OAAO,EAAE,CAAC;AAChB,eAAO,MAAM,OAAO,gBAAgB,KAAK,OAAO,cAAc,KAAK,OAAO,WAAY,GAAG,OAAO,GAAI;AAAA,MACtG;AAEA,aAAO;AAAA,IACT;AAKA,QAAM,WAAW;AACjB,QAAM,OAAO,IAAI,WAAW,QAAQ;AACpC,QAAM,OAAO,IAAI,WAAW,QAAQ;AACpC,aAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,WAAK,CAAC,IAAI;AACV,WAAK,CAAC,IAAI;AAAA,IACZ;AACA,aAAS,KAAK,OAAO,KAAK;AAExB,UAAI,IAAI,SAAS,SAAU,OAAM,IAAI,GAAG;AACxC,aAAO,IAAI,SAAS,SAAU,KAAI,KAAK,CAAC;AAExC,YAAM,UAAU,IAAI,KAAK,IAAI;AAC7B,YAAM,UAAU,IAAI,KAAK,IAAI;AAE7B,YAAM,OAAO,IAAI,CAAC,GAAG,SAAS,GAAG,KAAK,CAAC;AACvC,aAAO,IAAI,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC;AAAA,IAClC;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;ACzKA;AAAA;AACA,QAAM,EAAC,WAAU,IAAI;AACrB,QAAM,EAAC,WAAW,UAAS,IAAI;AAC/B,QAAM,IAAI;AACV,QAAM,MAAM,cAAiB;AAE7B,QAAM,EAAC,GAAG,OAAO,EAAC,IAAI,EAAE,gBAAgB;AACxC,QAAM,SAAS;AAKf,aAAS,UAAU,KAAK,WAAW,aAAa,GAAG;AACjD,YAAM,OAAO,QAAQ,WAAW,EAAE,WAAW,EAAE,UAAU,GAAG,CAAC,IAAI,MAAM,UAAU,MAAM,KAAK,GAAG;AAC/F,kBAAY,EAAE,eAAe,EAAE,eAAe,SAAS;AAEvD,YAAM,UAAU,EAAE,mBAAmB;AACrC,YAAM,IAAI,IAAI,WAAW,QAAQ,YAAY,EAAE;AAG/C,UAAI,KAAK,QAAQ;AACjB,UAAI,GAAG,SAAS,IAAK,MAAK,GAAG,OAAO,GAAG,SAAS,GAAG;AAGnD,YAAM,IAAI,UAAU,SAAS,CAAC;AAC9B,YAAM,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC;AAC1E,YAAM,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC;AAG1E,YAAM,KAAK,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,IAAI,KAAK,EAAE,CAAC,CAAC;AAEnD,UAAI,KAAK;AACT,UAAI,SAAS;AACb,UAAI,IAAI,CAAC;AACT,YAAM,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE;AAC1B,YAAM,QAAQ,MAAM;AAGlB,YAAI,IAAI,CAAC,GAAG,GAAG,MAAM,KAAK,KAAQ,MAAM,KAAK,KAAQ,MAAM,IAAI,KAAQ,KAAK,GAAM,CAAC;AACnF;AACA,iBAAS;AAAA,MACX;AACA,YAAM;AAEN,eAAS,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK;AAE9C,YAAI,WAAW,EAAE,OAAQ,OAAM;AAG/B,YAAI,CAAC,KAAK,EAAE,QAAQ,IAAI;AAAA,MAC1B;AACA,YAAM,KAAK,EAAE,WAAW,GAAG;AAE3B,aAAO,eAAe,SAAS,KAAK,KAAK,KAAK,KAAK,KAAK;AAAA,IAC1D;AAKA,aAAS,UAAU,aAAa,YAAY,aAAa,GAAG;AAAA,MAC1D,SAAS;AAAA,IACX,IAAI,CAAC,GAAG;AACN,mBAAa,IAAI,WAAW,YAAY,EAAE;AAE1C,UAAI,KAAK,YAAY,OAAO,KAAK,EAAE;AACnC,UAAI,KAAK,YAAY,OAAO,MAAM,EAAE;AAEpC,UAAI,eAAe,QAAQ;AACzB,aAAK,YAAY,OAAO,YAAY,SAAS,EAAE;AAC/C,aAAK,YAAY,OAAO,KAAK,YAAY,SAAS,MAAM,EAAE;AAAA,MAC5D;AAEA,YAAM,MAAM,EAAE,WAAW,EAAE;AAC3B,YAAM,KAAK,EAAE,eAAe,EAAE,eAAe,OAAO,YAAY,OAAO,GAAG,GAAG,CAAC;AAE9E,YAAM,IAAI,GAAG,SAAS,UAAU;AAChC,YAAM,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC;AAC1E,YAAM,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC;AAE1E,UAAI,KAAK;AACT,UAAI,SAAS;AACb,UAAI,IAAI,CAAC;AACT,YAAM,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE;AAC1B,YAAM,QAAQ,MAAM;AAGlB,YAAI,IAAI,CAAC,GAAG,GAAG,MAAM,KAAK,KAAQ,MAAM,KAAK,KAAQ,MAAM,IAAI,KAAQ,KAAK,GAAM,CAAC;AACnF;AACA,iBAAS;AAAA,MACX;AACA,YAAM;AAEN,eAAS,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK;AAE9C,YAAI,WAAW,EAAE,OAAQ,OAAM;AAG/B,YAAI,CAAC,KAAK,EAAE,QAAQ,IAAI;AAAA,MAC1B;AAGA,YAAM,UAAU,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,IAAI,KAAK,EAAE,CAAC,CAAC;AAExD,UAAI,YAAY,GAAG,YAAY,GAAG;AAChC,eAAO,WAAW,UAAU,MAAM,EAAE,YAAY,GAAG;AAAA,MACrD,OAAO;AACL,eAAO,WAAW,UAAU,CAAC,IAAI;AAAA,MACnC;AAAA,IACF;AAKA,aAAS,YAAY,KAAK,YAAY;AAAA,MACpC;AAAA,MAAW;AAAA,MAAK;AAAA,MAAM;AAAA,MAAW;AAAA,IACnC,IAAI,CAAC,GAAG;AACN,UAAI,UAAU,OAAO,QAAQ,WAAW,EAAE,UAAU,GAAG,IAAI,EAAE,WAAW,GAAG;AAE3E,UAAI,MAAM;AAER,oBAAY,aAAa,2BAA2B,UAAU;AAC9D,kBAAU,QAAQ,SAAS,WAAW,MAAM;AAAA,MAC9C;AAEA,YAAM,KAAK,IAAI,WAAW,YAAY,EAAE;AACxC,YAAM,IAAI,IAAI,WAAW,SAAS,EAAE;AAGpC,UAAI,IAAI;AACR,UAAI,IAAI;AACR,UAAI,IAAI;AAER,SAAG;AACD,WAAG;AACD,cAAI;AACJ,cAAI,aAAa,UAAU,QAAQ;AACjC,oBAAQ,UAAU,IAAI;AAAA,UACxB,OAAO;AACL,oBAAQ,SAAS;AAAA,UACnB;AACA,cAAI,MAAM;AAGV,cAAI,EAAE,IAAI,MAAM,EAAE,EAAE,IAAI,CAAC;AAAA,QAC3B,SAAS,EAAE,OAAO,WAAW,IAAI,KAAK,EAAE,IAAI,CAAC,EAAE,OAAO,CAAC;AAGvD,YAAI,GAAG,IAAI,WAAW,GAAG,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;AAAA,MACrF,SAAS,EAAE,OAAO,WAAW,IAAI;AAEjC,UAAI,IAAK,QAAO,UAAU,GAAG,CAAC;AAE9B,aAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,EAAE;AAAA,IACrE;AAKA,aAAS,kBAAkB,KAAK,SAAS,WAAW,EAAC,KAAK,MAAM,OAAM,IAAI,CAAC,GAAG;AAC5E,UAAI,UAAU,OAAO,QAAQ,WAAW,EAAE,UAAU,GAAG,IAAI,EAAE,WAAW,GAAG;AAE3E,UAAI,MAAM;AAER,kBAAU,QAAQ,SAAS,WAAW,MAAM;AAAA,MAC9C;AAEA,UAAI;AAAG,UACL;AACF,UAAI,KAAK;AACP,cAAM,eAAe,UAAU,OAAO;AACtC,YAAI,aAAa;AACjB,YAAI,aAAa;AAAA,MACnB,OAAO;AACL,YAAI,IAAI,WAAW,QAAQ,UAAU,GAAG,EAAE,GAAG,EAAE;AAC/C,YAAI,IAAI,WAAW,QAAQ,UAAU,EAAE,GAAG,EAAE;AAAA,MAC9C;AAEA,YAAM,KAAK,MAAM,eAAe,SAAS;AACzC,YAAM,IAAI,IAAI,WAAW,SAAS,EAAE;AAGpC,YAAM,IAAI,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AAExB,UAAI,EAAE,OAAO,WAAW,IAAI,EAAG,QAAO;AAGtC,YAAM,OAAO,EAAE,SAAS,CAAC,EAAE,IAAI,GAAG,SAAS,CAAC,CAAC;AAG7C,YAAM,IAAI,EAAE,IAAI,KAAK,KAAK,EAAE,aAAa,CAAC,EAAE,IAAI,CAAC;AAEjD,aAAO,EAAE,OAAO,CAAC;AAAA,IACnB;AAKA,aAAS,QAAQ,SAAS,WAAW,SAAS,oBAAoB;AAEhE,eAAS,EAAE,UAAU,MAAM;AAC3B,YAAM,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,EAAE;AAC5D,YAAM,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,EAAE;AAC5D,YAAM,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,EAAE;AAC5D,YAAM,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,EAAE;AAC5D,UAAI;AACJ,UAAI;AACJ,UAAI,UAAU,WAAW,KAAK;AAC5B,aAAK,UAAU,OAAO,GAAG,EAAE;AAC3B,aAAK,UAAU,OAAO,IAAI,EAAE;AAAA,MAC9B,OAAO;AACL,cAAM,QAAQ,EAAE,MAAM,eAAe,SAAS;AAC9C,aAAK,EAAE,QAAQ,MAAM,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,EAAE;AAC1D,aAAK,EAAE,QAAQ,MAAM,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,EAAE;AAAA,MAC5D;AACA,YAAM,OAAO,EAAE,WAAW,SAAS,IAAI,IAAI,KAAK,KAAK,KAAK,EAAE;AAE5D,YAAM,OAAO,OAAO,SAAS;AAC7B,WAAK,QAAQ,OAAO,GAAM;AAC1B,WAAK,QAAQ,QAAQ,IAAI,GAAM;AAE/B,YAAM,IAAI,IAAI,IAAI;AAGlB,aAAO,EAAE,WAAW,IAAI,EAAE,OAAO,EAAE,WAAW,OAAO,CAAC,CAAC,CAAC;AAAA,IAC1D;AAKA,aAAS,2BAA2B,YAAY;AAC9C,YAAM,KAAK,EAAE,SAAS,IAAI,WAAW,YAAY,EAAE,CAAC;AACpD,YAAM,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,EAAE;AAC7D,YAAM,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,EAAE;AAC7D,aAAO,OAAO,IAAI;AAAA,IACpB;AAKA,aAAS,WAAW;AAClB,YAAM,UAAU,EAAE,mBAAmB;AACrC,YAAM,KAAK,MAAM,eAAe,QAAQ,SAAS;AAEjD,cAAQ,IAAI,IAAI,WAAW,QAAQ,YAAY,EAAE;AACjD,cAAQ,KAAK,GAAG,KAAK,EAAE,aAAa;AAEpC,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA,MACf,oBAAoB,EAAE;AAAA,MACtB,sBAAsB,EAAE;AAAA,MACxB,qBAAqB,EAAE;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,iBAAiB,EAAE;AAAA,IACrB;AAAA;AAAA;;;ACpQA,IAAAC,eAAA;AAAA;AAAA,QAAM,EAAC,KAAK,KAAI,IAAI;AAKpB,aAAS,QAAQ,OAAO,KAAK;AAC3B,UAAI,MAAM,UAAU,IAAK,QAAO;AAEhC,aAAQ,IAAI,MAAM,MAAM,MAAM,SAAS,CAAC,EAAG,KAAK,GAAG,IAAI;AAAA,IACzD;AAKA,aAAS,WAAW,KAAK;AACvB,aAAO,IAAI,IAAI,UAAQ;AACrB,eAAO,KAAK,SAAS,EAAE;AACvB,eAAO,KAAK,WAAW,IAAI,MAAM,OAAO;AAAA,MAC1C,CAAC,EAAE,KAAK,EAAE;AAAA,IACZ;AAKA,aAAS,WAAW,QAAQ;AAC1B,YAAM,QAAQ,CAAC;AACf,UAAI,eAAe,OAAO;AAE1B,UAAI,eAAe,MAAM,GAAG;AAC1B,iBAAS,QAAQ,QAAQ,eAAe,CAAC;AAAA,MAC3C;AAEA,qBAAe,OAAO;AAEtB,eAAS,IAAI,GAAG,IAAI,cAAc,KAAK,GAAG;AACxC,cAAM,KAAK,SAAS,OAAO,OAAO,GAAG,CAAC,GAAG,EAAE,CAAC;AAAA,MAC9C;AACA,aAAO;AAAA,IACT;AAKA,aAAS,YAAY,KAAK;AACxB,YAAM,MAAM,CAAC;AAEb,eAAS,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK;AAC9C,cAAM,QAAQ,IAAI,YAAY,CAAC;AAE/B,YAAI,SAAS,KAAQ;AAEnB,cAAI,KAAK,KAAK;AAAA,QAChB,WAAW,SAAS,MAAQ;AAE1B,cAAI,KAAK,MAAQ,UAAU,CAAE;AAC7B,cAAI,KAAK,MAAQ,QAAQ,EAAK;AAAA,QAChC,WAAW,SAAS,SAAW,SAAS,SAAU,SAAS,OAAS;AAElE,cAAI,KAAK,MAAQ,UAAU,EAAG;AAC9B,cAAI,KAAK,MAAS,UAAU,IAAK,EAAK;AACtC,cAAI,KAAK,MAAQ,QAAQ,EAAK;AAAA,QAChC,WAAW,SAAS,SAAY,SAAS,SAAU;AAEjD;AACA,cAAI,KAAM,MAAQ,UAAU,KAAM,EAAK;AACvC,cAAI,KAAM,MAAS,UAAU,KAAM,EAAM;AACzC,cAAI,KAAM,MAAS,UAAU,IAAK,EAAM;AACxC,cAAI,KAAM,MAAQ,QAAQ,EAAM;AAAA,QAClC,OAAO;AAEL,cAAI,KAAK,KAAK;AACd,gBAAM,IAAI,MAAM,wBAAwB;AAAA,QAC1C;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU,SAAU,OAAO,SAAS;AACzC,cAAQ,OAAO,UAAU,WAAW,YAAY,KAAK,IAAI,MAAM,UAAU,MAAM,KAAK,KAAK;AAEzF,UAAI,SAAS;AACX,cAAM,OAAO,QAAQ,QAAQ;AAC7B,YAAI,SAAS,OAAQ,OAAM,IAAI,MAAM,cAAc;AAEnD,YAAI,MAAM,QAAQ;AAClB,YAAI,CAAC,IAAK,OAAM,IAAI,MAAM,aAAa;AAEvC,cAAM,OAAO,QAAQ,WAAW,WAAW,GAAG,IAAI,MAAM,UAAU,MAAM,KAAK,GAAG;AAChF,eAAO,WAAW,KAAK,OAAO,GAAG,CAAC;AAAA,MACpC;AAEA,aAAO,WAAW,IAAI,KAAK,CAAC;AAAA,IAC9B;AAAA;AAAA;;;AC7FA;AAAA;AACA,QAAM,UAAU;AAChB,QAAM,QAAQ;AACd,QAAM,QAAQ;AAEd,QAAM,OAAO;AAAA,MACX;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAC1F;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAC1F;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAC1F;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAC1F;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAC1F;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAC1F;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAC1F;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAC1F;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAC1F;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAC1F;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAC1F;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAC1F;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAC1F;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAC1F;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAC1F;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,IAC5F;AAEA,QAAM,KAAK;AAAA,MACT;AAAA,MAAY;AAAA,MAAY;AAAA,MAAY;AAAA,MACpC;AAAA,MAAY;AAAA,MAAY;AAAA,MAAY;AAAA,MACpC;AAAA,MAAY;AAAA,MAAY;AAAA,MAAY;AAAA,MACpC;AAAA,MAAY;AAAA,MAAY;AAAA,MAAY;AAAA,MACpC;AAAA,MAAY;AAAA,MAAY;AAAA,MAAY;AAAA,MACpC;AAAA,MAAY;AAAA,MAAY;AAAA,MAAY;AAAA,MACpC;AAAA,MAAY;AAAA,MAAY;AAAA,MAAY;AAAA,MACpC;AAAA,MAAY;AAAA,MAAY;AAAA,MAAY;AAAA,IACtC;AAKA,aAAS,WAAW,KAAK;AACvB,YAAM,MAAM,CAAC;AACb,eAAS,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK,GAAG;AACjD,YAAI,KAAK,SAAS,IAAI,OAAO,GAAG,CAAC,GAAG,EAAE,CAAC;AAAA,MACzC;AACA,aAAO;AAAA,IACT;AAKA,aAAS,WAAW,KAAK;AACvB,aAAO,IAAI,IAAI,UAAQ;AACrB,eAAO,KAAK,SAAS,EAAE;AACvB,eAAO,KAAK,WAAW,IAAI,MAAM,OAAO;AAAA,MAC1C,CAAC,EAAE,KAAK,EAAE;AAAA,IACZ;AAKA,aAAS,YAAY,KAAK;AACxB,YAAM,MAAM,CAAC;AAEb,eAAS,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK;AAC9C,cAAM,QAAQ,IAAI,YAAY,CAAC;AAE/B,YAAI,SAAS,KAAQ;AAEnB,cAAI,KAAK,KAAK;AAAA,QAChB,WAAW,SAAS,MAAQ;AAE1B,cAAI,KAAK,MAAQ,UAAU,CAAE;AAC7B,cAAI,KAAK,MAAQ,QAAQ,EAAK;AAAA,QAChC,WAAW,SAAS,SAAW,SAAS,SAAU,SAAS,OAAS;AAElE,cAAI,KAAK,MAAQ,UAAU,EAAG;AAC9B,cAAI,KAAK,MAAS,UAAU,IAAK,EAAK;AACtC,cAAI,KAAK,MAAQ,QAAQ,EAAK;AAAA,QAChC,WAAW,SAAS,SAAY,SAAS,SAAU;AAEjD;AACA,cAAI,KAAM,MAAQ,UAAU,KAAM,EAAK;AACvC,cAAI,KAAM,MAAS,UAAU,KAAM,EAAM;AACzC,cAAI,KAAM,MAAS,UAAU,IAAK,EAAM;AACxC,cAAI,KAAM,MAAQ,QAAQ,EAAM;AAAA,QAClC,OAAO;AAEL,cAAI,KAAK,KAAK;AACd,gBAAM,IAAI,MAAM,wBAAwB;AAAA,QAC1C;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAKA,aAAS,YAAY,KAAK;AACxB,YAAM,MAAM,CAAC;AACb,eAAS,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK;AAC9C,YAAI,IAAI,CAAC,KAAK,OAAQ,IAAI,CAAC,KAAK,KAAM;AAEpC,cAAI,KAAK,OAAO,gBAAgB,IAAI,CAAC,IAAI,MAAS,QAAQ,IAAI,IAAI,CAAC,IAAI,OAAS,QAAQ,IAAI,IAAI,CAAC,IAAI,OAAS,MAAM,IAAI,IAAI,CAAC,IAAI,GAAK,CAAC;AACvI,eAAK;AAAA,QACP,WAAW,IAAI,CAAC,KAAK,OAAQ,IAAI,CAAC,KAAK,KAAM;AAE3C,cAAI,KAAK,OAAO,gBAAgB,IAAI,CAAC,IAAI,OAAS,QAAQ,IAAI,IAAI,CAAC,IAAI,OAAS,MAAM,IAAI,IAAI,CAAC,IAAI,GAAK,CAAC;AACzG,eAAK;AAAA,QACP,WAAW,IAAI,CAAC,KAAK,OAAQ,IAAI,CAAC,KAAK,KAAM;AAE3C,cAAI,KAAK,OAAO,gBAAgB,IAAI,CAAC,IAAI,OAAS,MAAM,IAAI,IAAI,CAAC,IAAI,GAAK,CAAC;AAC3E;AAAA,QACF,OAAO;AAEL,cAAI,KAAK,OAAO,cAAc,IAAI,CAAC,CAAC,CAAC;AAAA,QACvC;AAAA,MACF;AAEA,aAAO,IAAI,KAAK,EAAE;AAAA,IACpB;AAKA,aAAS,KAAK,GAAG,GAAG;AAClB,YAAM,IAAI,IAAI;AACd,aAAQ,KAAK,IAAM,MAAO,KAAK;AAAA,IACjC;AAKA,aAAS,QAAQ,GAAG;AAClB,cAAQ,KAAK,MAAM,KAAK,GAAI,IAAI,QAAS,MACtC,KAAK,MAAM,KAAK,GAAI,IAAI,QAAS,MACjC,KAAK,MAAM,IAAI,GAAI,IAAI,QAAS,IAChC,KAAK,IAAI,GAAI,IAAI;AAAA,IACtB;AAKA,aAAS,GAAG,GAAG;AACb,aAAO,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE,IAAI,KAAK,GAAG,EAAE,IAAI,KAAK,GAAG,EAAE;AAAA,IAChE;AAKA,aAAS,GAAG,GAAG;AACb,aAAO,IAAI,KAAK,GAAG,EAAE,IAAI,KAAK,GAAG,EAAE;AAAA,IACrC;AAKA,aAAS,UAAU,OAAO,QAAQ,UAAU;AAC1C,YAAM,IAAI,IAAI,MAAM,CAAC;AAGrB,YAAM,MAAM,IAAI,MAAM,CAAC;AACvB,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAI,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI;AACxB,YAAI,CAAC,IAAI,MAAM,IAAI,IAAI,CAAC,IAAI;AAC5B,YAAI,CAAC,IAAI,MAAM,IAAI,IAAI,CAAC,IAAI;AAC5B,YAAI,CAAC,IAAI,MAAM,IAAI,IAAI,CAAC,IAAI;AAC5B,UAAE,CAAC,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;AAAA,MAC1D;AAGA,eAAS,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK,GAAG;AACnC,cAAM,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,SAAS,IAAI,CAAC;AACzC,UAAE,CAAC,KAAK,GAAG,QAAQ,GAAG,CAAC;AAEvB,cAAM,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,SAAS,IAAI,CAAC;AACzC,UAAE,CAAC,KAAK,GAAG,QAAQ,GAAG,CAAC;AAEvB,cAAM,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,SAAS,IAAI,CAAC;AACzC,UAAE,CAAC,KAAK,GAAG,QAAQ,GAAG,CAAC;AAEvB,cAAM,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,SAAS,IAAI,CAAC;AACzC,UAAE,CAAC,KAAK,GAAG,QAAQ,GAAG,CAAC;AAAA,MACzB;AAGA,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAC9B,eAAO,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK;AAClC,eAAO,IAAI,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK;AACtC,eAAO,IAAI,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,MAAM,IAAI;AACrC,eAAO,IAAI,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI;AAAA,MACjC;AAAA,IACF;AAKA,aAAS,WAAW,KAAK,UAAU,WAAW;AAC5C,YAAM,IAAI,IAAI,MAAM,CAAC;AAGrB,YAAM,MAAM,IAAI,MAAM,CAAC;AACvB,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAI,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI;AAC1B,YAAI,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI;AAC1B,YAAI,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI;AAC1B,YAAI,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI;AAC1B,UAAE,CAAC,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;AAAA,MAC1D;AAGA,QAAE,CAAC,KAAK;AACR,QAAE,CAAC,KAAK;AACR,QAAE,CAAC,KAAK;AACR,QAAE,CAAC,KAAK;AAGR,eAAS,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK,GAAG;AACnC,cAAM,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC;AACnC,iBAAS,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,QAAQ,GAAG,CAAC;AAEzC,cAAM,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC;AACnC,iBAAS,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,QAAQ,GAAG,CAAC;AAEzC,cAAM,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC;AACnC,iBAAS,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,QAAQ,GAAG,CAAC;AAEzC,cAAM,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC;AACnC,iBAAS,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,QAAQ,GAAG,CAAC;AAAA,MAC3C;AAGA,UAAI,cAAc,SAAS;AACzB,iBAAS,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK;AAChC,gBAAM,SAAS,CAAC;AAChB,mBAAS,CAAC,IAAI,SAAS,KAAK,CAAC;AAC7B,mBAAS,KAAK,CAAC,IAAI;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAEA,aAAS,IAAI,SAAS,KAAK,WAAW;AAAA,MACpC,UAAU;AAAA,MAAU;AAAA,MAAM,KAAK,CAAC;AAAA,MAAG,SAAS;AAAA,IAC9C,IAAI,CAAC,GAAG;AACN,UAAI,SAAS,OAAO;AAElB,YAAI,OAAO,OAAO,SAAU,MAAK,WAAW,EAAE;AAC9C,YAAI,GAAG,WAAY,MAAM,GAAI;AAE3B,gBAAM,IAAI,MAAM,eAAe;AAAA,QACjC;AAAA,MACF;AAGA,UAAI,OAAO,QAAQ,SAAU,OAAM,WAAW,GAAG;AACjD,UAAI,IAAI,WAAY,MAAM,GAAI;AAE5B,cAAM,IAAI,MAAM,gBAAgB;AAAA,MAClC;AAGA,UAAI,OAAO,YAAY,UAAU;AAC/B,YAAI,cAAc,SAAS;AAEzB,oBAAU,YAAY,OAAO;AAAA,QAC/B,OAAO;AAEL,oBAAU,WAAW,OAAO;AAAA,QAC9B;AAAA,MACF,OAAO;AACL,kBAAU,CAAC,GAAG,OAAO;AAAA,MACvB;AAGA,WAAK,YAAY,YAAY,YAAY,aAAa,cAAc,SAAS;AAC3E,cAAM,eAAe,QAAQ,QAAQ,SAAS;AAC9C,iBAAS,IAAI,GAAG,IAAI,cAAc,IAAK,SAAQ,KAAK,YAAY;AAAA,MAClE;AAGA,YAAM,WAAW,IAAI,MAAM,KAAK;AAChC,iBAAW,KAAK,UAAU,SAAS;AAEnC,YAAM,WAAW,CAAC;AAClB,UAAI,aAAa;AACjB,UAAI,UAAU,QAAQ;AACtB,UAAI,QAAQ;AACZ,aAAO,WAAW,OAAO;AACvB,cAAM,QAAQ,QAAQ,MAAM,OAAO,QAAQ,EAAE;AAC7C,cAAMC,UAAS,IAAI,MAAM,EAAE;AAE3B,YAAI,SAAS,OAAO;AAClB,mBAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,gBAAI,cAAc,SAAS;AAEzB,oBAAM,CAAC,KAAK,WAAW,CAAC;AAAA,YAC1B;AAAA,UACF;AAAA,QACF;AAEA,kBAAU,OAAOA,SAAQ,QAAQ;AAGjC,iBAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,cAAI,SAAS,OAAO;AAClB,gBAAI,cAAc,SAAS;AAEzB,cAAAA,QAAO,CAAC,KAAK,WAAW,CAAC;AAAA,YAC3B;AAAA,UACF;AAEA,mBAAS,QAAQ,CAAC,IAAIA,QAAO,CAAC;AAAA,QAChC;AAEA,YAAI,SAAS,OAAO;AAClB,cAAI,cAAc,SAAS;AAEzB,yBAAaA;AAAA,UACf,OAAO;AAEL,yBAAa;AAAA,UACf;AAAA,QACF;AAEA,mBAAW;AACX,iBAAS;AAAA,MACX;AAGA,WAAK,YAAY,YAAY,YAAY,aAAa,cAAc,SAAS;AAC3E,cAAM,MAAM,SAAS;AACrB,cAAM,eAAe,SAAS,MAAM,CAAC;AACrC,iBAAS,IAAI,GAAG,KAAK,cAAc,KAAK;AACtC,cAAI,SAAS,MAAM,CAAC,MAAM,aAAc,OAAM,IAAI,MAAM,oBAAoB;AAAA,QAC9E;AACA,iBAAS,OAAO,MAAM,cAAc,YAAY;AAAA,MAClD;AAGA,UAAI,WAAW,SAAS;AACtB,YAAI,cAAc,SAAS;AAEzB,iBAAO,WAAW,QAAQ;AAAA,QAC5B,OAAO;AAEL,iBAAO,YAAY,QAAQ;AAAA,QAC7B;AAAA,MACF,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO,UAAU;AAAA,MACf,QAAQ,SAAS,KAAK,SAAS;AAC7B,eAAO,IAAI,SAAS,KAAK,GAAG,OAAO;AAAA,MACrC;AAAA,MACA,QAAQ,SAAS,KAAK,SAAS;AAC7B,eAAO,IAAI,SAAS,KAAK,GAAG,OAAO;AAAA,MACrC;AAAA,IACF;AAAA;AAAA;;;ACtWA;AAAA;AAAA,WAAO,UAAU;AAAA,MACf,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA;AAAA;", "names": ["t", "z", "curve", "G", "n", "len", "require_sm3", "output"]}