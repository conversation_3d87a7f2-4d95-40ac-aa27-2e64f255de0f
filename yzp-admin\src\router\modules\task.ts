export default {
  path: "/task",
  redirect: "/task/403",
  meta: {
    icon: "ri-clipboard-fill",
    showLink: false,
    title: "任务管理",
    rank: 11
  },
  children: [
    {
      path: "/task/management",
      meta: {
        title: "任务"
      },
      children: [
        {
          path: "/task/task_assignment/awaiting_review",
          name: "task_assignment_awaiting_review",
          component: () =>
            import("@/views/task_management/task_assignment/index.vue"),
          meta: {
            title: "任务分配",
            businessStatus: 10
          }
        },
        {
          path: "/task_assignment/awaiting_review",
          name: "TaskAssignmentDetail",
          component: () =>
            import("@/views/task_management/task_details/index.vue"),
          meta: {
            title: "分配审批"
          }
        }
      ]
    }
  ]
} satisfies RouteConfigsTable;
