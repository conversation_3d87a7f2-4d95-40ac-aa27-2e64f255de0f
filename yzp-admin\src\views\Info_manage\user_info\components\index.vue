<script lang="ts" setup>
import { ref, computed } from "vue";
import { getInfoById } from "@/api/info_query/index";
import { formatTimestamp } from "@/utils/dateFormat";
import { ElMessage } from "element-plus";

const loading = ref<boolean>(false);
const rightType = ref<any>("note");
const infoTableData = ref<any>({});

const $emit = defineEmits(["cancelBtn", "update:dialogVisible"]);

const $props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  currentRow: {
    type: Object,
    default: () => ({})
  }
});

const visible = computed({
  get() {
    if ($props.dialogVisible) {
      getInfoByIdData();
    }
    return $props.dialogVisible;
  },
  set(value) {
    $emit("update:dialogVisible", value);
  }
});

// 获取详情
const getInfoByIdData = async () => {
  const res: any = await getInfoById({ id: $props.currentRow.id, type: 1 });
  if (res.code === 0) {
    infoTableData.value = res.data;
  } else {
    ElMessage.error(res.message);
  }
};

// 取消
function cancelBtn() {
  $emit("cancelBtn", true);
}

// 取消
async function handleClose() {
  cancelBtn();
}
</script>

<template>
  <div>
    <el-dialog
      v-model="visible"
      v-loading="loading"
      close-on-click-modal
      destroy-on-close
      :title="null"
      width="1100px"
      :show-close="true"
      class="custom-detail-dialog"
      align-center
      @close="cancelBtn"
    >
      <!-- 详情内容区 -->
      <el-descriptions title="用户信息">
        <el-descriptions-item label="姓名："
          >{{ infoTableData?.trueName || "暂无" }}
        </el-descriptions-item>

        <el-descriptions-item v-if="infoTableData?.gender" label="性别：">{{
          infoTableData?.gender === 1 ? "男" : "女"
        }}</el-descriptions-item>

        <el-descriptions-item label="手机号码：">{{
          infoTableData?.phone || "暂无"
        }}</el-descriptions-item>

        <el-descriptions-item label="出生年月日：">{{
          formatTimestamp(infoTableData?.birthday, "YYYY-MM-DD")
        }}</el-descriptions-item>

        <el-descriptions-item label="认证状态：">
          <el-tag
            v-if="infoTableData?.isAuth"
            size="small"
            :type="infoTableData?.isAuth === 0 ? 'danger' : 'success'"
            >{{
              infoTableData?.isAuth === 0
                ? "未认证"
                : infoTableData?.isAuth === 1
                  ? "已认证"
                  : ""
            }}</el-tag
          >
        </el-descriptions-item>
        <el-descriptions-item label="提交时间：">{{
          formatTimestamp(infoTableData?.createTime)
        }}</el-descriptions-item>
      </el-descriptions>
      <!-- 按钮组 -->
      <div v-if="rightType === 'note'" class="btn-group-bottom">
        <el-button @click="handleClose()">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.custom-detail-dialog .el-dialog__body {
  background: #f5f6fa;
  padding-bottom: 0;
  min-height: 520px;
}

.btn-group-bottom {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin: 20px 40px 0 0;
}
:deep(.el-table--fit) {
  margin-top: 10px;
  border-radius: 3px;
}
</style>
