import{d as x,r as V,O as v,c as b,b as u,f,e as l,g as _,w as c,j as L,t as y,_ as k,l as h,k as z,i as C,U as N,F as Y,h as A}from"./index-DOMkE6w1.js";const T={class:"transfer-select-box"},D={class:"scroll-container"},M={class:"transfer-btn-bar"},U=x({__name:"index",props:{transferList:{},modelValue:{}},emits:["update:modelValue","submit"],setup(p,{emit:s}){const t=p,o=s,a=V(t.modelValue);v(()=>t.modelValue,d=>a.value=d),v(()=>a.value,d=>o("update:modelValue",d));function e(){o("submit",a.value)}return(d,r)=>{const n=_("el-radio"),i=_("el-table-column"),g=_("el-table"),S=_("el-button");return u(),b("div",T,[r[2]||(r[2]=f("div",{class:"record-title"},"转审人员",-1)),f("div",D,[l(g,{data:d.transferList,border:"",class:"transfer-table","show-header":!0,"header-cell-style":{background:"#f7f8fa",color:"#888",fontWeight:500,fontSize:"15px",textAlign:"left"},"cell-style":{background:"#f7f8fa",color:"#222",fontSize:"15px",textAlign:"left"}},{default:c(()=>[l(i,{prop:"name",label:"名字","min-width":"180"},{default:c(w=>[l(n,{modelValue:a.value,"onUpdate:modelValue":r[0]||(r[0]=m=>a.value=m),label:w.row.value,style:{"margin-left":"8px"}},{default:c(()=>[L(y(w.row.name),1)]),_:2},1032,["modelValue","label"])]),_:1})]),_:1},8,["data"]),f("div",M,[l(S,{type:"primary",style:{width:"100px"},onClick:e},{default:c(()=>r[1]||(r[1]=[L("转审")])),_:1})])])])}}}),G=k(U,[["__scopeId","data-v-f6efb835"]]),$=(p,s="YYYY-MM-DD HH:mm:ss")=>{if(!p)return"";const t=typeof p=="string"?parseInt(p):p;if(isNaN(t)||t<=0)return"";const o=t.toString().length===10?t*1e3:t,a=new Date(o);if(isNaN(a.getTime()))return"";const e=a.getFullYear(),d=String(a.getMonth()+1).padStart(2,"0"),r=String(a.getDate()).padStart(2,"0"),n=String(a.getHours()).padStart(2,"0"),i=String(a.getMinutes()).padStart(2,"0"),g=String(a.getSeconds()).padStart(2,"0");return s.replace(/YYYY/g,e.toString()).replace(/MM/g,d).replace(/DD/g,r).replace(/HH/g,n).replace(/mm/g,i).replace(/ss/g,g)},F={class:"audit-status-records-box"},H={class:"scroll-container"},R={key:0},B={key:0},I={key:1},W={key:2},E=x({__name:"index",props:{auditList:{},statusList:{}},setup(p){return(s,t)=>{const o=_("el-table-column"),a=_("el-table");return u(),b("div",F,[f("div",H,[s.auditList.length>0?(u(),b("div",R,[t[0]||(t[0]=f("div",{class:"record-title"},"审核记录",-1)),l(a,{data:s.auditList,border:"",class:"audit-table","show-header":!0,"header-cell-style":{background:"#f7f8fa",color:"#888",fontWeight:500,fontSize:"15px",textAlign:"center"},"cell-style":{background:"#f7f8fa",color:"#222",fontSize:"15px",textAlign:"center"},style:{width:"100%","margin-bottom":"24px"}},{default:c(()=>[l(o,{prop:"auditUserName",label:"人员","min-width":"80"}),l(o,{prop:"auditTime",label:"审核时间","min-width":"120","show-overflow-tooltip":""},{default:c(e=>[f("span",null,y(C($)(e.row.auditTime)),1)]),_:1}),s.auditList.some(e=>e.status==="2")?(u(),z(o,{key:0,prop:"reason",label:"驳回原因","min-width":"120","show-overflow-tooltip":""},{default:c(e=>[e.row.status==="2"?(u(),b("span",B,y(e.row.reason),1)):h("",!0)]),_:1})):h("",!0),l(o,{prop:"status",label:"结果","min-width":"80"},{default:c(e=>[f("span",{style:N({color:e.row.status==="1"?"#1FC600":e.row.status==="2"?"#F56C6C":"#222"})},y(e.row.status==="1"?"通过":e.row.status==="2"?"已驳回":"待审核"),5)]),_:1})]),_:1},8,["data"])])):h("",!0),s.statusList.length>0?(u(),b("div",I,[t[1]||(t[1]=f("div",{class:"record-title"},"状态记录",-1)),l(a,{data:s.statusList,border:"",class:"status-table","show-header":!0,"header-cell-style":{background:"#f7f8fa",color:"#888",fontWeight:500,fontSize:"15px",textAlign:"center"},"cell-style":{background:"#f7f8fa",color:"#222",fontSize:"15px",textAlign:"center"},style:{width:"100%"}},{default:c(()=>[l(o,{prop:"person",label:"人员","min-width":"80"}),l(o,{prop:"time",label:"转审时间","min-width":"160"}),l(o,{prop:"person2",label:"人员","min-width":"80"})]),_:1},8,["data"])])):h("",!0),s.statusList.length===0&&s.auditList.length===0?(u(),b("div",W,t[2]||(t[2]=[f("div",{class:"no-data"},"暂无数据",-1)]))):h("",!0)])])}}}),J=k(E,[["__scopeId","data-v-7b350a81"]]),j={class:"reason-edit-card"},O=x({__name:"index",props:{options:{},modelValue:{}},emits:["update:modelValue","change"],setup(p,{emit:s}){var d,r;const t=p,o=s,a=V(((d=t.options[0])==null?void 0:d.value)||""),e=V(((r=t.options[0])==null?void 0:r.content)||"");return v(a,n=>{const i=t.options.find(g=>g.value===n);e.value=i?i.content:"",o("change",n),o("update:modelValue",e.value)}),v(()=>t.modelValue,n=>{n!==void 0&&(e.value=n)}),v(e,n=>{o("update:modelValue",n)}),(n,i)=>{const g=_("el-option"),S=_("el-select"),w=_("el-input");return u(),b("div",j,[l(S,{modelValue:a.value,"onUpdate:modelValue":i[0]||(i[0]=m=>a.value=m),class:"reason-select",style:{width:"220px"}},{default:c(()=>[(u(!0),b(Y,null,A(n.options,m=>(u(),z(g,{key:m.value,label:m.label,value:m.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),l(w,{modelValue:e.value,"onUpdate:modelValue":i[1]||(i[1]=m=>e.value=m),placeholder:"请填写批注内容",type:"textarea",rows:7,class:"reason-textarea",resize:"none",autosize:{minRows:7,maxRows:12}},null,8,["modelValue"])])}}}),K=k(O,[["__scopeId","data-v-6d9ae132"]]);export{J as A,K as R,G as T,$ as f};
