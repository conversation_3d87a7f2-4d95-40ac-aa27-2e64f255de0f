<script setup lang="ts">
import Motion from "./utils/motion";
import { useRouter } from "vue-router";
import { message } from "@/utils/message";
import { loginRules } from "./utils/rule";
import { ref, reactive, toRaw, onMounted } from "vue";
import { debounce } from "@pureadmin/utils";
import { useNav } from "@/layout/hooks/useNav";
import { useEventListener } from "@vueuse/core";
import type { FormInstance } from "element-plus";
import { useLayout } from "@/layout/hooks/useLayout";
import { useUserStoreHook } from "@/store/modules/user";
import { initRouter, getTopMenu } from "@/router/utils";
import { bg, avatar, illustration } from "./utils/static";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange";
import md5 from "blueimp-md5";

import dayIcon from "@/assets/svg/day.svg?component";
import darkIcon from "@/assets/svg/dark.svg?component";
import Lock from "~icons/ri/lock-fill";
import User from "~icons/ri/user-3-fill";
import { getGenerateCode } from "@/api/login/index";

defineOptions({
  name: "Login"
});

const router = useRouter();
const loading = ref(false);
const disabled = ref(false);
const ruleFormRef = ref<FormInstance>();
const codeUrl = ref("");
let oldUrl = "";

const { initStorage } = useLayout();
initStorage();

const { dataTheme, overallStyle, dataThemeChange } = useDataThemeChange();
dataThemeChange(overallStyle.value);
const { title } = useNav();

const ruleForm = reactive({
  username: "admin",
  password: "123456",
  verificationCode: ""
});

const onLogin = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(valid => {
    if (valid) {
      loading.value = true;
      useUserStoreHook()
        .loginByUsername({
          account: ruleForm.username,
          password: md5(ruleForm.password),
          verificationCode: ruleForm.verificationCode
        })
        .then((res: any) => {
          if (res.code === 0) {
            // 获取后端路由
            return initRouter().then(() => {
              disabled.value = true;
              router
                .push(getTopMenu(true).path)
                .then(() => {
                  message("登录成功", { type: "success" });
                })
                .finally(() => (disabled.value = false));
            });
          } else {
            message(res.msg, { type: "error" });
            getCodeDetails();
          }
        })
        .finally(() => (loading.value = false));
    }
  });
};

const immediateDebounce: any = debounce(
  formRef => onLogin(formRef),
  1000,
  true
);

useEventListener(document, "keydown", ({ code }) => {
  if (
    ["Enter", "NumpadEnter"].includes(code) &&
    !disabled.value &&
    !loading.value
  )
    immediateDebounce(ruleFormRef.value);
});

// 获取图形验证码
const getCodeDetails = async () => {
  const res: any = await getGenerateCode();

  // 把 arraybuffer 转为 blob
  const blob = new Blob([res], { type: "image/png" }); // 根据你的接口返回类型设定 MIME 类型

  // 释放旧的 URL
  if (oldUrl) URL.revokeObjectURL(oldUrl);

  // 创建新的 URL 并赋值
  codeUrl.value = URL.createObjectURL(blob);
  oldUrl = codeUrl.value;
};

onMounted(() => {
  getCodeDetails();
});
</script>

<template>
  <div class="select-none">
    <img :src="bg" class="wave" />
    <div class="flex-c absolute right-5 top-3">
      <!-- 主题 -->
      <el-switch
        v-model="dataTheme"
        inline-prompt
        :active-icon="dayIcon"
        :inactive-icon="darkIcon"
        @change="dataThemeChange"
      />
    </div>
    <div class="login-container">
      <div class="img">
        <component :is="toRaw(illustration)" />
      </div>
      <div class="login-box">
        <div class="login-form">
          <div class="login-form-header">
            <!-- <avatar class="avatar" /> -->
            <div class="avatar">
              <img src="@/assets/login/logo.png" class="logo" alt="logo" />
            </div>
            <Motion>
              <h2 class="outline-hidden">{{ title }}</h2>
              <h5 class="outline-hidden-sub">登录您的易直聘后台管理系统</h5>
            </Motion>
          </div>

          <el-form
            ref="ruleFormRef"
            :model="ruleForm"
            :rules="loginRules"
            size="large"
          >
            <Motion :delay="100">
              <el-form-item
                :rules="[
                  {
                    required: true,
                    message: '请输入账号',
                    trigger: 'blur'
                  }
                ]"
                prop="username"
              >
                <el-input
                  v-model="ruleForm.username"
                  clearable
                  placeholder="账号"
                  :prefix-icon="useRenderIcon(User)"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="150">
              <el-form-item prop="password">
                <el-input
                  v-model="ruleForm.password"
                  clearable
                  show-password
                  placeholder="密码"
                  :prefix-icon="useRenderIcon(Lock)"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="150">
              <el-form-item prop="verificationCode">
                <div class="verification-code-container">
                  <el-input
                    v-model="ruleForm.verificationCode"
                    clearable
                    placeholder="验证码"
                  />
                  <el-image
                    :src="codeUrl"
                    class="verification-code-image"
                    @click="getCodeDetails"
                  />
                </div>
              </el-form-item>
            </Motion>

            <Motion :delay="250">
              <el-button
                class="w-full mt-4!"
                size="default"
                type="primary"
                :loading="loading"
                :disabled="disabled"
                @click="onLogin(ruleFormRef)"
              >
                登录
              </el-button>
            </Motion>
          </el-form>
        </div>
      </div>
    </div>
    <footer class="login-footer">
      <div class="footer-content">
        <div class="footer-item">
          地址：重庆市九龙坡区渝州路街道科园一路　 邮箱：<EMAIL>　
          客服电话：400-965-9675
        </div>
        <div class="footer-item">
          <!-- <span
            style="vertical-align: middle; font-size: 18px; margin-right: 4px"
          ></span> -->
          渝公网安备50010702506556号 渝ICP备2025058312号-1
        </div>
      </div>
    </footer>
  </div>
</template>

<style scoped>
@import url("@/style/login.css");
.login-footer {
  width: 100vw;
  color: #888888;
  text-align: center;
  padding: 12px 0 8px 0;
  font-size: 15px;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 10;
  line-height: 1.7;
}
.footer-content {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 48px;
}
.footer-item {
  display: flex;
  align-items: center;
}
</style>

<style lang="scss" scoped>
.verification-code-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;

  .verification-code-image {
    cursor: pointer;
    height: 36px;
    min-width: 24%;
  }
}

.login-form-header {
  .logo {
    width: 84px;
    height: 84px;
  }
  .outline-hidden {
    color: #338fff !important;
  }

  .outline-hidden-sub {
    color: #a0aec0 !important;
  }
  margin-top: -70px;
  padding-bottom: 36px;
}

:deep(.el-input-group__append, .el-input-group__prepend) {
  padding: 0;
}
</style>
