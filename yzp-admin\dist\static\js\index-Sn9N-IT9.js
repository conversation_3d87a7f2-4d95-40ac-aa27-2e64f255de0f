var ie=Object.defineProperty;var L=Object.getOwnPropertySymbols;var ue=Object.prototype.hasOwnProperty,de=Object.prototype.propertyIsEnumerable;var A=(d,t,a)=>t in d?ie(d,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):d[t]=a,Y=(d,t)=>{for(var a in t||(t={}))ue.call(t,a)&&A(d,a,t[a]);if(L)for(var a of L(t))de.call(t,a)&&A(d,a,t[a]);return d};var j=(d,t,a)=>new Promise((x,h)=>{var z=r=>{try{v(a.next(r))}catch(k){h(k)}},c=r=>{try{v(a.throw(r))}catch(k){h(k)}},v=r=>r.done?x(r.value):Promise.resolve(r.value).then(z,c);v((a=a.apply(d,t)).next())});import pe from"./index-CPPPBRES.js";import{d as me,r as n,o as ce,u as ve,a as ye,c as _,b as i,e as p,w as u,f as S,g as m,F as E,h as M,i as P,j as C,k as g,l as T,t as U,_ as be}from"./index-DOMkE6w1.js";import{a as fe}from"./index-C5VTKhL7.js";import{f as _e}from"./index-DFv13GLf.js";import"./index-BPHCkZA8.js";import"./quickReply-D6WzbhB8.js";const ge={class:"table-header-flex"},he={class:"form-btns"},ke={key:0},we={key:1},Ce={key:2},xe={style:{display:"flex","justify-content":"flex-end","margin-top":"20px"}},$=420,ze=me({__name:"index",setup(d){const t=n({}),a=n(!1),x=n(!1),h=n(0),z=n([]),c=n(0),v=n({}),r=n(""),k=ve(),s=n({entity:{endTime:"",name:"",phone:"",startTime:"",status:null},orderBy:{},page:1,size:10});let F=[{type:"input",key:"username",label:"姓名"},{type:"input",key:"phone",label:"手机号"},{type:"datetime",key:"dates",label:"提交时间"}];const I=[{property:"name",label:"姓名",width:""},{property:"age",label:"年龄",width:""},{property:"sex",label:"性别",width:""},{property:"phone",label:"手机号码",width:""},{property:"createTime",label:"提交时间",width:""}],V=n(1),R=n(10),Q=n("default"),q=n(!1),G=n(!1),J=l=>{R.value=l,s.value.size=l,s.value.page=1,b()},K=l=>{V.value=l,s.value.page=l,b()},B=n(window.innerHeight-$);function H(){B.value=window.innerHeight-$}const b=()=>j(null,null,function*(){x.value=!0;try{const l=yield fe(Y({},s.value));l.code===0&&(h.value=l.data.total,z.value=l.data.list)}catch(l){}finally{x.value=!1}}),O=()=>{t.value={},s.value.entity={endTime:"",name:"",phone:"",startTime:"",status:c.value},b()},W=()=>{t.value.dates&&t.value.dates.length===2?(s.value.entity.startTime=t.value.dates[0],s.value.entity.endTime=t.value.dates[1]):(delete s.value.entity.startTime,delete s.value.entity.endTime),s.value.entity.name=t.value.name||void 0,s.value.entity.phone=t.value.phone||void 0,b()},X=l=>{v.value=l.row,a.value=!0,r.value="note"},Z=l=>{v.value=l.row,a.value=!0,r.value="transfer"},ee=l=>{v.value=l.row,a.value=!0,r.value="record"},te=()=>{r.value="",a.value=!1,b()};return ce(()=>{const l=k.meta.businessStatus;s.value.entity.status=l!==void 0?Number(l):0,c.value=l!==void 0?Number(l):0,b(),window.addEventListener("resize",H)}),ye(()=>{window.removeEventListener("resize",H)}),(l,o)=>{const ae=m("el-input"),le=m("el-date-picker"),oe=m("el-form-item"),ne=m("el-form"),w=m("el-button"),D=m("el-card"),N=m("el-table-column"),re=m("el-table"),se=m("el-pagination");return i(),_("div",null,[p(D,{shadow:"never"},{default:u(()=>[S("div",ge,[p(ne,{inline:!0,model:t.value,class:"table-header-form"},{default:u(()=>[(i(!0),_(E,null,M(P(F),(e,f)=>(i(),g(oe,{key:f,label:e.label,class:"form-item"},{default:u(()=>[e.type==="input"?(i(),g(ae,{key:0,modelValue:t.value[e.key],"onUpdate:modelValue":y=>t.value[e.key]=y,size:"large",placeholder:"请输入"+e.label,clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="datetime"?(i(),g(le,{key:1,modelValue:t.value[e.key],"onUpdate:modelValue":y=>t.value[e.key]=y,type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",size:"large",style:{width:"380px"},"value-format":"YYYY-MM-DD HH:mm:ss",clearable:""},null,8,["modelValue","onUpdate:modelValue"])):T("",!0)]),_:2},1032,["label"]))),128))]),_:1},8,["model"]),S("div",he,[p(w,{size:"large",type:"primary",onClick:W},{default:u(()=>o[3]||(o[3]=[C("搜索")])),_:1}),p(w,{size:"large",type:"info",style:{"background-color":"#b4c4d1",color:"#ffffff",border:"#b4c4d1"},onClick:O},{default:u(()=>o[4]||(o[4]=[C("重置")])),_:1})])])]),_:1}),p(D,{shadow:"never",style:{"margin-top":"15px",padding:"5px 15px"}},{default:u(()=>[p(re,{ref:"tableContainer",data:z.value,style:{width:"100%"},border:"",height:B.value},{default:u(()=>[(i(),_(E,null,M(I,(e,f)=>p(N,{key:f,width:e.width,label:e.label,property:e.property,"show-overflow-tooltip":""},{default:u(y=>[e.property==="createTime"?(i(),_("span",ke,U(P(_e)(y.row[e.property])),1)):e.property==="sex"?(i(),_("span",we,U(y.row[e.property]===1?"男":"女"),1)):(i(),_("span",Ce,U(y.row[e.property]),1))]),_:2},1032,["width","label","property"])),64)),p(N,{fixed:"right",label:"操作","min-width":"60"},{default:u(e=>[c.value===0?(i(),g(w,{key:0,link:"",type:"primary",style:{color:"#279efb"},onClick:f=>X(e)},{default:u(()=>o[5]||(o[5]=[C(" 视检 ")])),_:2},1032,["onClick"])):T("",!0),c.value===0?(i(),g(w,{key:1,link:"",type:"primary",style:{color:"#fb2727"},onClick:f=>Z(e)},{default:u(()=>o[6]||(o[6]=[C(" 转办 ")])),_:2},1032,["onClick"])):T("",!0),c.value===1||c.value===2?(i(),g(w,{key:2,link:"",type:"primary",style:{color:"#4eb906"},onClick:f=>ee(e)},{default:u(()=>o[7]||(o[7]=[C(" 操作记录 ")])),_:2},1032,["onClick"])):T("",!0)]),_:1})]),_:1},8,["data","height"]),S("div",xe,[p(se,{"current-page":V.value,"onUpdate:currentPage":o[0]||(o[0]=e=>V.value=e),"page-size":R.value,"onUpdate:pageSize":o[1]||(o[1]=e=>R.value=e),"page-sizes":[10,20,50,100],size:Q.value,disabled:G.value,background:q.value,layout:"total, sizes, prev, pager, next, jumper",total:h.value,onSizeChange:J,onCurrentChange:K},null,8,["current-page","page-size","size","disabled","background","total"])])]),_:1}),p(pe,{dialogVisible:a.value,"onUpdate:dialogVisible":o[2]||(o[2]=e=>a.value=e),isRightType:r.value,currentRow:v.value,onCancelBtn:te},null,8,["dialogVisible","isRightType","currentRow"])])}}}),De=be(ze,[["__scopeId","data-v-aeb20c61"]]);export{De as default};
