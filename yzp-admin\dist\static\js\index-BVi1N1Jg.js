var R=(L,U,g)=>new Promise((c,r)=>{var x=i=>{try{m(g.next(i))}catch(p){r(p)}},n=i=>{try{m(g.throw(i))}catch(p){r(p)}},m=i=>i.done?c(i.value):Promise.resolve(i.value).then(x,n);m((g=g.apply(L,U)).next())});import{f as oe,T as ne,A as le,R as ie}from"./index-DFv13GLf.js";import{n as re}from"./quickReply-D6WzbhB8.js";import{e as de,f as ue}from"./index-Dgu8EOgW.js";import{d as ce,r as d,x as me,O as q,aQ as D,c as C,b as l,S as pe,ab as ve,k as f,w as u,e as s,f as o,l as G,g as y,j as b,t as _,i as H,F as J,h as K,_ as fe}from"./index-DOMkE6w1.js";const _e={class:"content-row"},ge={class:"company-info-card"},ye={class:"company-info-header"},be={class:"company-info-title"},he={class:"company-name"},Ie={class:"company-tags"},ke={class:"company-desc-scroll"},Te={class:"company-benefits"},Ce={class:"company-env-imgs"},Le={class:"card-box"},Ue={key:0,class:"btn-group-bottom"},xe=ce({__name:"index",props:{dialogVisible:{type:Boolean,default:!1},isRightType:{type:String,default:""},currentRow:{type:Object,default:()=>({})}},emits:["cancelBtn","update:dialogVisible","update:updataList"],setup(L,{emit:U}){const g=d(!1),c=d("note"),r=d({}),x=d(""),n=d({}),m=d([]),i=d([]),p=U,I=L,N=me({get(){return I.dialogVisible&&W(),I.dialogVisible},set(e){p("update:dialogVisible",e)}});q(()=>I.isRightType,e=>{c.value=e},{immediate:!0});const W=()=>R(null,null,function*(){const e=yield de({id:r.value.id});e.code===0?n.value=e.data:D.error(e.message)});function S(){p("cancelBtn",!0),c.value="note"}const A=e=>R(null,null,function*(){(yield ue(e)).code===0&&(D.success("操作成功"),S())});function X(e){const t={id:r.value.id,manualInspectionStatus:1};A(t),p("update:updataList",!0)}function Y(e){if(!h.value||h.value.trim()===""){D.warning("请填写驳回原因");return}const t={id:r.value.id,manualInspectionStatus:2,reason:h.value};A(t)}function Z(){c.value="transfer"}function V(e){S()}const ee=[{name:"A组张三",value:"zhangsan"},{name:"A组李四",value:"lisi"}],j=d("lisi"),h=d("");return q(()=>I.currentRow,e=>{m.value=[],i.value=[],m.value.push({auditUserName:(e==null?void 0:e.manualInspectionUserName)||"",auditTime:(e==null?void 0:e.manualInspectionTime)||"",status:(e==null?void 0:e.manualInspectionStatus)===1?"1":(e==null?void 0:e.manualInspectionStatus)===2?"2":"0",reason:(e==null?void 0:e.reason)||""}),r.value=e,x.value=e.headImgUrl},{immediate:!0}),(e,t)=>{const v=y("el-table-column"),E=y("el-table"),O=y("el-image"),te=y("el-tag"),B=y("el-button"),ae=y("el-dialog"),se=ve("loading");return l(),C("div",null,[pe((l(),f(ae,{modelValue:N.value,"onUpdate:modelValue":t[4]||(t[4]=k=>N.value=k),"close-on-click-modal":"","destroy-on-close":"",title:null,width:"1100px","show-close":!0,class:"custom-detail-dialog","align-center":"",onClose:S},{default:u(()=>{var k,P,$,F,w,z,M,Q;return[s(E,{data:[r.value],border:"",style:{width:"100%","margin-bottom":"12px"},class:"info-table-normal"},{default:u(()=>[s(v,{prop:"name",label:"企业名称","min-width":"180","show-overflow-tooltip":""}),s(v,{prop:"socialCreditCode",label:"社会信用代码","min-width":"180"}),s(v,{prop:"enterpriseLegalPerson",label:"法人","min-width":"120"})]),_:1},8,["data"]),s(E,{data:[r.value],border:"",style:{width:"100%"},class:"info-table-normal"},{default:u(()=>[s(v,{prop:"createUserId",label:"提交人ID","min-width":"100"},{default:u(a=>[b(_(`ID：${a.row.createUserId}`),1)]),_:1}),s(v,{prop:"name",label:"提交人姓名","min-width":"120"}),s(v,{prop:"phone",label:"提交人电话","min-width":"140"}),s(v,{prop:"createTime",label:"提交时间","min-width":"180"},{default:u(a=>[b(_(H(oe)(a.row.createTime)),1)]),_:1})]),_:1},8,["data"]),o("div",_e,[o("div",ge,[o("div",ye,[(k=n.value)!=null&&k.companyLogoUrl?(l(),f(O,{key:0,src:(P=n.value)==null?void 0:P.companyLogoUrl,class:"company-logo",fit:"contain"},null,8,["src"])):G("",!0),o("div",be,[o("div",he,_((($=n.value)==null?void 0:$.name)||""),1),o("div",Ie,[o("div",null,_(((F=n.value)==null?void 0:F.financeStep)||""),1),o("div",null,_(((w=n.value)==null?void 0:w.industryName)||""),1)])])]),o("div",ke,_(((z=n.value)==null?void 0:z.profile)||""),1),o("div",Te,[(l(!0),C(J,null,K(((M=n.value)==null?void 0:M.fuLi)||[],(a,T)=>(l(),f(te,{key:T,class:"benefit-tag",type:"info",effect:"plain"},{default:u(()=>[b(_(a),1)]),_:2},1024))),128))]),o("div",Ce,[(l(!0),C(J,null,K(((Q=n.value)==null?void 0:Q.tuPian)||[],(a,T)=>(l(),f(O,{key:T,src:a.attachIdUrl||"",class:"env-img",fit:"cover","preview-src-list":a.attachIdUrl||"","initial-index":T},null,8,["src","preview-src-list","initial-index"]))),128))])]),o("div",Le,[c.value==="transfer"?(l(),f(ne,{key:0,modelValue:j.value,"onUpdate:modelValue":t[0]||(t[0]=a=>j.value=a),transferList:ee,onSubmit:V},null,8,["modelValue"])):c.value==="record"?(l(),f(le,{key:1,auditList:m.value,statusList:i.value},null,8,["auditList","statusList"])):(l(),f(ie,{key:2,modelValue:h.value,"onUpdate:modelValue":t[1]||(t[1]=a=>h.value=a),options:H(re)},null,8,["modelValue","options"]))])]),c.value==="note"?(l(),C("div",Ue,[s(B,{type:"primary",style:{background:"#3477f4",border:"none"},onClick:Z},{default:u(()=>t[5]||(t[5]=[b("转审")])),_:1}),s(B,{type:"danger",onClick:t[2]||(t[2]=()=>Y("reject"))},{default:u(()=>t[6]||(t[6]=[b("驳回")])),_:1}),s(B,{type:"success",onClick:t[3]||(t[3]=()=>X("pass"))},{default:u(()=>t[7]||(t[7]=[b("通过")])),_:1})])):G("",!0)]}),_:1},8,["modelValue"])),[[se,g.value]])])}}}),Ae=fe(xe,[["__scopeId","data-v-afde3440"]]);export{Ae as default};
