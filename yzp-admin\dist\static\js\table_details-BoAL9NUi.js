var x=(h,T,m)=>new Promise((d,u)=>{var p=s=>{try{v(m.next(s))}catch(c){u(c)}},b=s=>{try{v(m.throw(s))}catch(c){u(c)}},v=s=>s.done?d(s.value):Promise.resolve(s.value).then(p,b);v((m=m.apply(h,T)).next())});import{T as W,A as Q,R as q,n as F}from"./quickReply-DfweD696.js";import{i as G}from"./index-D6X2KkBM.js";import{f as H}from"./dateFormat-BuOeynu9.js";import{d as J,r as i,m as K,P as L,c as C,b as n,T as X,ab as Y,k as _,w as r,e as o,f as l,l as Z,g as y,t as B,i as N,j as w,aQ as A,_ as V}from"./index-VeYmKv4z.js";const ee={class:"content-row"},te={class:"card-box attachment-box"},ae={class:"avatar-preview-wrapper"},se={key:1,class:"no-avatar"},oe={class:"card-box"},le={key:0,class:"btn-group-bottom"},ie=J({__name:"table_details",props:{dialogVisible:{type:Boolean,default:!1},isRightType:{type:String,default:""},currentRow:{type:Object,default:()=>({})},closeOnClickModal:{type:Boolean,default:!1}},emits:["cancelBtn","update:dialogVisible"],setup(h,{emit:T}){const m=i(!1),d=i("note"),u=i({}),p=i(""),b=i([]),v=i([]),s=T,c=h,R=K({get(){return c.dialogVisible},set(e){s("update:dialogVisible",e)}});L(()=>c.isRightType,e=>{d.value=e},{immediate:!0});function k(){s("cancelBtn",!0),d.value="note"}const S=e=>x(null,null,function*(){const t=yield G(e);t.code===0?A.success("操作成功"):A.error(t.message)});function I(e){return x(this,null,function*(){const t={id:u.value.id,status:1};yield S(t),k()})}function $(e){return x(this,null,function*(){if(!g.value||g.value.trim()===""){A.warning("请填写驳回原因");return}const t={id:u.value.id,status:2,reason:g.value};yield S(t),k()})}function j(e){k()}const O=[{name:"A组张三",value:"zhangsan"},{name:"A组李四",value:"lisi"}],U=i("lisi"),g=i("");return L(()=>c.currentRow,e=>{b.value=[],v.value=[],b.value.push({auditUserName:(e==null?void 0:e.auditUserName)||"",auditTime:(e==null?void 0:e.auditTime)||"",status:(e==null?void 0:e.status)===1?"1":(e==null?void 0:e.status)===2?"2":"0",reason:(e==null?void 0:e.reason)||""}),u.value=e,p.value=e.headImgUrl},{immediate:!0}),(e,t)=>{const f=y("el-table-column"),z=y("el-table"),E=y("el-image"),D=y("el-button"),M=y("el-dialog"),P=Y("loading");return n(),C("div",null,[X((n(),_(M,{modelValue:R.value,"onUpdate:modelValue":t[4]||(t[4]=a=>R.value=a),"close-on-click-modal":h.closeOnClickModal,"destroy-on-close":"",title:null,width:"1100px","show-close":!0,class:"custom-detail-dialog","align-center":"",onClose:k},{default:r(()=>[o(z,{data:[u.value],border:"",class:"info-table info-table-top",style:{width:"100%","margin-bottom":"32px"},"show-header":!0,"header-cell-style":{background:"#fff",color:"#888",fontWeight:500,fontSize:"16px",textAlign:"center"},"cell-style":{background:"#fff",color:"#222",fontSize:"18px",fontWeight:500,textAlign:"center"}},{default:r(()=>[o(f,{prop:"id",label:"ID","show-overflow-tooltip":"","min-width":"120"},{default:r(a=>[l("span",null,B(`ID：${a.row.id}`),1)]),_:1}),o(f,{prop:"name",label:"姓名","min-width":"100"}),o(f,{prop:"age",label:"年龄","min-width":"80"}),o(f,{prop:"sex",label:"性别","min-width":"80"},{default:r(a=>[l("span",null,B(a.row.sex===1?"男":"女"),1)]),_:1}),o(f,{prop:"phone",label:"电话","min-width":"140"}),o(f,{prop:"createTime",label:"提交时间","min-width":"180"},{default:r(a=>[l("span",null,B(N(H)(a.row.createTime)),1)]),_:1})]),_:1},8,["data"]),l("div",ee,[l("div",te,[t[6]||(t[6]=l("div",{class:"card-title"},"头像",-1)),l("div",ae,[p.value?(n(),_(E,{key:0,src:p.value,class:"avatar-preview","preview-src-list":[p.value],fit:"cover"},{error:r(()=>t[5]||(t[5]=[l("div",{class:"no-avatar"},"加载失败",-1)])),_:1},8,["src","preview-src-list"])):(n(),C("div",se,"暂无头像"))])]),l("div",oe,[d.value==="transfer"?(n(),_(W,{key:0,modelValue:U.value,"onUpdate:modelValue":t[0]||(t[0]=a=>U.value=a),transferList:O,onSubmit:j},null,8,["modelValue"])):d.value==="record"?(n(),_(Q,{key:1,auditList:b.value,statusList:v.value},null,8,["auditList","statusList"])):(n(),_(q,{key:2,modelValue:g.value,"onUpdate:modelValue":t[1]||(t[1]=a=>g.value=a),options:N(F)},null,8,["modelValue","options"]))])]),d.value==="note"?(n(),C("div",le,[o(D,{type:"danger",onClick:t[2]||(t[2]=a=>$("reject"))},{default:r(()=>t[7]||(t[7]=[w("驳回")])),_:1}),o(D,{type:"success",onClick:t[3]||(t[3]=a=>I("pass"))},{default:r(()=>t[8]||(t[8]=[w("通过")])),_:1})])):Z("",!0)]),_:1},8,["modelValue","close-on-click-modal"])),[[P,m.value]])])}}}),me=V(ie,[["__scopeId","data-v-720ac549"]]);export{me as default};
