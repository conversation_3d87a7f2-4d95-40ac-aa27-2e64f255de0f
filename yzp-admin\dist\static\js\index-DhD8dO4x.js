var re=Object.defineProperty;var N=Object.getOwnPropertySymbols;var ie=Object.prototype.hasOwnProperty,ue=Object.prototype.propertyIsEnumerable;var L=(d,t,a)=>t in d?re(d,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):d[t]=a,P=(d,t)=>{for(var a in t||(t={}))ie.call(t,a)&&L(d,a,t[a]);if(N)for(var a of N(t))ue.call(t,a)&&L(d,a,t[a]);return d};var Y=(d,t,a)=>new Promise((b,h)=>{var m=r=>{try{v(a.next(r))}catch(o){h(o)}},z=r=>{try{v(a.throw(r))}catch(o){h(o)}},v=r=>r.done?b(r.value):Promise.resolve(r.value).then(m,z);v((a=a.apply(d,t)).next())});import de from"./index-BE4KlvTT.js";import{b as pe}from"./index-D6X2KkBM.js";import{d as ce,r as n,o as me,u as ve,a as ye,c as _,b as i,e as p,w as u,f as V,g as c,F as j,h as E,i as O,j as C,k,l as R,t as S,_ as be}from"./index-VeYmKv4z.js";import{f as fe}from"./dateFormat-BuOeynu9.js";import"./quickReply-DfweD696.js";import"./index-C3VS7c2V.js";const _e={class:"table-header-flex"},he={class:"form-btns"},ge={key:0},ke={key:1},we={key:2},Ce={style:{display:"flex","justify-content":"flex-end","margin-top":"20px"}},F=420,ze=ce({__name:"index",setup(d){const t=n({}),a=n(!1),b=n(""),h=n(0),m=n(0),z=ve(),v=n({}),r=n(!1),o=n({entity:{endTime:"",name:"",phone:"",startTime:"",status:null},orderBy:{},page:1,size:10});let $=[{type:"input",key:"name",label:"姓名"},{type:"input",key:"phone",label:"手机号"},{type:"datetime",key:"dates",label:"提交时间"}];const A=[{property:"name",label:"姓名",width:""},{property:"age",label:"年龄",width:""},{property:"sex",label:"性别",width:""},{property:"phone",label:"手机号码",width:""},{property:"createTime",label:"提交时间",width:""}],U=n([]),x=n(1),T=n(10),I=n("default"),Q=n(!1),q=n(!1),G=l=>{T.value=l,o.value.size=l,o.value.page=1,f()},J=l=>{x.value=l,o.value.page=l,f()},B=n(window.innerHeight-F);function D(){B.value=window.innerHeight-F}const f=()=>Y(null,null,function*(){const l=yield pe(P({},o.value));l.code===0&&(U.value=l.data.list,h.value=l.data.total)}),K=()=>{t.value.dates&&t.value.dates.length===2?(o.value.entity.startTime=t.value.dates[0],o.value.entity.endTime=t.value.dates[1]):(delete o.value.entity.startTime,delete o.value.entity.endTime),o.value.entity.name=t.value.name||void 0,o.value.entity.phone=t.value.phone||void 0,f()},W=()=>{t.value={},o.value.entity={endTime:"",name:"",phone:"",startTime:"",status:m.value},f()},X=l=>{v.value=l.row,a.value=!0,r.value=!1,b.value="note"},Z=l=>{v.value=l.row,a.value=!0,r.value=!0,b.value="record"},ee=()=>{b.value="",a.value=!1,r.value=!1,f()};return me(()=>{const l=z.meta.businessStatus;o.value.entity.status=l!==void 0?Number(l):0,m.value=l!==void 0?Number(l):0,f(),window.addEventListener("resize",D)}),ye(()=>{window.removeEventListener("resize",D)}),(l,s)=>{const te=c("el-input"),ae=c("el-date-picker"),le=c("el-form-item"),oe=c("el-form"),w=c("el-button"),H=c("el-card"),M=c("el-table-column"),ne=c("el-table"),se=c("el-pagination");return i(),_("div",null,[p(H,{shadow:"never"},{default:u(()=>[V("div",_e,[p(oe,{inline:!0,model:t.value,class:"table-header-form"},{default:u(()=>[(i(!0),_(j,null,E(O($),(e,g)=>(i(),k(le,{key:g,label:e.label,class:"form-item"},{default:u(()=>[e.type==="input"?(i(),k(te,{key:0,modelValue:t.value[e.key],"onUpdate:modelValue":y=>t.value[e.key]=y,size:"large",placeholder:"请输入"+e.label,clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="datetime"?(i(),k(ae,{key:1,modelValue:t.value[e.key],"onUpdate:modelValue":y=>t.value[e.key]=y,type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",size:"large",style:{width:"380px"},"value-format":"YYYY-MM-DD HH:mm:ss",clearable:""},null,8,["modelValue","onUpdate:modelValue"])):R("",!0)]),_:2},1032,["label"]))),128))]),_:1},8,["model"]),V("div",he,[p(w,{size:"large",type:"primary",onClick:K},{default:u(()=>s[3]||(s[3]=[C("搜索")])),_:1}),p(w,{size:"large",type:"info",style:{"background-color":"#b4c4d1",color:"#ffffff",border:"#b4c4d1"},onClick:W},{default:u(()=>s[4]||(s[4]=[C("重置")])),_:1})])])]),_:1}),p(H,{shadow:"never",style:{"margin-top":"15px",padding:"5px 15px"}},{default:u(()=>[p(ne,{ref:"tableContainer",data:U.value,style:{width:"100%"},border:"",height:B.value},{default:u(()=>[(i(),_(j,null,E(A,(e,g)=>p(M,{key:g,width:e.width,label:e.label,property:e.property,"show-overflow-tooltip":""},{default:u(y=>[e.property==="createTime"?(i(),_("span",ge,S(O(fe)(y.row[e.property])),1)):e.property==="sex"?(i(),_("span",ke,S(y.row[e.property]===1?"男":"女"),1)):(i(),_("span",we,S(y.row[e.property]),1))]),_:2},1032,["width","label","property"])),64)),p(M,{fixed:"right",label:"操作","min-width":"60"},{default:u(e=>[m.value===1?(i(),k(w,{key:0,link:"",type:"primary",style:{color:"#279efb"},onClick:g=>X(e)},{default:u(()=>s[5]||(s[5]=[C(" 视检 ")])),_:2},1032,["onClick"])):R("",!0),m.value===2||m.value===3?(i(),k(w,{key:1,link:"",type:"primary",style:{color:"#4eb906"},onClick:g=>Z(e)},{default:u(()=>s[6]||(s[6]=[C(" 操作记录 ")])),_:2},1032,["onClick"])):R("",!0)]),_:1})]),_:1},8,["data","height"]),V("div",Ce,[p(se,{"current-page":x.value,"onUpdate:currentPage":s[0]||(s[0]=e=>x.value=e),"page-size":T.value,"onUpdate:pageSize":s[1]||(s[1]=e=>T.value=e),"page-sizes":[10,20,50,100],size:I.value,disabled:q.value,background:Q.value,layout:"total, sizes, prev, pager, next, jumper",total:h.value,onSizeChange:G,onCurrentChange:J},null,8,["current-page","page-size","size","disabled","background","total"])])]),_:1}),p(de,{dialogVisible:a.value,"onUpdate:dialogVisible":s[2]||(s[2]=e=>a.value=e),isRightType:b.value,currentRow:v.value,closeOnClickModal:r.value,onCancelBtn:ee},null,8,["dialogVisible","isRightType","currentRow","closeOnClickModal"])])}}}),De=be(ze,[["__scopeId","data-v-d66dcf34"]]);export{De as default};
