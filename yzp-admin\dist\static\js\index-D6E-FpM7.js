var B=(I,U,g)=>new Promise((u,r)=>{var x=i=>{try{m(g.next(i))}catch(p){r(p)}},a=i=>{try{m(g.throw(i))}catch(p){r(p)}},m=i=>i.done?u(i.value):Promise.resolve(i.value).then(x,a);m((g=g.apply(I,U)).next())});import{T as ne,A as ie,R as re,n as ce}from"./quickReply-DfweD696.js";import{f as de}from"./dateFormat-BuOeynu9.js";import{e as ue,f as me}from"./index-BeicPvf_.js";import{d as pe,r as c,m as ve,P as G,aQ as R,c as L,b as l,T as fe,ab as _e,k as f,w as d,e as n,f as s,l as H,g as y,j as h,t as _,i as J,F as K,h as W,_ as ge}from"./index-VeYmKv4z.js";const ye={class:"content-row"},be={class:"company-info-card"},he={class:"company-info-header"},Ie={class:"company-info-title"},ke={class:"company-name"},Te={class:"company-tags"},Ce={style:{color:"#888888","font-size":"14px"}},Le={style:{color:"#888888","font-size":"14px"}},Ue={class:"company-desc-scroll"},xe={class:"company-benefits"},Se={class:"company-env-imgs"},Be={class:"card-box"},Re={key:0,class:"btn-group-bottom"},De=pe({__name:"index",props:{dialogVisible:{type:Boolean,default:!1},isRightType:{type:String,default:""},currentRow:{type:Object,default:()=>({})},closeOnClickModal:{type:Boolean,default:!1}},emits:["cancelBtn","update:dialogVisible","update:updataList"],setup(I,{emit:U}){const g=c(!1),u=c("note"),r=c({}),x=c(""),a=c({}),m=c([]),i=c([]),p=U,k=I,D=ve({get(){return k.dialogVisible&&X(),k.dialogVisible},set(e){p("update:dialogVisible",e)}});G(()=>k.isRightType,e=>{u.value=e},{immediate:!0});const X=()=>B(null,null,function*(){const e=yield ue({id:r.value.id});e.code===0?a.value=e.data:R.error(e.message)}),Y=e=>{switch(e){case 1:return"0-20人";case 2:return"20-99人";case 3:return"100-499人";case 4:return"500-999人";case 5:return"1000-9999人";case 6:return"10000人以上";default:return""}};function S(){p("cancelBtn",!0),u.value="note"}const N=e=>B(null,null,function*(){(yield me(e)).code===0&&(R.success("操作成功"),S())});function Z(e){const t={id:r.value.id,manualInspectionStatus:1};N(t),p("update:updataList",!0)}function V(e){if(!b.value||b.value.trim()===""){R.warning("请填写驳回原因");return}const t={id:r.value.id,manualInspectionStatus:2,reason:b.value};N(t)}function ee(e){S()}const te=[{name:"A组张三",value:"zhangsan"},{name:"A组李四",value:"lisi"}],A=c("lisi"),b=c("");return G(()=>k.currentRow,e=>{m.value=[],i.value=[],m.value.push({auditUserName:(e==null?void 0:e.manualInspectionUserName)||"",auditTime:(e==null?void 0:e.manualInspectionTime)||"",status:(e==null?void 0:e.manualInspectionStatus)===1?"1":(e==null?void 0:e.manualInspectionStatus)===2?"2":"0",reason:(e==null?void 0:e.reason)||""}),r.value=e,x.value=e.headImgUrl},{immediate:!0}),(e,t)=>{const v=y("el-table-column"),P=y("el-table"),j=y("el-image"),ae=y("el-tag"),O=y("el-button"),se=y("el-dialog"),oe=_e("loading");return l(),L("div",null,[fe((l(),f(se,{modelValue:D.value,"onUpdate:modelValue":t[4]||(t[4]=T=>D.value=T),"close-on-click-modal":I.closeOnClickModal,"destroy-on-close":"",title:null,width:"1100px","show-close":!0,class:"custom-detail-dialog","align-center":"",onClose:S},{default:d(()=>{var T,z,E,F,M,$,w,Q;return[n(P,{data:[r.value],border:"",style:{width:"100%","margin-bottom":"12px"},class:"info-table-normal"},{default:d(()=>[n(v,{prop:"name",label:"企业名称","min-width":"180","show-overflow-tooltip":""}),n(v,{prop:"socialCreditCode",label:"社会信用代码","min-width":"180"}),n(v,{prop:"enterpriseLegalPerson",label:"法人","min-width":"120"})]),_:1},8,["data"]),n(P,{data:[r.value],border:"",style:{width:"100%"},class:"info-table-normal"},{default:d(()=>[n(v,{prop:"createUserId",label:"提交人ID","min-width":"100"},{default:d(o=>[h(_(`ID：${o.row.createUserId}`),1)]),_:1}),n(v,{prop:"name",label:"提交人姓名","min-width":"120"}),n(v,{prop:"phone",label:"提交人电话","min-width":"140"}),n(v,{prop:"createTime",label:"提交时间","min-width":"180"},{default:d(o=>[h(_(J(de)(o.row.createTime)),1)]),_:1})]),_:1},8,["data"]),s("div",ye,[s("div",be,[s("div",he,[(T=a.value)!=null&&T.companyLogoUrl?(l(),f(j,{key:0,src:(z=a.value)==null?void 0:z.companyLogoUrl,class:"company-logo",fit:"contain"},null,8,["src"])):H("",!0),s("div",Ie,[s("div",ke,_(((E=a.value)==null?void 0:E.name)||""),1),s("div",Te,[s("div",Ce,_(Y(((F=a.value)==null?void 0:F.financeStep)||"")),1),s("div",Le,_(((M=a.value)==null?void 0:M.industryName)||""),1)])])]),s("div",Ue,_((($=a.value)==null?void 0:$.profile)||""),1),s("div",xe,[(l(!0),L(K,null,W(((w=a.value)==null?void 0:w.fuLi)||[],(o,C)=>(l(),f(ae,{key:C,class:"benefit-tag",type:"info",effect:"plain"},{default:d(()=>[h(_(o),1)]),_:2},1024))),128))]),s("div",Se,[(l(!0),L(K,null,W(((Q=a.value)==null?void 0:Q.tuPian)||[],(o,C)=>{var q;return l(),f(j,{key:C,src:o.attachIdUrl||"",class:"env-img",fit:"cover","preview-src-list":(((q=a.value)==null?void 0:q.tuPian)||[]).map(le=>le.attachIdUrl).filter(Boolean),"initial-index":C},null,8,["src","preview-src-list","initial-index"])}),128))])]),s("div",Be,[u.value==="transfer"?(l(),f(ne,{key:0,modelValue:A.value,"onUpdate:modelValue":t[0]||(t[0]=o=>A.value=o),transferList:te,onSubmit:ee},null,8,["modelValue"])):u.value==="record"?(l(),f(ie,{key:1,auditList:m.value,statusList:i.value},null,8,["auditList","statusList"])):(l(),f(re,{key:2,modelValue:b.value,"onUpdate:modelValue":t[1]||(t[1]=o=>b.value=o),options:J(ce)},null,8,["modelValue","options"]))])]),u.value==="note"?(l(),L("div",Re,[n(O,{type:"danger",onClick:t[2]||(t[2]=()=>V("reject"))},{default:d(()=>t[5]||(t[5]=[h("驳回")])),_:1}),n(O,{type:"success",onClick:t[3]||(t[3]=()=>Z("pass"))},{default:d(()=>t[6]||(t[6]=[h("通过")])),_:1})])):H("",!0)]}),_:1},8,["modelValue","close-on-click-modal"])),[[oe,g.value]])])}}}),ze=ge(De,[["__scopeId","data-v-58fde09a"]]);export{ze as default};
