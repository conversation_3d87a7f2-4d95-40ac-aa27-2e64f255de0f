var H=(D,t,y)=>new Promise((h,v)=>{var w=s=>{try{f(y.next(s))}catch(c){v(c)}},k=s=>{try{f(y.throw(s))}catch(c){v(c)}},f=s=>s.done?h(s.value):Promise.resolve(s.value).then(w,k);f((y=y.apply(D,t)).next())});import le from"./index-Cu7Ncqqy.js";import{g as oe}from"./index-CpjYoR0F.js";import{f as ne}from"./dateFormat-BuOeynu9.js";import{d as re,r as l,o as ie,a as se,c as m,b as i,e as d,w as u,f as T,g as p,F as z,h as x,i as S,j as R,k as _,l as de,t as C,V as ue,_ as pe}from"./index-VeYmKv4z.js";const ye={class:"table-header-flex"},ce={class:"form-btns"},me={key:0},ve={key:2},fe={style:{display:"flex","justify-content":"flex-end","margin-top":"20px"}},L=420,be=re({__name:"index",setup(D){const t=l({}),y=l(!1),h=l(""),v=l(!1);let w=[{type:"input",key:"userName",label:"姓名"},{type:"input",key:"identifyNum",label:"身份证号码"},{type:"select",key:"authResult",label:"状态"},{type:"datetime",key:"dates",label:"提交时间"}];const k=[{property:"userName",label:"姓名",width:""},{property:"identifyNum",label:"身份证号码",width:""},{property:"authResult",label:"状态",width:""},{property:"createTime",label:"提交时间",width:""}],f=l([]),s=l(1),c=l(10),Y=l("default"),j=l(!1),E=l(!1),N=l(0),M=l({}),I=l([{label:"核验一致",value:1},{label:"核验不一致",value:2}]),n=l({entity:{id2MetaVerifyRecordQueryDTO:{endTime:"",startTime:"",authResult:"",identifyNum:"",userName:""},type:5},orderBy:{},page:1,size:10}),F=o=>{c.value=o,n.value.size=o,n.value.page=1,b()},P=o=>{s.value=o,n.value.page=o,b()},Q=l(window.innerHeight-L);function U(){Q.value=window.innerHeight-L}function A(o){M.value=o.row,y.value=!0}const $=()=>{h.value="",y.value=!1,b()},q=()=>{t.value.dates&&t.value.dates.length===2?(n.value.entity.id2MetaVerifyRecordQueryDTO.startTime=t.value.dates[0],n.value.entity.id2MetaVerifyRecordQueryDTO.endTime=t.value.dates[1]):(delete n.value.entity.id2MetaVerifyRecordQueryDTO.startTime,delete n.value.entity.id2MetaVerifyRecordQueryDTO.endTime),n.value.entity.id2MetaVerifyRecordQueryDTO.authResult=t.value.authResult,n.value.entity.id2MetaVerifyRecordQueryDTO.identifyNum=t.value.identifyNum,n.value.entity.id2MetaVerifyRecordQueryDTO.userName=t.value.userName,b()},G=()=>{t.value={},n.value={entity:{id2MetaVerifyRecordQueryDTO:{endTime:"",startTime:"",authResult:"",identifyNum:"",userName:""},type:5},orderBy:{},page:1,size:10},b()},b=()=>H(null,null,function*(){v.value=!0;try{const o=yield oe(n.value);o.code===0&&(N.value=o.data.total,f.value=o.data.list)}catch(o){}finally{v.value=!1}});return ie(()=>{b(),window.addEventListener("resize",U)}),se(()=>{window.removeEventListener("resize",U)}),(o,r)=>{const J=p("el-input"),K=p("el-option"),W=p("el-select"),X=p("el-date-picker"),Z=p("el-form-item"),ee=p("el-form"),V=p("el-button"),O=p("el-card"),B=p("el-table-column"),te=p("el-table"),ae=p("el-pagination");return i(),m("div",null,[d(O,{shadow:"never"},{default:u(()=>[T("div",ye,[d(ee,{inline:!0,model:t.value,class:"table-header-form"},{default:u(()=>[(i(!0),m(z,null,x(S(w),(e,g)=>(i(),_(Z,{key:g,label:e.label,class:"form-item"},{default:u(()=>[e.type==="input"?(i(),_(J,{key:0,modelValue:t.value[e.key],"onUpdate:modelValue":a=>t.value[e.key]=a,size:"large",placeholder:"请输入"+e.label,style:{width:"180px"},clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="select"?(i(),_(W,{key:1,modelValue:t.value[e.key],"onUpdate:modelValue":a=>t.value[e.key]=a,size:"large",placeholder:"请选择"+e.label,style:{width:"180px"},clearable:""},{default:u(()=>[(i(!0),m(z,null,x(I.value,a=>(i(),_(K,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="datetime"?(i(),_(X,{key:2,modelValue:t.value[e.key],"onUpdate:modelValue":a=>t.value[e.key]=a,type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",size:"large",style:{width:"380px"},"value-format":"YYYY-MM-DD HH:mm:ss",clearable:""},null,8,["modelValue","onUpdate:modelValue"])):de("",!0)]),_:2},1032,["label"]))),128))]),_:1},8,["model"]),T("div",ce,[d(V,{size:"large",type:"primary",onClick:q},{default:u(()=>r[3]||(r[3]=[R("搜索")])),_:1}),d(V,{size:"large",type:"info",style:{"background-color":"#b4c4d1",color:"#ffffff",border:"#b4c4d1"},onClick:G},{default:u(()=>r[4]||(r[4]=[R("重置")])),_:1})])])]),_:1}),d(O,{shadow:"never",style:{"margin-top":"15px",padding:"5px 15px"}},{default:u(()=>[d(te,{ref:"tableContainer",data:f.value,loading:v.value,"element-loading-text":"数据加载中...","element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(255, 255, 255, 0.9)","element-loading-svg-view-box":"-10, -10, 50, 50",style:{width:"100%"},height:Q.value},{default:u(()=>[(i(),m(z,null,x(k,(e,g)=>d(B,{key:g,width:e.width,label:e.label,"show-overflow-tooltip":""},{default:u(a=>[e.property==="createTime"?(i(),m("span",me,C(S(ne)(a.row[e.property])),1)):e.property==="authResult"?(i(),m("span",{key:1,style:ue({color:a.row[e.property]===1?"#67C23A":a.row[e.property]===2?"#fb5451":""})},C(a.row[e.property]===1?"核验一致":a.row[e.property]===2?"核验不一致":""),5)):(i(),m("span",ve,C(a.row[e.property]),1))]),_:2},1032,["width","label"])),64)),d(B,{fixed:"right",label:"操作","min-width":"120"},{default:u(e=>[d(V,{link:"",type:"primary",style:{color:"#279efb"},onClick:g=>A(e)},{default:u(()=>r[5]||(r[5]=[R(" 详情 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","loading","height"]),T("div",fe,[d(ae,{"current-page":s.value,"onUpdate:currentPage":r[0]||(r[0]=e=>s.value=e),"page-size":c.value,"onUpdate:pageSize":r[1]||(r[1]=e=>c.value=e),"page-sizes":[10,20,50,100],size:Y.value,disabled:E.value,background:j.value,layout:"total, sizes, prev, pager, next, jumper",total:N.value,onSizeChange:F,onCurrentChange:P},null,8,["current-page","page-size","size","disabled","background","total"])])]),_:1}),d(le,{dialogVisible:y.value,"onUpdate:dialogVisible":r[2]||(r[2]=e=>y.value=e),currentRow:M.value,onCancelBtn:$},null,8,["dialogVisible","currentRow"])])}}}),Ve=pe(be,[["__scopeId","data-v-589e1318"]]);export{Ve as default};
