var E=(L,n,s)=>new Promise((v,c)=>{var _=u=>{try{k(s.next(u))}catch(g){c(g)}},y=u=>{try{k(s.throw(u))}catch(g){c(g)}},k=u=>u.done?v(u.value):Promise.resolve(u.value).then(_,y);k((s=s.apply(L,n)).next())});import oe from"./index-B-sOercv.js";import{b as re}from"./index-BeicPvf_.js";import{d as se,r as l,o as ie,u as de,a as ue,c as C,b as i,e as d,w as r,f as U,g as p,F,h as I,i as pe,j as V,k as S,l as $,t as O,_ as ce}from"./index-VeYmKv4z.js";import"./quickReply-DfweD696.js";import"./dateFormat-BuOeynu9.js";const me={class:"table-header-flex"},ve={class:"form-btns"},ye={key:0},ge={key:1},fe={style:{display:"flex","justify-content":"flex-end","margin-top":"20px"}},A=420,be=se({__name:"index",setup(L){const n=l({}),s=l(!1),v=l(""),c=l(0),_=l(!1),y=l(!1);let k=[{type:"input",key:"enterpriseLegalPerson",label:"法人"},{type:"input",key:"hrCardName",label:"招聘者"},{type:"input",key:"name",label:"公司名称"},{type:"datetime",key:"dates",label:"提交时间"}];const u=[{property:"name",label:"公司名称",width:""},{property:"enterpriseLegalPerson",label:"法人",width:""},{property:"socialCreditCode",label:"社会信用代码",width:""},{property:"hrCardName",label:"招聘者",width:""},{property:"phone",label:"电话",width:""},{property:"createTime",label:"提交时间",width:""}],g=l([]),T=l(1),N=l(10),Q=l("default"),q=l(!1),G=l(!1),P=l(0),H=l({}),J=de(),o=l({entity:{cityCode:"",cityName:"",companyId:null,districtCode:"",districtName:"",provideCode:"",provideName:"",status:null,type:null},orderBy:{},page:1,size:10}),f=()=>E(null,null,function*(){_.value=!0;try{const e=yield re(o.value);e.code===0&&(P.value=e.data.total,g.value=e.data.list)}catch(e){}finally{_.value=!1}}),K=e=>{N.value=e,o.value.size=e,o.value.page=1,f()},W=e=>{T.value=e,o.value.page=e,f()},Y=l(window.innerHeight-A);function j(){Y.value=window.innerHeight-A}function X(e){H.value=e.row,s.value=!0,y.value=!1,v.value="note"}const Z=e=>{H.value=e.row,s.value=!0,y.value=!0,v.value="record"},ee=()=>{v.value="",s.value=!1,y.value=!1,f()},te=()=>{n.value.dates&&n.value.dates.length===2?(o.value.entity.startTime=n.value.dates[0],o.value.entity.endTime=n.value.dates[1]):(delete o.value.entity.startTime,delete o.value.entity.endTime),o.value.entity.name=n.value.name||void 0,o.value.entity.phone=n.value.phone||void 0,f()},ae=()=>{n.value={},o.value={entity:{endTime:"",id:null,name:"",phone:"",reason:"",startTime:"",status:c.value},orderBy:{},page:1,size:10},f()},le=e=>{if(!e)return"";const a=typeof e=="string"?parseInt(e):e;if(isNaN(a)||a<=0)return"";const D=a.toString().length===10?a*1e3:a,m=new Date(D),R=m.getFullYear(),B=String(m.getMonth()+1).padStart(2,"0"),b=String(m.getDate()).padStart(2,"0"),z=String(m.getHours()).padStart(2,"0"),x=String(m.getMinutes()).padStart(2,"0"),M=String(m.getSeconds()).padStart(2,"0");return`${R}-${B}-${b} ${z}:${x}:${M}`};return ie(()=>{const e=J.meta.businessStatus;o.value.entity.status=e!==void 0?Number(e):0,c.value=e!==void 0?Number(e):0,f(),window.addEventListener("resize",j)}),ue(()=>{window.removeEventListener("resize",j)}),(e,a)=>{const D=p("el-input"),m=p("el-date-picker"),R=p("el-form-item"),B=p("el-form"),b=p("el-button"),z=p("el-card"),x=p("el-table-column"),M=p("el-table"),ne=p("el-pagination");return i(),C("div",null,[d(z,{shadow:"never"},{default:r(()=>[U("div",me,[d(B,{inline:!0,model:n.value,class:"table-header-form"},{default:r(()=>[(i(!0),C(F,null,I(pe(k),(t,w)=>(i(),S(R,{key:w,label:t.label,class:"form-item"},{default:r(()=>[t.type==="input"?(i(),S(D,{key:0,modelValue:n.value[t.key],"onUpdate:modelValue":h=>n.value[t.key]=h,placeholder:"请输入"+t.label,clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])):t.type==="datetime"?(i(),S(m,{key:1,modelValue:n.value[t.key],"onUpdate:modelValue":h=>n.value[t.key]=h,type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",style:{width:"380px"},"value-format":"YYYY-MM-DD HH:mm:ss",clearable:""},null,8,["modelValue","onUpdate:modelValue"])):$("",!0)]),_:2},1032,["label"]))),128))]),_:1},8,["model"]),U("div",ve,[d(b,{size:"large",type:"primary",onClick:te},{default:r(()=>a[3]||(a[3]=[V("搜索")])),_:1}),d(b,{size:"large",type:"info",style:{"background-color":"#b4c4d1",color:"#ffffff",border:"#b4c4d1"},onClick:ae},{default:r(()=>a[4]||(a[4]=[V("重置")])),_:1})])])]),_:1}),d(z,{shadow:"never",style:{"margin-top":"15px",padding:"5px 15px"}},{default:r(()=>[d(M,{ref:"tableContainer",data:g.value,loading:_.value,style:{width:"100%"},border:"",height:Y.value},{default:r(()=>[(i(),C(F,null,I(u,(t,w)=>d(x,{key:w,width:t.width,label:t.label,"show-overflow-tooltip":""},{default:r(h=>[t.property==="createTime"?(i(),C("span",ye,O(le(h.row[t.property])),1)):(i(),C("span",ge,O(h.row[t.property]),1))]),_:2},1032,["width","label"])),64)),d(x,{fixed:"right",label:"操作","min-width":"120"},{default:r(t=>[c.value===0?(i(),S(b,{key:0,link:"",type:"primary",style:{color:"#279efb"},onClick:w=>X(t)},{default:r(()=>a[5]||(a[5]=[V(" 审批 ")])),_:2},1032,["onClick"])):$("",!0),c.value===1||c.value===2?(i(),S(b,{key:1,link:"",type:"primary",style:{color:"#4eb906"},onClick:w=>Z(t)},{default:r(()=>a[6]||(a[6]=[V(" 操作记录 ")])),_:2},1032,["onClick"])):$("",!0)]),_:1})]),_:1},8,["data","loading","height"]),U("div",fe,[d(ne,{"current-page":T.value,"onUpdate:currentPage":a[0]||(a[0]=t=>T.value=t),"page-size":N.value,"onUpdate:pageSize":a[1]||(a[1]=t=>N.value=t),"page-sizes":[10,20,50,100],size:Q.value,disabled:G.value,background:q.value,layout:"total, sizes, prev, pager, next, jumper",total:P.value,onSizeChange:K,onCurrentChange:W},null,8,["current-page","page-size","size","disabled","background","total"])])]),_:1}),d(oe,{dialogVisible:s.value,"onUpdate:dialogVisible":a[2]||(a[2]=t=>s.value=t),isRightType:v.value,currentRow:H.value,closeOnClickModal:y.value,onCancelBtn:ee},null,8,["dialogVisible","isRightType","currentRow","closeOnClickModal"])])}}}),ze=ce(be,[["__scopeId","data-v-ad9d7f2c"]]);export{ze as default};
