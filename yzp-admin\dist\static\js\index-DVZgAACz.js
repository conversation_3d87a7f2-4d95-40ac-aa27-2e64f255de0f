var ie=Object.defineProperty;var L=Object.getOwnPropertySymbols;var ue=Object.prototype.hasOwnProperty,de=Object.prototype.propertyIsEnumerable;var Y=(d,t,a)=>t in d?ie(d,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):d[t]=a,j=(d,t)=>{for(var a in t||(t={}))ue.call(t,a)&&Y(d,a,t[a]);if(L)for(var a of L(t))de.call(t,a)&&Y(d,a,t[a]);return d};var E=(d,t,a)=>new Promise((x,m)=>{var w=s=>{try{v(a.next(s))}catch(y){m(y)}},_=s=>{try{v(a.throw(s))}catch(y){m(y)}},v=s=>s.done?x(s.value):Promise.resolve(s.value).then(w,_);v((a=a.apply(d,t)).next())});import{d as pe,r as o,o as ce,u as me,a as ve,c as g,b as i,e as p,w as u,f as R,g as c,F as O,h as P,i as A,j as z,k,l as B,t as S,_ as ye}from"./index-VeYmKv4z.js";import fe from"./index-46i_ssQi.js";import{g as be}from"./index-D6X2KkBM.js";import{f as ge}from"./dateFormat-BuOeynu9.js";import"./quickReply-DfweD696.js";const _e={class:"table-header-flex"},he={class:"form-btns"},ke={key:0},we={key:1},Ce={key:2},ze={style:{display:"flex","justify-content":"flex-end","margin-top":"20px"}},F=420,xe=pe({__name:"index",setup(d){const t=o({}),a=o(!1),x=me(),m=o(0),w=o(0),_=o(!1),v=o(""),s=o({}),y=o(!1);let $=[{type:"input",key:"name",label:"姓名"},{type:"input",key:"phone",label:"手机号"},{type:"datetime",key:"dates",label:"提交时间"}];const r=o({entity:{endTime:"",name:"",phone:"",startTime:"",status:null},orderBy:{},page:1,size:10}),I=[{property:"name",label:"姓名",width:""},{property:"age",label:"年龄",width:""},{property:"sex",label:"性别",width:""},{property:"phone",label:"手机号码",width:""},{property:"certificate",label:"证书名称",width:""},{property:"createTime",label:"提交时间",width:""}],U=o([]),b=()=>E(null,null,function*(){_.value=!0;try{const l=yield be(j({},r.value));l.code===0&&(w.value=l.data.total,U.value=l.data.list)}catch(l){}finally{_.value=!1}}),T=o(1),V=o(10),Q=o("default"),q=o(!1),G=o(!1),J=l=>{V.value=l,r.value.size=l,r.value.page=1,b()},K=l=>{T.value=l,r.value.page=l,b()},H=o(window.innerHeight-F);function D(){H.value=window.innerHeight-F}const W=l=>{s.value=l.row,a.value=!0,y.value=!1,v.value="note"},X=l=>{s.value=l.row,a.value=!0,y.value=!0,v.value="record"},Z=()=>{v.value="",a.value=!1,y.value=!1,b()},ee=()=>{t.value.dates&&t.value.dates.length===2?(r.value.entity.startTime=t.value.dates[0],r.value.entity.endTime=t.value.dates[1]):(delete r.value.entity.startTime,delete r.value.entity.endTime),r.value.entity.name=t.value.name||void 0,r.value.entity.phone=t.value.phone||void 0,b()},te=()=>{t.value={},r.value={entity:{endTime:"",name:"",phone:"",startTime:"",status:m.value},orderBy:{},page:1,size:10},b()};return ce(()=>{const l=x.meta.businessStatus;r.value.entity.status=l!==void 0?Number(l):1,m.value=l!==void 0?Number(l):1,b(),window.addEventListener("resize",D)}),ve(()=>{window.removeEventListener("resize",D)}),(l,n)=>{const ae=c("el-input"),le=c("el-date-picker"),oe=c("el-form-item"),ne=c("el-form"),C=c("el-button"),M=c("el-card"),N=c("el-table-column"),re=c("el-table"),se=c("el-pagination");return i(),g("div",null,[p(M,{shadow:"never"},{default:u(()=>[R("div",_e,[p(ne,{inline:!0,model:t.value,class:"table-header-form"},{default:u(()=>[(i(!0),g(O,null,P(A($),(e,h)=>(i(),k(oe,{key:h,label:e.label,class:"form-item"},{default:u(()=>[e.type==="input"?(i(),k(ae,{key:0,modelValue:t.value[e.key],"onUpdate:modelValue":f=>t.value[e.key]=f,size:"large",placeholder:"请输入"+e.label,clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="datetime"?(i(),k(le,{key:1,modelValue:t.value[e.key],"onUpdate:modelValue":f=>t.value[e.key]=f,type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",size:"large",style:{width:"380px"},"value-format":"YYYY-MM-DD HH:mm:ss",clearable:""},null,8,["modelValue","onUpdate:modelValue"])):B("",!0)]),_:2},1032,["label"]))),128))]),_:1},8,["model"]),R("div",he,[p(C,{size:"large",type:"primary",onClick:ee},{default:u(()=>n[3]||(n[3]=[z("搜索")])),_:1}),p(C,{size:"large",type:"info",style:{"background-color":"#b4c4d1",color:"#ffffff",border:"#b4c4d1"},onClick:te},{default:u(()=>n[4]||(n[4]=[z("重置")])),_:1})])])]),_:1}),p(M,{shadow:"never",style:{"margin-top":"15px",padding:"5px 15px"}},{default:u(()=>[p(re,{ref:"tableContainer",data:U.value,style:{width:"100%"},loading:_.value,height:H.value},{default:u(()=>[(i(),g(O,null,P(I,(e,h)=>p(N,{key:h,width:e.width,label:e.label,property:e.property,"show-overflow-tooltip":""},{default:u(f=>[e.property==="createTime"?(i(),g("span",ke,S(A(ge)(f.row[e.property])),1)):e.property==="sex"?(i(),g("span",we,S(f.row[e.property]===1?"男":"女"),1)):(i(),g("span",Ce,S(f.row[e.property]),1))]),_:2},1032,["width","label","property"])),64)),p(N,{fixed:"right",label:"操作","min-width":"60"},{default:u(e=>[m.value===1?(i(),k(C,{key:0,link:"",type:"primary",style:{color:"#279efb"},onClick:h=>W(e)},{default:u(()=>n[5]||(n[5]=[z(" 视检 ")])),_:2},1032,["onClick"])):B("",!0),m.value===2||m.value===3?(i(),k(C,{key:1,link:"",type:"primary",style:{color:"#4eb906"},onClick:h=>X(e)},{default:u(()=>n[6]||(n[6]=[z(" 操作记录 ")])),_:2},1032,["onClick"])):B("",!0)]),_:1})]),_:1},8,["data","loading","height"]),R("div",ze,[p(se,{"current-page":T.value,"onUpdate:currentPage":n[0]||(n[0]=e=>T.value=e),"page-size":V.value,"onUpdate:pageSize":n[1]||(n[1]=e=>V.value=e),"page-sizes":[10,20,50,100],size:Q.value,disabled:G.value,background:q.value,layout:"total, sizes, prev, pager, next, jumper",total:w.value,onSizeChange:J,onCurrentChange:K},null,8,["current-page","page-size","size","disabled","background","total"])])]),_:1}),p(fe,{dialogVisible:a.value,"onUpdate:dialogVisible":n[2]||(n[2]=e=>a.value=e),isRightType:v.value,currentRow:s.value,closeOnClickModal:y.value,onCancelBtn:Z},null,8,["dialogVisible","isRightType","currentRow","closeOnClickModal"])])}}}),He=ye(xe,[["__scopeId","data-v-95f4bec8"]]);export{He as default};
