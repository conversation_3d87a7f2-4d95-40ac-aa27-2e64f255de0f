var I=(k,C,p)=>new Promise((d,s)=>{var T=o=>{try{v(p.next(o))}catch(c){s(c)}},g=o=>{try{v(p.throw(o))}catch(c){s(c)}},v=o=>o.done?d(o.value):Promise.resolve(o.value).then(T,g);v((p=p.apply(k,C)).next())});import{f as q,T as F,A as G,R as J}from"./index-DFv13GLf.js";import{i as K,j as W}from"./index-Dgu8EOgW.js";import{d as X,r as u,x as Y,O as N,c as h,b as l,S as Z,ab as V,k as f,w as i,e as a,f as r,l as ee,g as y,j as _,t as S,i as te,aQ as A,_ as se}from"./index-DOMkE6w1.js";const ae={class:"content-row"},oe={class:"img-card-row"},le={class:"img-card"},ie={key:1,class:"img-placeholder"},ne={class:"img-card"},re={key:1,class:"img-placeholder"},de={class:"card-box"},ue={key:0,class:"btn-group-bottom"},ce=X({__name:"index",props:{dialogVisible:{type:Boolean,default:!1},isRightType:{type:String,default:""},currentRow:{type:Object,default:()=>({})}},emits:["cancelBtn","update:dialogVisible","update:updataList"],setup(k,{emit:C}){const p=u(!1),d=u("note"),s=u({}),T=u(""),g=u([]),v=u([]),o=C,c=k,w=Y({get(){return c.dialogVisible},set(t){o("update:dialogVisible",t)}});N(()=>c.isRightType,t=>{d.value=t},{immediate:!0});const j=()=>I(null,null,function*(){(yield W({id:s.value.id})).code});function x(){o("cancelBtn",!0),d.value="note"}const B=t=>I(null,null,function*(){(yield K(t)).code===0&&(A.success("操作成功"),x())});function E(t){const e={id:s.value.id,status:1};B(e),o("update:updataList",!0)}function H(t){if(!b.value||b.value.trim()===""){A.warning("请填写驳回原因");return}const e={id:s.value.id,status:2,reason:b.value};B(e)}function O(){d.value="transfer"}function $(t){x()}const P=[{name:"A组张三",value:"zhangsan"},{name:"A组李四",value:"lisi"}],R=u("lisi"),z=[{label:"自定义批注",value:"none",content:""},{label:"资料不合规",value:"lack",content:"您提交的资料不合规，请重新提交。"}],b=u("");return N(()=>c.currentRow,t=>{g.value=[],v.value=[],g.value.push({auditUserName:(t==null?void 0:t.manualInspectionUserName)||"",auditTime:(t==null?void 0:t.manualInspectionTime)||"",status:(t==null?void 0:t.status)===1?"1":(t==null?void 0:t.status)===2?"2":"0",reason:(t==null?void 0:t.reason)||""}),s.value=t,T.value=t.headImgUrl,j()},{immediate:!0}),(t,e)=>{const m=y("el-table-column"),U=y("el-table"),D=y("el-image"),L=y("el-button"),M=y("el-dialog"),Q=V("loading");return l(),h("div",null,[Z((l(),f(M,{modelValue:w.value,"onUpdate:modelValue":e[4]||(e[4]=n=>w.value=n),"close-on-click-modal":"","destroy-on-close":"",title:null,width:"1100px","show-close":!0,class:"custom-detail-dialog","align-center":"",onClose:x},{default:i(()=>[a(U,{data:[s.value],border:"",style:{width:"100%","margin-bottom":"12px"},class:"info-table-normal"},{default:i(()=>[a(m,{prop:"name","show-overflow-tooltip":"",label:"企业名称","min-width":"180"}),a(m,{prop:"socialCreditCode","show-overflow-tooltip":"",label:"社会信用代码","min-width":"180"}),a(m,{prop:"enterpriseLegalPerson","show-overflow-tooltip":"",label:"法人","min-width":"120"})]),_:1},8,["data"]),a(U,{data:[s.value],border:"",style:{width:"100%"},class:"info-table-normal"},{default:i(()=>[a(m,{prop:"userId",label:"提交人ID","min-width":"100"},{default:i(n=>[_(S(`ID：${n.row.userId}`),1)]),_:1}),a(m,{prop:"hrCardName",label:"提交人姓名","min-width":"120"}),a(m,{prop:"phone",label:"提交人电话","min-width":"140"}),a(m,{prop:"createTime",label:"提交时间","min-width":"180"},{default:i(n=>[_(S(te(q)(n.row.createTime)),1)]),_:1})]),_:1},8,["data"]),r("div",ae,[r("div",oe,[r("div",le,[e[6]||(e[6]=r("div",{class:"img-title"},"营业执照",-1)),s.value.businessLicense?(l(),f(D,{key:0,src:s.value.businessLicense,class:"img-preview",fit:"contain","preview-src-list":[s.value.businessLicense],"initial-index":0},{error:i(()=>e[5]||(e[5]=[r("div",{class:"img-placeholder"},null,-1)])),_:1},8,["src","preview-src-list"])):(l(),h("div",ie))]),r("div",ne,[e[8]||(e[8]=r("div",{class:"img-title"},"在职证明",-1)),s.value.employmentCert?(l(),f(D,{key:0,src:s.value.employmentCert,class:"img-preview",fit:"contain","preview-src-list":[s.value.employmentCert],"initial-index":0},{error:i(()=>e[7]||(e[7]=[r("div",{class:"img-placeholder"},null,-1)])),_:1},8,["src","preview-src-list"])):(l(),h("div",re))])]),r("div",de,[d.value==="transfer"?(l(),f(F,{key:0,modelValue:R.value,"onUpdate:modelValue":e[0]||(e[0]=n=>R.value=n),transferList:P,onSubmit:$},null,8,["modelValue"])):d.value==="record"?(l(),f(G,{key:1,auditList:g.value,statusList:v.value},null,8,["auditList","statusList"])):(l(),f(J,{key:2,modelValue:b.value,"onUpdate:modelValue":e[1]||(e[1]=n=>b.value=n),options:z},null,8,["modelValue"]))])]),d.value==="note"?(l(),h("div",ue,[a(L,{type:"primary",style:{background:"#3477f4",border:"none"},onClick:O},{default:i(()=>e[9]||(e[9]=[_("转审")])),_:1}),a(L,{type:"danger",onClick:e[2]||(e[2]=()=>H("reject"))},{default:i(()=>e[10]||(e[10]=[_("驳回")])),_:1}),a(L,{type:"success",onClick:e[3]||(e[3]=()=>E("pass"))},{default:i(()=>e[11]||(e[11]=[_("通过")])),_:1})])):ee("",!0)]),_:1},8,["modelValue"])),[[Q,p.value]])])}}}),ge=se(ce,[["__scopeId","data-v-acaa9e6c"]]);export{ge as default};
