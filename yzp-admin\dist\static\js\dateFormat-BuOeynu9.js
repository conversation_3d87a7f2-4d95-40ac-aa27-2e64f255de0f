const Y=(r,e,s=!1)=>{if(!r)return"";const n=typeof r=="string"?parseInt(r):r;if(isNaN(n)||n<=0)return"";const a=n.toString().length===10?n*1e3:n,t=new Date(a);if(isNaN(t.getTime()))return"";const g=t.getFullYear(),c=String(t.getMonth()+1).padStart(2,"0"),i=String(t.getDate()).padStart(2,"0"),o=String(t.getHours()).padStart(2,"0"),l=String(t.getMinutes()).padStart(2,"0"),S=String(t.getSeconds()).padStart(2,"0");return s?e=e||"YYYY-MM-DD":e=e||"YYYY-MM-DD HH:mm:ss",e.replace(/YYYY/g,g.toString()).replace(/MM/g,c).replace(/DD/g,i).replace(/HH/g,o).replace(/mm/g,l).replace(/ss/g,S)};export{Y as f};
