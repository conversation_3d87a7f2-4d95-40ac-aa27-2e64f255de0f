var X=Object.defineProperty;var $=Object.getOwnPropertySymbols;var Y=Object.prototype.hasOwnProperty,Z=Object.prototype.propertyIsEnumerable;var j=(l,o,a)=>o in l?X(l,o,{enumerable:!0,configurable:!0,writable:!0,value:a}):l[o]=a,E=(l,o)=>{for(var a in o||(o={}))Y.call(o,a)&&j(l,a,o[a]);if($)for(var a of $(o))Z.call(o,a)&&j(l,a,o[a]);return l};var v=(l,o,a)=>new Promise((T,i)=>{var A=n=>{try{u(a.next(n))}catch(r){i(r)}},d=n=>{try{u(a.throw(n))}catch(r){i(r)}},u=n=>n.done?T(n.value):Promise.resolve(n.value).then(A,d);u((a=a.apply(l,o)).next())});import{f as V,T as ee,A as te,R as ae}from"./index-DFv13GLf.js";import{o as oe}from"./index-BPHCkZA8.js";import{d as se,e as le}from"./index-C5VTKhL7.js";import{n as ie}from"./quickReply-D6WzbhB8.js";import{d as ne,r as s,x as re,O,c as C,b as c,S as de,ab as ue,k as g,w as f,e as p,f as b,l as ce,g as k,j as y,t as P,i as R,aQ as _,_ as pe}from"./index-DOMkE6w1.js";const me={class:"content-row"},fe={class:"card-box attachment-box"},ve={class:"pdf-preview-wrapper"},ge={key:1,class:"no-pdf"},be={class:"card-box"},ye={key:0,class:"btn-group-bottom"},_e=ne({__name:"index",props:{dialogVisible:{type:Boolean,default:!1},isRightType:{type:String,default:"note"},currentRow:{type:Object,default:()=>({})}},emits:["cancelBtn","update:dialogVisible"],setup(l,{emit:o}){const a=s(""),T=s(!1),i=s("note"),A=s(""),d=s({}),u=s(""),n=s(!1),r=s(""),W=s(0),U=s([]),S=s([]),D=o,h=l,I=re({get(){return h.dialogVisible},set(e){D("update:dialogVisible",e)}});O(()=>h.isRightType,e=>{i.value=e},{immediate:!0});const G=()=>v(null,null,function*(){try{n.value=!0,r.value="",W.value=0;const e=yield le({id:h.currentRow.id});if(e.code===0){const t=e.data[0].fileUrl||"";t&&t.includes("img-test.easyzhipin.com")?u.value=t.replace("https://img-test.easyzhipin.com","/api/pdf"):u.value=t}else r.value=e.message||"获取附件失败",_.error(r.value)}catch(e){console.error("获取附件失败:",e),r.value="网络错误，请稍后重试",_.error(r.value)}finally{}}),N=e=>v(null,null,function*(){const t=yield se(E({},e));t.code===0?_.success("操作成功"):_.error(t.message)});function L(){D("cancelBtn",!0),i.value="note"}function M(e){return v(this,null,function*(){yield N({id:d.value.id,status:1}),L()})}function Q(e){return v(this,null,function*(){if(!a.value){_.warning("请输入驳回原因");return}yield N({id:d.value.id,status:2,reason:a.value}),L()})}function q(){i.value="transfer"}function w(e){i.value="note"}const F=[{name:"A组张三",value:"zhangsan"},{name:"A组李四",value:"lisi"}],z=s("lisi");return O(()=>h.currentRow,e=>{U.value=[],S.value=[],U.value.push({auditUserName:(e==null?void 0:e.manualInspectionUserName)||"",auditTime:(e==null?void 0:e.manualInspectionTime)||"",status:(e==null?void 0:e.status)===1?"1":(e==null?void 0:e.status)===2?"2":"0",reason:(e==null?void 0:e.reason)||""}),d.value=e,A.value=e.headImgUrl,G()},{immediate:!0}),(e,t)=>{const x=k("el-table-column"),H=k("el-table"),B=k("el-button"),J=k("el-dialog"),K=ue("loading");return c(),C("div",null,[de((c(),g(J,{modelValue:I.value,"onUpdate:modelValue":t[4]||(t[4]=m=>I.value=m),"close-on-click-modal":"","destroy-on-close":"",title:null,width:"1100px","show-close":!0,class:"custom-detail-dialog","align-center":"",onClose:L},{default:f(()=>[p(H,{data:[d.value],border:"",class:"info-table info-table-top",style:{width:"100%","margin-bottom":"32px"},"show-header":!0,"header-cell-style":{background:"#fff",color:"#888",fontWeight:500,fontSize:"16px",textAlign:"center"},"cell-style":{background:"#fff",color:"#222",fontSize:"18px",fontWeight:500,textAlign:"center"}},{default:f(()=>[p(x,{prop:"id",label:"提交人ID","show-overflow-tooltip":"","min-width":"120"},{default:f(()=>[y(P(`ID：${d.value.id}`),1)]),_:1}),p(x,{prop:"name",label:"提交人姓名","min-width":"100"}),p(x,{prop:"phone",label:"提交人电话","min-width":"140"}),p(x,{prop:"createTime",label:"提交时间","min-width":"180"},{default:f(()=>[y(P(R(V)(d.value.createTime)),1)]),_:1})]),_:1},8,["data"]),b("div",me,[b("div",fe,[t[5]||(t[5]=b("div",{class:"card-title"},"附件简历",-1)),b("div",ve,[u.value?(c(),g(R(oe),{key:0,source:u.value,class:"pdf-preview"},null,8,["source"])):(c(),C("div",ge,"暂无附件"))])]),b("div",be,[i.value==="transfer"?(c(),g(ee,{key:0,modelValue:z.value,"onUpdate:modelValue":t[0]||(t[0]=m=>z.value=m),transferList:F,onSubmit:w},null,8,["modelValue"])):i.value==="record"?(c(),g(te,{key:1,auditList:U.value,statusList:S.value},null,8,["auditList","statusList"])):(c(),g(ae,{key:2,modelValue:a.value,"onUpdate:modelValue":t[1]||(t[1]=m=>a.value=m),options:R(ie)},null,8,["modelValue","options"]))])]),i.value==="note"?(c(),C("div",ye,[p(B,{type:"primary",style:{background:"#3477f4",border:"none"},onClick:q},{default:f(()=>t[6]||(t[6]=[y("转审")])),_:1}),p(B,{type:"danger",onClick:t[2]||(t[2]=m=>Q("reject"))},{default:f(()=>t[7]||(t[7]=[y("驳回")])),_:1}),p(B,{type:"success",onClick:t[3]||(t[3]=m=>M("pass"))},{default:f(()=>t[8]||(t[8]=[y("通过")])),_:1})])):ce("",!0)]),_:1},8,["modelValue"])),[[K,T.value]])])}}}),Le=pe(_e,[["__scopeId","data-v-2c9db96b"]]);export{Le as default};
