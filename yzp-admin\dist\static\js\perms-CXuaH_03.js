import{d as g,aB as k,g as p,ab as c,c as B,b as d,f as o,T as l,e as t,t as x,i as a,U as C,w as s,j as i,k as r,l as _,aX as v}from"./index-VeYmKv4z.js";const N={class:"mb-2!"},P={class:"mb-2!"},T=g({name:"PermissionButtonLogin",__name:"perms",setup(V){const{permissions:y}=k();return(D,e)=>{var w;const n=p("el-button"),m=p("Perms",!0),b=p("el-space"),u=p("el-card"),f=c("perms");return d(),B("div",null,[o("p",N,"当前拥有的code列表："+x(a(y)),1),l(o("p",P," *:*:* 代表拥有全部按钮级别权限 ",512),[[C,((w=a(y))==null?void 0:w[0])==="*:*:*"]]),t(u,{shadow:"never",class:"mb-2"},{header:s(()=>e[0]||(e[0]=[o("div",{class:"card-header"},"组件方式判断权限",-1)])),default:s(()=>[t(b,{wrap:""},{default:s(()=>[t(m,{value:"permission:btn:add"},{default:s(()=>[t(n,{plain:"",type:"warning"},{default:s(()=>e[1]||(e[1]=[i(" 拥有code：'permission:btn:add' 权限可见 ")])),_:1})]),_:1}),t(m,{value:["permission:btn:edit"]},{default:s(()=>[t(n,{plain:"",type:"primary"},{default:s(()=>e[2]||(e[2]=[i(" 拥有code：['permission:btn:edit'] 权限可见 ")])),_:1})]),_:1}),t(m,{value:["permission:btn:add","permission:btn:edit","permission:btn:delete"]},{default:s(()=>[t(n,{plain:"",type:"danger"},{default:s(()=>e[3]||(e[3]=[i(" 拥有code：['permission:btn:add', 'permission:btn:edit', 'permission:btn:delete'] 权限可见 ")])),_:1})]),_:1})]),_:1})]),_:1}),t(u,{shadow:"never",class:"mb-2"},{header:s(()=>e[4]||(e[4]=[o("div",{class:"card-header"},"函数方式判断权限",-1)])),default:s(()=>[t(b,{wrap:""},{default:s(()=>[a(v)("permission:btn:add")?(d(),r(n,{key:0,plain:"",type:"warning"},{default:s(()=>e[5]||(e[5]=[i(" 拥有code：'permission:btn:add' 权限可见 ")])),_:1})):_("",!0),a(v)(["permission:btn:edit"])?(d(),r(n,{key:1,plain:"",type:"primary"},{default:s(()=>e[6]||(e[6]=[i(" 拥有code：['permission:btn:edit'] 权限可见 ")])),_:1})):_("",!0),a(v)(["permission:btn:add","permission:btn:edit","permission:btn:delete"])?(d(),r(n,{key:2,plain:"",type:"danger"},{default:s(()=>e[7]||(e[7]=[i(" 拥有code：['permission:btn:add', 'permission:btn:edit', 'permission:btn:delete'] 权限可见 ")])),_:1})):_("",!0)]),_:1})]),_:1}),t(u,{shadow:"never"},{header:s(()=>e[8]||(e[8]=[o("div",{class:"card-header"}," 指令方式判断权限（该方式不能动态修改权限） ",-1)])),default:s(()=>[t(b,{wrap:""},{default:s(()=>[l((d(),r(n,{plain:"",type:"warning"},{default:s(()=>e[9]||(e[9]=[i(" 拥有code：'permission:btn:add' 权限可见 ")])),_:1})),[[f,"permission:btn:add"]]),l((d(),r(n,{plain:"",type:"primary"},{default:s(()=>e[10]||(e[10]=[i(" 拥有code：['permission:btn:edit'] 权限可见 ")])),_:1})),[[f,["permission:btn:edit"]]]),l((d(),r(n,{plain:"",type:"danger"},{default:s(()=>e[11]||(e[11]=[i(" 拥有code：['permission:btn:add', 'permission:btn:edit', 'permission:btn:delete'] 权限可见 ")])),_:1})),[[f,["permission:btn:add","permission:btn:edit","permission:btn:delete"]]])]),_:1})]),_:1})])}}});export{T as default};
