import{d as y,x as g,r as w,aB as u,g as s,c,b as d,e as l,w as n,F as C,h as b,f as m,t as x,U as k,N as B,K as S,aC as V}from"./index-DOMkE6w1.js";const N={class:"card-header"},F=y({name:"PermissionPage",__name:"index",setup(P){var o;const i=g(()=>({width:"85vw",justifyContent:"start"})),a=w((o=u())==null?void 0:o.username),p=[{value:"admin",label:"管理员角色"},{value:"common",label:"普通角色"}];function _(){u().loginByUsername({username:a.value,password:"admin123"}).then(t=>{t.success&&(B().removeItem("async-routes"),S().clearAllCachePage(),V())})}return(t,r)=>{const v=s("el-option"),f=s("el-select"),h=s("el-card");return d(),c("div",null,[l(h,{shadow:"never",style:k(i.value)},{header:n(()=>[m("div",N,[m("span",null,"当前角色："+x(a.value),1)])]),default:n(()=>[l(f,{modelValue:a.value,"onUpdate:modelValue":r[0]||(r[0]=e=>a.value=e),class:"w-[160px]!",onChange:_},{default:n(()=>[(d(),c(C,null,b(p,e=>l(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1},8,["style"])])}}});export{F as default};
