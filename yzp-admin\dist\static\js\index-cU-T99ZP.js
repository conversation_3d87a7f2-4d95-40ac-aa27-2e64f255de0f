var j=(L,n,s)=>new Promise((m,p)=>{var k=d=>{try{w(s.next(d))}catch(y){p(y)}},N=d=>{try{w(s.throw(d))}catch(y){p(y)}},w=d=>d.done?m(d.value):Promise.resolve(d.value).then(k,N);w((s=s.apply(L,n)).next())});import oe from"./index-CMJUaphJ.js";import{b as re}from"./index-Dgu8EOgW.js";import{d as se,r as l,o as ie,u as de,a as ue,c as C,b as i,e as u,w as r,f as B,g as c,F as E,h as F,i as pe,j as S,k as _,l as V,t as I,_ as ce}from"./index-DOMkE6w1.js";import"./index-DFv13GLf.js";const ve={class:"table-header-flex"},me={class:"form-btns"},ye={key:0},ge={key:1},fe={style:{display:"flex","justify-content":"flex-end","margin-top":"20px"}},A=420,be=se({__name:"index",setup(L){const n=l({}),s=l(!1),m=l(""),p=l(0),k=l(!1);let N=[{type:"input",key:"name",label:"法人"},{type:"input",key:"phone",label:"招聘者"},{type:"input",key:"phone",label:"公司名称"},{type:"datetime",key:"dates",label:"提交时间"}];const w=[{property:"name",label:"公司名称",width:""},{property:"enterpriseLegalPerson",label:"法人",width:""},{property:"socialCreditCode",label:"社会信用代码",width:""},{property:"hrCardName",label:"招聘者",width:""},{property:"phone",label:"电话",width:""},{property:"createTime",label:"提交时间",width:""}],d=l([]),y=l(1),H=l(10),Q=l("default"),q=l(!1),G=l(!1),M=l(0),z=l({}),J=de(),o=l({entity:{cityCode:"",cityName:"",companyId:null,districtCode:"",districtName:"",provideCode:"",provideName:"",status:null,type:null},orderBy:{},page:1,size:10}),f=()=>j(null,null,function*(){k.value=!0;try{const e=yield re(o.value);e.code===0&&(M.value=e.data.total,d.value=e.data.list)}catch(e){}finally{k.value=!1}}),K=e=>{H.value=e,o.value.size=e,o.value.page=1,f()},O=e=>{y.value=e,o.value.page=e,f()},Y=l(window.innerHeight-A);function P(){Y.value=window.innerHeight-A}function W(e){z.value=e.row,s.value=!0,m.value="note"}const X=e=>{z.value=e.row,s.value=!0,m.value="transfer"},Z=e=>{z.value=e.row,s.value=!0,m.value="record"},ee=()=>{m.value="",s.value=!1},te=()=>{n.value.dates&&n.value.dates.length===2?(o.value.entity.startTime=n.value.dates[0],o.value.entity.endTime=n.value.dates[1]):(delete o.value.entity.startTime,delete o.value.entity.endTime),o.value.entity.name=n.value.name||void 0,o.value.entity.phone=n.value.phone||void 0,f()},ae=()=>{n.value={},o.value={entity:{endTime:"",id:null,name:"",phone:"",reason:"",startTime:"",status:p.value},orderBy:{},page:1,size:10},f()},le=e=>{if(!e)return"";const a=typeof e=="string"?parseInt(e):e;if(isNaN(a)||a<=0)return"";const D=a.toString().length===10?a*1e3:a,v=new Date(D),R=v.getFullYear(),U=String(v.getMonth()+1).padStart(2,"0"),g=String(v.getDate()).padStart(2,"0"),x=String(v.getHours()).padStart(2,"0"),T=String(v.getMinutes()).padStart(2,"0"),$=String(v.getSeconds()).padStart(2,"0");return`${R}-${U}-${g} ${x}:${T}:${$}`};return ie(()=>{const e=J.meta.businessStatus;o.value.entity.status=e!==void 0?Number(e):0,p.value=e!==void 0?Number(e):0,f(),window.addEventListener("resize",P)}),ue(()=>{window.removeEventListener("resize",P)}),(e,a)=>{const D=c("el-input"),v=c("el-date-picker"),R=c("el-form-item"),U=c("el-form"),g=c("el-button"),x=c("el-card"),T=c("el-table-column"),$=c("el-table"),ne=c("el-pagination");return i(),C("div",null,[u(x,{shadow:"never"},{default:r(()=>[B("div",ve,[u(U,{inline:!0,model:n.value,class:"table-header-form"},{default:r(()=>[(i(!0),C(E,null,F(pe(N),(t,b)=>(i(),_(R,{key:b,label:t.label,class:"form-item"},{default:r(()=>[t.type==="input"?(i(),_(D,{key:0,modelValue:n.value[t.key],"onUpdate:modelValue":h=>n.value[t.key]=h,placeholder:"请输入"+t.label,clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])):t.type==="datetime"?(i(),_(v,{key:1,modelValue:n.value[t.key],"onUpdate:modelValue":h=>n.value[t.key]=h,type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",style:{width:"380px"},"value-format":"YYYY-MM-DD HH:mm:ss",clearable:""},null,8,["modelValue","onUpdate:modelValue"])):V("",!0)]),_:2},1032,["label"]))),128))]),_:1},8,["model"]),B("div",me,[u(g,{size:"large",type:"primary",onClick:te},{default:r(()=>a[3]||(a[3]=[S("搜索")])),_:1}),u(g,{size:"large",type:"info",style:{"background-color":"#b4c4d1",color:"#ffffff",border:"#b4c4d1"},onClick:ae},{default:r(()=>a[4]||(a[4]=[S("重置")])),_:1})])])]),_:1}),u(x,{shadow:"never",style:{"margin-top":"15px",padding:"5px 15px"}},{default:r(()=>[u($,{ref:"tableContainer",data:d.value,loading:k.value,style:{width:"100%"},border:"",height:Y.value},{default:r(()=>[(i(),C(E,null,F(w,(t,b)=>u(T,{key:b,width:t.width,label:t.label,"show-overflow-tooltip":""},{default:r(h=>[t.property==="createTime"?(i(),C("span",ye,I(le(h.row[t.property])),1)):(i(),C("span",ge,I(h.row[t.property]),1))]),_:2},1032,["width","label"])),64)),u(T,{fixed:"right",label:"操作","min-width":"120"},{default:r(t=>[p.value===0?(i(),_(g,{key:0,link:"",type:"primary",style:{color:"#279efb"},onClick:b=>W(t)},{default:r(()=>a[5]||(a[5]=[S(" 视检 ")])),_:2},1032,["onClick"])):V("",!0),p.value===0?(i(),_(g,{key:1,link:"",type:"primary",style:{color:"#fb2727"},onClick:b=>X(t)},{default:r(()=>a[6]||(a[6]=[S(" 转办 ")])),_:2},1032,["onClick"])):V("",!0),p.value===1||p.value===2?(i(),_(g,{key:2,link:"",type:"primary",style:{color:"#4eb906"},onClick:b=>Z(t)},{default:r(()=>a[7]||(a[7]=[S(" 操作记录 ")])),_:2},1032,["onClick"])):V("",!0)]),_:1})]),_:1},8,["data","loading","height"]),B("div",fe,[u(ne,{"current-page":y.value,"onUpdate:currentPage":a[0]||(a[0]=t=>y.value=t),"page-size":H.value,"onUpdate:pageSize":a[1]||(a[1]=t=>H.value=t),"page-sizes":[10,20,50,100],size:Q.value,disabled:G.value,background:q.value,layout:"total, sizes, prev, pager, next, jumper",total:M.value,onSizeChange:K,onCurrentChange:O},null,8,["current-page","page-size","size","disabled","background","total"])])]),_:1}),u(oe,{dialogVisible:s.value,"onUpdate:dialogVisible":a[2]||(a[2]=t=>s.value=t),isRightType:m.value,currentRow:z.value,onCancelBtn:ee,"onUpdate:updataList":f},null,8,["dialogVisible","isRightType","currentRow"])])}}}),Se=ce(be,[["__scopeId","data-v-19de05d7"]]);export{Se as default};
