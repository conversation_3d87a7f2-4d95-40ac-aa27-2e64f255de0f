var X=Object.defineProperty;var N=Object.getOwnPropertySymbols;var Y=Object.prototype.hasOwnProperty,Z=Object.prototype.propertyIsEnumerable;var $=(l,s,a)=>s in l?X(l,s,{enumerable:!0,configurable:!0,writable:!0,value:a}):l[s]=a,j=(l,s)=>{for(var a in s||(s={}))Y.call(s,a)&&$(l,a,s[a]);if(N)for(var a of N(s))Z.call(s,a)&&$(l,a,s[a]);return l};var g=(l,s,a)=>new Promise((L,i)=>{var u=o=>{try{n(a.next(o))}catch(c){i(c)}},U=o=>{try{n(a.throw(o))}catch(c){i(c)}},n=o=>o.done?L(o.value):Promise.resolve(o.value).then(u,U);n((a=a.apply(l,s)).next())});import{T as V,A as ee,R as te,n as ae}from"./quickReply-DfweD696.js";import{o as oe}from"./index-C3VS7c2V.js";import{f as se}from"./dateFormat-BuOeynu9.js";import{d as le,e as ie}from"./index-D6X2KkBM.js";import{d as ne,r as d,m as de,P as E,c as h,b as r,T as re,ab as ue,k as b,w as v,e as m,f,l as k,g as x,j as T,t as F,i as C,V as ce,aQ as A,_ as pe}from"./index-VeYmKv4z.js";const me={class:"content-row"},fe={class:"card-box attachment-box"},ve={class:"pdf-preview-wrapper"},ge={key:0,class:"pdf-loading"},be={key:2,class:"no-pdf"},ye={class:"card-box"},_e={key:0,class:"btn-group-bottom"},he=ne({__name:"index",props:{dialogVisible:{type:Boolean,default:!1},isRightType:{type:String,default:"note"},currentRow:{type:Object,default:()=>({})},closeOnClickModal:{type:Boolean,default:!1}},emits:["cancelBtn","update:dialogVisible"],setup(l,{emit:s}){const a=d(""),L=d(!1),i=d(!1),u=d("note"),U=d(""),n=d({}),o=d(""),c=d([]),D=d([]),R=s,y=l,S=de({get(){return y.dialogVisible},set(e){R("update:dialogVisible",e)}});E(()=>y.isRightType,e=>{u.value=e},{immediate:!0});const O=()=>g(null,null,function*(){try{const e=yield ie({id:y.currentRow.id});if(e.code===0){const t=e.data[0].fileUrl||"";t&&t.includes("img-test.easyzhipin.com")?o.value=t.replace("https://img-test.easyzhipin.com","/api/pdf"):o.value=t,o.value&&(i.value=!0)}}catch(e){console.error("获取PDF失败:",e),i.value=!1}}),I=e=>g(null,null,function*(){const t=yield le(j({},e));t.code===0?A.success("操作成功"):A.error(t.message)});function B(){R("cancelBtn",!0),u.value="note"}function M(e){return g(this,null,function*(){yield I({id:n.value.id,status:1}),B()})}function W(e){return g(this,null,function*(){if(!a.value){A.warning("请输入驳回原因");return}yield I({id:n.value.id,status:2,reason:a.value}),B()})}function G(e){u.value="note"}const Q=()=>{i.value=!1},q=e=>{i.value=!1,A.error("PDF加载失败，请稍后重试")},w=[{name:"A组张三",value:"zhangsan"},{name:"A组李四",value:"lisi"}],P=d("lisi");return E(()=>y.currentRow,e=>{i.value=!1,o.value="",c.value=[],D.value=[],c.value.push({auditUserName:(e==null?void 0:e.manualInspectionUserName)||"",auditTime:(e==null?void 0:e.manualInspectionTime)||"",status:(e==null?void 0:e.status)===1?"1":(e==null?void 0:e.status)===2?"2":"0",reason:(e==null?void 0:e.reason)||""}),n.value=e,U.value=e.headImgUrl,O()},{immediate:!0}),(e,t)=>{const _=x("el-table-column"),H=x("el-table"),z=x("el-button"),J=x("el-dialog"),K=ue("loading");return r(),h("div",null,[re((r(),b(J,{modelValue:S.value,"onUpdate:modelValue":t[4]||(t[4]=p=>S.value=p),"close-on-click-modal":l.closeOnClickModal,"destroy-on-close":"",title:null,width:"1100px","show-close":!0,class:"custom-detail-dialog","align-center":"",onClose:B},{default:v(()=>[m(H,{data:[n.value],border:"",class:"info-table info-table-top",style:{width:"100%","margin-bottom":"32px"},"show-header":!0,"header-cell-style":{background:"#fff",color:"#888",fontWeight:500,fontSize:"16px",textAlign:"center"},"cell-style":{background:"#fff",color:"#222",fontSize:"18px",fontWeight:500,textAlign:"center"}},{default:v(()=>[m(_,{prop:"id",label:"提交人ID","show-overflow-tooltip":"","min-width":"120"},{default:v(()=>[T(F(`ID：${n.value.id}`),1)]),_:1}),m(_,{prop:"name",label:"提交人姓名","min-width":"100"}),m(_,{prop:"phone",label:"提交人电话","min-width":"140"}),m(_,{prop:"createTime",label:"提交时间","min-width":"180"},{default:v(()=>[T(F(C(se)(n.value.createTime)),1)]),_:1})]),_:1},8,["data"]),f("div",me,[f("div",fe,[t[6]||(t[6]=f("div",{class:"card-title"},"附件简历",-1)),f("div",ve,[i.value&&o.value?(r(),h("div",ge,t[5]||(t[5]=[f("div",{class:"loading-spinner"},null,-1),f("div",{class:"loading-text"},"PDF加载中...",-1)]))):k("",!0),o.value?(r(),b(C(oe),{key:1,source:o.value,class:"pdf-preview",style:ce({display:i.value?"none":"block"}),onLoaded:Q,onLoadingFailed:q},null,8,["source","style"])):k("",!0),o.value?k("",!0):(r(),h("div",be,"暂无附件"))])]),f("div",ye,[u.value==="transfer"?(r(),b(V,{key:0,modelValue:P.value,"onUpdate:modelValue":t[0]||(t[0]=p=>P.value=p),transferList:w,onSubmit:G},null,8,["modelValue"])):u.value==="record"?(r(),b(ee,{key:1,auditList:c.value,statusList:D.value},null,8,["auditList","statusList"])):(r(),b(te,{key:2,modelValue:a.value,"onUpdate:modelValue":t[1]||(t[1]=p=>a.value=p),options:C(ae)},null,8,["modelValue","options"]))])]),u.value==="note"?(r(),h("div",_e,[m(z,{type:"danger",onClick:t[2]||(t[2]=p=>W("reject"))},{default:v(()=>t[7]||(t[7]=[T("驳回")])),_:1}),m(z,{type:"success",onClick:t[3]||(t[3]=p=>M("pass"))},{default:v(()=>t[8]||(t[8]=[T("通过")])),_:1})])):k("",!0)]),_:1},8,["modelValue","close-on-click-modal"])),[[K,L.value]])])}}}),Be=pe(he,[["__scopeId","data-v-a2fb4475"]]);export{Be as default};
