var y=(T,k,c)=>new Promise((l,i)=>{var m=s=>{try{u(c.next(s))}catch(p){i(p)}},b=s=>{try{u(c.throw(s))}catch(p){i(p)}},u=s=>s.done?l(s.value):Promise.resolve(s.value).then(m,b);u((c=c.apply(T,k)).next())});import{f as Q,T as q,A as F,R as G}from"./index-DFv13GLf.js";import{f as H}from"./index-C5VTKhL7.js";import{n as J}from"./quickReply-D6WzbhB8.js";import{d as K,r as n,x as X,O as I,c as L,b as f,S as Y,ab as Z,k as h,w as r,e as o,f as x,l as V,g,j as v,t as S,i as N,aQ as A,_ as ee}from"./index-DOMkE6w1.js";const te={class:"content-row"},ae={class:"card-box"},oe={class:"card-box"},se={key:0,class:"btn-group-bottom"},le=K({__name:"index",props:{dialogVisible:{type:Boolean,default:!1},isRightType:{type:String,default:"note"},currentRow:{type:Object,default:()=>({})}},emits:["cancelBtn","update:dialogVisible"],setup(T,{emit:k}){const c=n(!1),l=n("note"),i=n(""),m=n({}),b=n(""),u=n([]),s=n([]),p=k,C=T,B=X({get(){return C.dialogVisible},set(e){p("update:dialogVisible",e)}});I(()=>C.isRightType,e=>{l.value=e},{immediate:!0});function _(){p("cancelBtn",!0),l.value="note"}const R=e=>y(null,null,function*(){const t=yield H(e);t.code===0?A.success("操作成功"):A.error(t.message)});function w(e){return y(this,null,function*(){yield R({id:m.value.id,status:3}),_()})}function $(e){return y(this,null,function*(){if(!i.value||i.value.trim()===""){A.warning("请填写驳回原因");return}yield R({id:m.value.id,status:2,reason:i.value}),_()})}function j(){_()}function z(e){l.value="note"}const E=[{name:"A组张三",value:"zhangsan"},{name:"A组李四",value:"lisi"}],D=n("lisi");return I(()=>C.currentRow,e=>{u.value=[],s.value=[],u.value.push({auditUserName:(e==null?void 0:e.manualInspectionUserName)||"",auditTime:(e==null?void 0:e.manualInspectionTime)||"",status:(e==null?void 0:e.status)===3?"1":(e==null?void 0:e.status)===2?"2":"0",reason:(e==null?void 0:e.reason)||""}),m.value=e,b.value=e.attachmentUrl},{immediate:!0}),(e,t)=>{const d=g("el-table-column"),O=g("el-table"),W=g("el-image"),U=g("el-button"),M=g("el-dialog"),P=Z("loading");return f(),L("div",null,[Y((f(),h(M,{modelValue:B.value,"onUpdate:modelValue":t[4]||(t[4]=a=>B.value=a),"close-on-click-modal":"","destroy-on-close":"",title:null,width:"1100px","show-close":!0,class:"custom-detail-dialog","align-center":"",onClose:_},{default:r(()=>[o(O,{data:[m.value],border:"",class:"info-table info-table-top",style:{width:"100%","margin-bottom":"32px"},"show-header":!0,"header-cell-style":{background:"#fff",color:"#888",fontWeight:500,fontSize:"16px",textAlign:"center"},"cell-style":{background:"#fff",color:"#222",fontSize:"18px",fontWeight:500,textAlign:"center"}},{default:r(()=>[o(d,{prop:"createUserId",label:"提交人ID","show-overflow-tooltip":"","min-width":"120"},{default:r(a=>[v(S(`ID：${a.row.createUserId}`),1)]),_:1}),o(d,{prop:"name",label:"提交人姓名","min-width":"100"}),o(d,{prop:"age",label:"年龄","min-width":"80"}),o(d,{prop:"sex",label:"性别","min-width":"80"},{default:r(a=>[v(S(a.row.sex===1?"男":"女"),1)]),_:1}),o(d,{prop:"certificate",label:"证书名称","show-overflow-tooltip":"","min-width":"140"}),o(d,{prop:"phone",label:"提交人电话","min-width":"140"}),o(d,{prop:"createTime",label:"提交时间","min-width":"180"},{default:r(a=>[v(S(N(Q)(a.row.createTime)),1)]),_:1})]),_:1},8,["data"]),x("div",te,[x("div",ae,[t[5]||(t[5]=x("div",{class:"card-title"},"证书图片",-1)),o(W,{class:"cert-img",src:b.value,"preview-src-list":[b.value],fit:"contain"},null,8,["src","preview-src-list"])]),x("div",oe,[l.value==="transfer"?(f(),h(q,{key:0,modelValue:D.value,"onUpdate:modelValue":t[0]||(t[0]=a=>D.value=a),transferList:E,onSubmit:z},null,8,["modelValue"])):l.value==="record"?(f(),h(F,{key:1,auditList:u.value,statusList:s.value},null,8,["auditList","statusList"])):(f(),h(G,{key:2,modelValue:i.value,"onUpdate:modelValue":t[1]||(t[1]=a=>i.value=a),options:N(J)},null,8,["modelValue","options"]))])]),l.value==="note"?(f(),L("div",se,[o(U,{type:"primary",style:{background:"#3477f4",border:"none"},onClick:j},{default:r(()=>t[6]||(t[6]=[v("转审")])),_:1}),o(U,{type:"danger",onClick:t[2]||(t[2]=a=>$("reject"))},{default:r(()=>t[7]||(t[7]=[v("驳回")])),_:1}),o(U,{type:"success",onClick:t[3]||(t[3]=a=>w("pass"))},{default:r(()=>t[8]||(t[8]=[v("通过")])),_:1})])):V("",!0)]),_:1},8,["modelValue"])),[[P,c.value]])])}}}),ce=ee(le,[["__scopeId","data-v-acd2dc1b"]]);export{ce as default};
