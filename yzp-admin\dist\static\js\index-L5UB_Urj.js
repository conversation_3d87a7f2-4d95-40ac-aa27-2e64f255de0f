var se=Object.defineProperty;var L=Object.getOwnPropertySymbols;var ie=Object.prototype.hasOwnProperty,ue=Object.prototype.propertyIsEnumerable;var P=(u,t,a)=>t in u?se(u,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):u[t]=a,Y=(u,t)=>{for(var a in t||(t={}))ie.call(t,a)&&P(u,a,t[a]);if(L)for(var a of L(t))ue.call(t,a)&&P(u,a,t[a]);return u};var j=(u,t,a)=>new Promise((v,h)=>{var m=o=>{try{c(a.next(o))}catch(k){h(k)}},x=o=>{try{c(a.throw(o))}catch(k){h(k)}},c=o=>o.done?v(o.value):Promise.resolve(o.value).then(m,x);c((a=a.apply(u,t)).next())});import de from"./index-DhvSwAMj.js";import{b as pe}from"./index-C5VTKhL7.js";import{d as me,r,o as ce,u as ve,a as ye,c as _,b as s,e as d,w as i,f as R,g as p,F as E,h as M,i as $,j as C,k as g,l as z,t as S,_ as be}from"./index-DOMkE6w1.js";import{f as fe}from"./index-DFv13GLf.js";import"./index-BPHCkZA8.js";import"./quickReply-D6WzbhB8.js";const _e={class:"table-header-flex"},ge={class:"form-btns"},he={key:0},ke={key:1},we={key:2},Ce={style:{display:"flex","justify-content":"flex-end","margin-top":"20px"}},F=420,ze=me({__name:"index",setup(u){const t=r({}),a=r(!1),v=r(""),h=r(0),m=r(0),x=ve(),c=r({}),o=r({entity:{endTime:"",name:"",phone:"",startTime:"",status:null},orderBy:{},page:1,size:10});let k=[{type:"input",key:"username",label:"姓名"},{type:"input",key:"phone",label:"手机号"},{type:"datetime",key:"dates",label:"提交时间"}];const A=[{property:"name",label:"姓名",width:""},{property:"age",label:"年龄",width:""},{property:"sex",label:"性别",width:""},{property:"phone",label:"手机号码",width:""},{property:"createTime",label:"提交时间",width:""}],U=r([]),T=r(1),V=r(10),I=r("default"),Q=r(!1),q=r(!1),G=l=>{V.value=l,o.value.size=l,o.value.page=1,b()},J=l=>{T.value=l,o.value.page=l,b()},B=r(window.innerHeight-F);function D(){B.value=window.innerHeight-F}const b=()=>j(null,null,function*(){const l=yield pe(Y({},o.value));l.code===0&&(U.value=l.data.list,h.value=l.data.total)}),K=()=>{t.value.dates&&t.value.dates.length===2?(o.value.entity.startTime=t.value.dates[0],o.value.entity.endTime=t.value.dates[1]):(delete o.value.entity.startTime,delete o.value.entity.endTime),o.value.entity.name=t.value.name||void 0,o.value.entity.phone=t.value.phone||void 0,b()},O=()=>{t.value={},o.value.entity={endTime:"",name:"",phone:"",startTime:"",status:m.value},b()},W=l=>{c.value=l.row,a.value=!0,v.value="note"},X=l=>{c.value=l.row,a.value=!0,v.value="transfer"},Z=l=>{c.value=l.row,a.value=!0,v.value="record"},ee=()=>{v.value="",a.value=!1,b()};return ce(()=>{const l=x.meta.businessStatus;o.value.entity.status=l!==void 0?Number(l):0,m.value=l!==void 0?Number(l):0,b(),window.addEventListener("resize",D)}),ye(()=>{window.removeEventListener("resize",D)}),(l,n)=>{const te=p("el-input"),ae=p("el-date-picker"),le=p("el-form-item"),oe=p("el-form"),w=p("el-button"),H=p("el-card"),N=p("el-table-column"),ne=p("el-table"),re=p("el-pagination");return s(),_("div",null,[d(H,{shadow:"never"},{default:i(()=>[R("div",_e,[d(oe,{inline:!0,model:t.value,class:"table-header-form"},{default:i(()=>[(s(!0),_(E,null,M($(k),(e,f)=>(s(),g(le,{key:f,label:e.label,class:"form-item"},{default:i(()=>[e.type==="input"?(s(),g(te,{key:0,modelValue:t.value[e.key],"onUpdate:modelValue":y=>t.value[e.key]=y,size:"large",placeholder:"请输入"+e.label,clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="datetime"?(s(),g(ae,{key:1,modelValue:t.value[e.key],"onUpdate:modelValue":y=>t.value[e.key]=y,type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",size:"large",style:{width:"380px"},"value-format":"YYYY-MM-DD HH:mm:ss",clearable:""},null,8,["modelValue","onUpdate:modelValue"])):z("",!0)]),_:2},1032,["label"]))),128))]),_:1},8,["model"]),R("div",ge,[d(w,{size:"large",type:"primary",onClick:K},{default:i(()=>n[3]||(n[3]=[C("搜索")])),_:1}),d(w,{size:"large",type:"info",style:{"background-color":"#b4c4d1",color:"#ffffff",border:"#b4c4d1"},onClick:O},{default:i(()=>n[4]||(n[4]=[C("重置")])),_:1})])])]),_:1}),d(H,{shadow:"never",style:{"margin-top":"15px",padding:"5px 15px"}},{default:i(()=>[d(ne,{ref:"tableContainer",data:U.value,style:{width:"100%"},border:"",height:B.value},{default:i(()=>[(s(),_(E,null,M(A,(e,f)=>d(N,{key:f,width:e.width,label:e.label,property:e.property,"show-overflow-tooltip":""},{default:i(y=>[e.property==="createTime"?(s(),_("span",he,S($(fe)(y.row[e.property])),1)):e.property==="sex"?(s(),_("span",ke,S(y.row[e.property]===1?"男":"女"),1)):(s(),_("span",we,S(y.row[e.property]),1))]),_:2},1032,["width","label","property"])),64)),d(N,{fixed:"right",label:"操作","min-width":"60"},{default:i(e=>[m.value===1?(s(),g(w,{key:0,link:"",type:"primary",style:{color:"#279efb"},onClick:f=>W(e)},{default:i(()=>n[5]||(n[5]=[C(" 视检 ")])),_:2},1032,["onClick"])):z("",!0),m.value===1?(s(),g(w,{key:1,link:"",type:"primary",style:{color:"#fb2727"},onClick:f=>X(e)},{default:i(()=>n[6]||(n[6]=[C(" 转办 ")])),_:2},1032,["onClick"])):z("",!0),m.value===2||m.value===3?(s(),g(w,{key:2,link:"",type:"primary",style:{color:"#4eb906"},onClick:f=>Z(e)},{default:i(()=>n[7]||(n[7]=[C(" 操作记录 ")])),_:2},1032,["onClick"])):z("",!0)]),_:1})]),_:1},8,["data","height"]),R("div",Ce,[d(re,{"current-page":T.value,"onUpdate:currentPage":n[0]||(n[0]=e=>T.value=e),"page-size":V.value,"onUpdate:pageSize":n[1]||(n[1]=e=>V.value=e),"page-sizes":[10,20,50,100],size:I.value,disabled:q.value,background:Q.value,layout:"total, sizes, prev, pager, next, jumper",total:h.value,onSizeChange:G,onCurrentChange:J},null,8,["current-page","page-size","size","disabled","background","total"])])]),_:1}),d(de,{dialogVisible:a.value,"onUpdate:dialogVisible":n[2]||(n[2]=e=>a.value=e),isRightType:v.value,currentRow:c.value,onCancelBtn:ee},null,8,["dialogVisible","isRightType","currentRow"])])}}}),De=be(ze,[["__scopeId","data-v-688443e8"]]);export{De as default};
