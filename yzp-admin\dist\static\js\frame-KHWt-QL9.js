import{d as p,r as i,u as g,i as f,P as x,o as I,ab as R,T as k,c as P,b as S,f as B,Z as E,_ as L}from"./index-VeYmKv4z.js";const b={class:"frame","element-loading-text":"加载中..."},w=["src"],y=p({name:"LayFrame",__name:"frame",props:{frameInfo:{}},setup(v){var m,u,d;const o=v,s=i(!0),t=g(),r=i(""),l=i(null);(m=f(t.meta))!=null&&m.frameSrc&&(r.value=(u=f(t.meta))==null?void 0:u.frameSrc),((d=f(t.meta))==null?void 0:d.frameLoading)===!1&&c();function c(){s.value=!1}function h(){E(()=>{const e=f(l);if(!e)return;const a=e;a.attachEvent?a.attachEvent("onload",()=>{c()}):e.onload=()=>{c()}})}return x(()=>t.fullPath,e=>{var a,n,_;t.name==="Redirect"&&e.includes((a=o.frameInfo)==null?void 0:a.fullPath)&&(r.value=e,s.value=!0),((n=o.frameInfo)==null?void 0:n.fullPath)===e&&(r.value=(_=o.frameInfo)==null?void 0:_.frameSrc)}),I(()=>{h()}),(e,a)=>{const n=R("loading");return k((S(),P("div",b,[B("iframe",{ref_key:"frameRef",ref:l,src:r.value,class:"frame-iframe"},null,8,w)])),[[n,s.value]])}}}),T=L(y,[["__scopeId","data-v-fe57e7fd"]]);export{T as default};
