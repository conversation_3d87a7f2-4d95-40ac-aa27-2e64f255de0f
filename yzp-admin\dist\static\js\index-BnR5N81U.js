var Y=(R,l,r)=>new Promise((m,c)=>{var g=d=>{try{_(r.next(d))}catch(y){c(y)}},v=d=>{try{_(r.throw(d))}catch(y){c(y)}},_=d=>d.done?m(d.value):Promise.resolve(d.value).then(g,v);_((r=r.apply(R,l)).next())});import oe from"./index-DeG0xkZG.js";import{d as se,r as a,o as re,u as ie,a as ue,c as k,b as i,e as u,w as s,f as U,g as p,F as j,h as A,i as E,j as z,k as w,l as N,t as L,_ as de}from"./index-VeYmKv4z.js";import{f as pe}from"./dateFormat-BuOeynu9.js";import{c as ce}from"./index-BeicPvf_.js";import"./quickReply-DfweD696.js";const me={class:"table-header-flex"},ve={class:"form-btns"},ye={key:0},fe={key:1},be={style:{display:"flex","justify-content":"flex-end","margin-top":"20px"}},O=420,ge=se({__name:"index",setup(R){const l=a({}),r=a(!1),m=a(""),c=a(0),g=a(!1),v=a(!1);let _=[{type:"input",key:"createUserName",label:"招聘者"},{type:"input",key:"name",label:"公司名称"},{type:"datetime",key:"dates",label:"提交时间"}];const d=[{property:"name",label:"公司名称",width:""},{property:"address",label:"公司地址",width:""},{property:"createUserName",label:"招聘者",width:""},{property:"phone",label:"电话",width:""},{property:"createTime",label:"提交时间",width:""}],y=a([]),x=a(1),T=a(10),P=a("default"),F=a(!1),$=a(!1),B=a(0),V=a({}),I=ie(),o=a({entity:{createUserName:null,endTime:null,name:null,startTime:null,status:null},orderBy:{},page:1,size:10}),f=()=>Y(null,null,function*(){g.value=!0;try{const t=yield ce(o.value);t.code===0&&(B.value=t.data.total,y.value=t.data.list)}catch(t){}finally{g.value=!1}}),Q=t=>{T.value=t,o.value.size=t,o.value.page=1,f()},q=t=>{x.value=t,o.value.page=t,f()},S=a(window.innerHeight-O);function D(){S.value=window.innerHeight-O}function G(t){V.value=t.row,r.value=!0,v.value=!1,m.value="note"}const J=t=>{V.value=t.row,r.value=!0,v.value=!0,m.value="record"},K=()=>{m.value="",r.value=!1,v.value=!1,f()},W=()=>{l.value.dates&&l.value.dates.length===2?(o.value.entity.startTime=l.value.dates[0],o.value.entity.endTime=l.value.dates[1]):(delete o.value.entity.startTime,delete o.value.entity.endTime),o.value.entity.name=l.value.name||void 0,o.value.entity.phone=l.value.phone||void 0,f()},X=()=>{l.value={},o.value={entity:{createUserName:null,endTime:null,name:null,startTime:null,status:c.value},orderBy:{},page:1,size:10},f()};return re(()=>{const t=I.meta.businessStatus;o.value.entity.status=t!==void 0?Number(t):0,c.value=t!==void 0?Number(t):0,f(),window.addEventListener("resize",D)}),ue(()=>{window.removeEventListener("resize",D)}),(t,n)=>{const Z=p("el-input"),ee=p("el-date-picker"),te=p("el-form-item"),ae=p("el-form"),C=p("el-button"),H=p("el-card"),M=p("el-table-column"),le=p("el-table"),ne=p("el-pagination");return i(),k("div",null,[u(H,{shadow:"never"},{default:s(()=>[U("div",me,[u(ae,{inline:!0,model:l.value,class:"table-header-form"},{default:s(()=>[(i(!0),k(j,null,A(E(_),(e,h)=>(i(),w(te,{key:h,label:e.label,class:"form-item"},{default:s(()=>[e.type==="input"?(i(),w(Z,{key:0,modelValue:l.value[e.key],"onUpdate:modelValue":b=>l.value[e.key]=b,placeholder:"请输入"+e.label,clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="datetime"?(i(),w(ee,{key:1,modelValue:l.value[e.key],"onUpdate:modelValue":b=>l.value[e.key]=b,type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",style:{width:"380px"},"value-format":"YYYY-MM-DD HH:mm:ss",clearable:""},null,8,["modelValue","onUpdate:modelValue"])):N("",!0)]),_:2},1032,["label"]))),128))]),_:1},8,["model"]),U("div",ve,[u(C,{size:"large",type:"primary",onClick:W},{default:s(()=>n[3]||(n[3]=[z("搜索")])),_:1}),u(C,{size:"large",type:"info",style:{"background-color":"#b4c4d1",color:"#ffffff",border:"#b4c4d1"},onClick:X},{default:s(()=>n[4]||(n[4]=[z("重置")])),_:1})])])]),_:1}),u(H,{shadow:"never",style:{"margin-top":"15px",padding:"5px 15px"}},{default:s(()=>[u(le,{ref:"tableContainer",data:y.value,loading:g.value,style:{width:"100%"},border:"",height:S.value},{default:s(()=>[(i(),k(j,null,A(d,(e,h)=>u(M,{key:h,width:e.width,label:e.label,"show-overflow-tooltip":""},{default:s(b=>[e.property==="createTime"?(i(),k("span",ye,L(E(pe)(b.row[e.property])),1)):(i(),k("span",fe,L(b.row[e.property]),1))]),_:2},1032,["width","label"])),64)),u(M,{fixed:"right",label:"操作","min-width":"120"},{default:s(e=>[c.value===0?(i(),w(C,{key:0,link:"",type:"primary",style:{color:"#279efb"},onClick:h=>G(e)},{default:s(()=>n[5]||(n[5]=[z(" 审批 ")])),_:2},1032,["onClick"])):N("",!0),c.value===1||c.value===2?(i(),w(C,{key:1,link:"",type:"primary",style:{color:"#4eb906"},onClick:h=>J(e)},{default:s(()=>n[6]||(n[6]=[z(" 操作记录 ")])),_:2},1032,["onClick"])):N("",!0)]),_:1})]),_:1},8,["data","loading","height"]),U("div",be,[u(ne,{"current-page":x.value,"onUpdate:currentPage":n[0]||(n[0]=e=>x.value=e),"page-size":T.value,"onUpdate:pageSize":n[1]||(n[1]=e=>T.value=e),"page-sizes":[10,20,50,100],size:P.value,disabled:$.value,background:F.value,layout:"total, sizes, prev, pager, next, jumper",total:B.value,onSizeChange:Q,onCurrentChange:q},null,8,["current-page","page-size","size","disabled","background","total"])])]),_:1}),u(oe,{dialogVisible:r.value,"onUpdate:dialogVisible":n[2]||(n[2]=e=>r.value=e),isRightType:m.value,currentRow:V.value,closeOnClickModal:v.value,onCancelBtn:K},null,8,["dialogVisible","isRightType","currentRow","closeOnClickModal"])])}}}),xe=de(ge,[["__scopeId","data-v-dfffe151"]]);export{xe as default};
