<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import TransferUserSelect from "@/components/TransferUserSelect/index.vue";
import AuditStatusRecords from "@/components/AuditStatusRecords/index.vue";
import ReasonEditableCard from "@/components/ReasonEditableCard/index.vue";
import {
  getCompanyJobInfoById,
  handleCompanyJobAudit
} from "@/api/company/index";
import { ElMessage } from "element-plus";
import { formatTimestamp } from "@/utils/dateFormat";
import { noteOptions } from "@/utils/quickReply";

const loading = ref<boolean>(false);
const rightType = ref<any>("note");
const infoTableData = ref<any>({});
const avatarUrl = ref<any>("");
const transferValue = ref("lisi");
const noteText = ref("");
const positionInfo = ref<any>({});
const positionKey = ref<any>([]);

const auditList = ref<
  {
    auditUserName: string;
    auditTime: string;
    status: string;
    reason?: string;
  }[]
>([]);
const statusList = ref<{ person: string; time: string; person2: string }[]>([]);

const $emit = defineEmits([
  "cancelBtn",
  "update:dialogVisible",
  "update:updataList"
]);

const $props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  isRightType: {
    type: String,
    default: ""
  },
  currentRow: {
    type: Object,
    default: () => ({})
  }
});

const visible = computed({
  get() {
    if ($props.dialogVisible) {
      getCompanyJobInfo();
    }
    return $props.dialogVisible;
  },
  set(value) {
    $emit("update:dialogVisible", value);
  }
});

// 副作用：监听 isAudit，赋值给 showResult 和 showTransfer
watch(
  () => $props.isRightType,
  newVal => {
    console.log("🚀 ~ newVal:", newVal);
    rightType.value = newVal;
  },
  { immediate: true }
);

const getCompanyJobInfo = async () => {
  const res: any = await getCompanyJobInfoById({
    id: infoTableData.value.id
  });
  if (res.code === 0) {
    positionInfo.value = res.data;
    // 假设 positionKey 是字符串，如 "本科,朝九晚六,英语4级"
    const rawPositionKey = res.data.positionKey;
    // 如果是字符串，用 split 分割成数组
    if (typeof rawPositionKey === "string") {
      positionKey.value = rawPositionKey.split(",").map(item => item.trim());
    } else if (Array.isArray(rawPositionKey)) {
      // 如果后端直接返回数组，也支持
      positionKey.value = rawPositionKey;
    } else {
      positionKey.value = [];
    }
  } else {
    ElMessage.error(res.message);
  }
};

// 处理薪资范围
const handleSalaryRange = (salaryBegin: any, salaryEnd: any): string => {
  const isEmpty = (val: any) => val === undefined || val === null || val === "";
  const isNegotiable = (val: any) => val === "面议";
  // 两个都是"面议"
  if (isNegotiable(salaryBegin) && isNegotiable(salaryEnd)) {
    return "面议";
  }
  // 两个都是空
  if (
    (isEmpty(salaryBegin) || isNegotiable(salaryBegin)) &&
    (isEmpty(salaryEnd) || isNegotiable(salaryEnd))
  ) {
    return "";
  }
  // 转换为K并向下取整
  const toK = (value: any): number | null => {
    const num = parseFloat(value);
    return isNaN(num) ? null : Math.floor(num / 1000);
  };
  const beginK = toK(salaryBegin);
  const endK = toK(salaryEnd);
  // 单边有效处理
  if (isEmpty(salaryBegin) || isNegotiable(salaryBegin) || beginK === null) {
    return endK !== null ? `${endK} k` : "";
  }
  if (isEmpty(salaryEnd) || isNegotiable(salaryEnd) || endK === null) {
    return beginK !== null ? `${beginK} k` : "";
  }
  // 双边都有效
  return `${beginK} k-${endK} k`;
};

// 取消
function cancelBtn() {
  $emit("cancelBtn", true);
  rightType.value = "note";
}

// 审批
const handleAudit = async (params: any) => {
  const res: any = await handleCompanyJobAudit(params);
  if (res.code === 0) {
    ElMessage.success("操作成功");
    cancelBtn();
  }
};

// 通过
function handlePass(value: string) {
  const params = {
    id: infoTableData.value.id,
    manualInspectionStatus: value === "pass" ? 1 : 2
  };
  handleAudit(params);
  $emit("update:updataList", true);
}

// 驳回
function handleReject(value: string) {
  if (!noteText.value || noteText.value.trim() === "") {
    ElMessage.warning("请填写驳回原因");
    return;
  }
  const params = {
    id: infoTableData.value.id,
    manualInspectionStatus: value === "reject" ? 2 : 1,
    reason: noteText.value
  };
  handleAudit(params);
}

function handleTransfer() {
  rightType.value = "transfer";
}
function handleTransferSubmit(val) {
  // 这里处理转审逻辑
  cancelBtn();
}

// 审核记录选择
const transferList = [
  { name: "A组张三", value: "zhangsan" },
  { name: "A组李四", value: "lisi" }
];

watch(
  () => $props.currentRow,
  newVal => {
    auditList.value = [];
    statusList.value = [];
    auditList.value.push({
      auditUserName: newVal?.manualInspectionUserName || "",
      auditTime: newVal?.manualInspectionTime || "",
      status:
        newVal?.manualInspectionStatus === 1
          ? "1"
          : newVal?.manualInspectionStatus === 2
            ? "2"
            : "0",
      reason: newVal?.reason || ""
    });
    infoTableData.value = newVal;
    avatarUrl.value = newVal.headImgUrl;
  },
  { immediate: true }
);
</script>

<template>
  <div>
    <el-dialog
      v-model="visible"
      v-loading="loading"
      close-on-click-modal
      destroy-on-close
      :title="null"
      width="1100px"
      :show-close="true"
      class="custom-detail-dialog"
      align-center
      @close="cancelBtn"
    >
      <!-- 顶部信息区：两个el-table分两行，表头隐藏，内容上下分布 -->
      <el-table
        :data="[positionInfo]"
        border
        style="width: 100%; margin-bottom: 12px"
        class="info-table-normal"
      >
        <el-table-column prop="name" label="企业名称" min-width="180" />
        <el-table-column
          prop="socialCreditCode"
          label="社会信用代码"
          min-width="180"
        />
        <el-table-column
          prop="enterpriseLegalPerson"
          label="法人"
          min-width="120"
        />
      </el-table>
      <el-table
        :data="[infoTableData]"
        border
        style="width: 100%"
        class="info-table-normal"
      >
        <el-table-column prop="userId" label="提交人ID" min-width="100">
          <template #default="scope">
            {{ `ID: ${scope.row.userId}` }}
          </template>
        </el-table-column>
        <el-table-column
          prop="positionName"
          label="提交人姓名"
          min-width="120"
        />
        <el-table-column prop="phone" label="提交人电话" min-width="140" />
        <el-table-column prop="createTime" label="提交时间" min-width="180">
          <template #default="scope">
            {{ formatTimestamp(scope.row.createTime) }}
          </template>
        </el-table-column>
      </el-table>
      <!-- 下方内容区 -->
      <div class="content-row">
        <!-- 左侧-->
        <div class="job-info-card">
          <div class="job-info-header">
            <span class="job-title-row">
              <span class="job-title-label">岗位名称：</span>
              <span class="job-title-name">{{
                positionInfo?.positionName || ""
              }}</span>
            </span>
            <span class="job-salary">
              <span class="job-salary-text">
                {{
                  handleSalaryRange(
                    positionInfo?.workSalaryBegin ?? "",
                    positionInfo?.workSalaryEnd ?? ""
                  ) || "--"
                }}
              </span>
              <span v-if="positionInfo?.salaryMonths" class="job-salary-month"
                >{{ `·${positionInfo?.salaryMonths} 薪` || "" }}
              </span>
            </span>
          </div>
          <div class="job-edu-tags">
            <el-tag
              v-for="(item, idx) in positionKey"
              :key="idx"
              class="edu-tag"
              type="info"
              effect="plain"
              >{{ item }}</el-tag
            >
          </div>
          <div class="job-desc-scroll">
            {{ positionInfo?.positionDesc || "" }}
          </div>
        </div>
        <!-- 右侧内容保持不变 -->
        <div class="card-box">
          <template v-if="rightType === 'transfer'">
            <TransferUserSelect
              v-model="transferValue"
              :transferList="transferList"
              @submit="handleTransferSubmit"
            />
          </template>
          <template v-else-if="rightType === 'record'">
            <AuditStatusRecords
              :auditList="auditList"
              :statusList="statusList"
            />
          </template>
          <template v-else>
            <ReasonEditableCard v-model="noteText" :options="noteOptions" />
          </template>
        </div>
      </div>
      <!-- 按钮组 -->
      <div v-if="rightType === 'note'" class="btn-group-bottom">
        <!-- <el-button
          type="primary"
          style="background: #3477f4; border: none"
          @click="handleTransfer"
          >转审</el-button
        > -->
        <el-button type="danger" @click="() => handleReject('reject')"
          >驳回</el-button
        >
        <el-button type="success" @click="() => handlePass('pass')"
          >通过</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.custom-detail-dialog .el-dialog__body {
  background: #f5f6fa;
  padding-bottom: 0;
  min-height: 520px;
}
.info-table-top {
  background: #fff;
  border-radius: 10px;
  font-size: 16px;
}
.info-table-top .el-table__cell {
  border: none;
  background: #fff;
  padding: 18px 24px;
}
.content-row {
  display: flex;
  gap: 32px;
  justify-content: center;
  margin-bottom: 32px;
  margin-top: 20px;
}
.card-box {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 24px 24px 18px 24px;
  min-width: 340px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.card-title {
  font-size: 16px;
  color: #222;
  font-weight: 600;
  margin-bottom: 10px;
}
.cert-img {
  width: 100%;
  height: 220px;
  object-fit: contain;
  border-radius: 8px;
  background: #f7f8fa;
}
.note-select {
  margin-bottom: 12px;
}
.note-textarea {
  .el-textarea__inner {
    background: #f7f8fa;
    border-radius: 8px;
    font-size: 16px;
    color: #222;
    padding: 18px 16px;
    min-height: 180px;
    border: none;
    box-shadow: none;
    resize: none;
  }
}
.btn-group-bottom {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin: 20px 40px 0 0;
}
:deep(.el-table--fit) {
  margin-top: 10px;
  border-radius: 3px;
}
.img-card-row {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  flex-direction: column;
}
@media (min-width: 900px) {
  .img-card-row {
    flex-direction: row;
  }
}
.img-card {
  background: #fff;
  border-radius: 10px;
  padding: 16px;
  width: 220px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.img-title {
  font-size: 15px;
  color: #222;
  font-weight: 600;
  margin-bottom: 12px;
}
.img-preview {
  width: 180px;
  height: 180px;
  border-radius: 8px;
  background: #f5f6fa;
}
.img-placeholder {
  width: 180px;
  height: 180px;
  background: #e5e6eb;
  border-radius: 8px;
}
.cell-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.cell-label {
  color: #888;
  font-size: 14px;
  margin-bottom: 6px;
}
.cell-value {
  color: #222;
  font-size: 18px;
  font-weight: 600;
  word-break: break-all;
}
.info-table-normal .el-table__cell {
  text-align: center;
  font-size: 16px;
}
.info-table-normal .el-table__header th {
  background: #f7f8fa;
  color: #888;
  font-weight: 500;
  font-size: 15px;
}
.job-info-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 24px 24px 16px 24px;
  width: 100%;
  min-width: 340px;
  max-width: 520px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.job-info-header {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 12px;
}
.job-title-row {
  display: flex;
  align-items: center;
  font-size: 18px;
  margin-bottom: 8px;
}
.job-title-label {
  color: #222;
  font-size: 16px;
  font-weight: 500;
  margin-right: 8px;
}
.job-title-name {
  color: #222;
  font-size: 16px;
  font-weight: 500;
  margin-right: 16px;
}
.job-salary {
  color: #ff3b30;
  font-size: 16px;
  font-weight: 500;
  margin-left: 16px;
  display: inline-flex;
  align-items: center;
  margin-right: 3px;
}
.job-salary-text {
  margin: 0;
  padding: 0;
}
.job-salary-month {
  color: #ff3b30;
  font-size: 16px;
  font-weight: 500;
  margin-left: 3px;
}
.job-edu-tags {
  margin-bottom: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}
.edu-tag {
  font-size: 15px;
  border-radius: 6px;
  padding: 2px 18px;
  background: #f3f3f3;
  color: #222;
  border: none;
}
.job-desc-scroll {
  max-height: 220px;
  overflow-y: auto;
  font-size: 15px;
  color: #444;
  line-height: 1.7;
  width: 100%;
  margin-top: 8px;
  white-space: pre-line;
}
</style>
