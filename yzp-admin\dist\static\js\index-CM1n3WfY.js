var $=(L,N,y)=>new Promise((m,f)=>{var R=a=>{try{i(y.next(a))}catch(v){f(v)}},C=a=>{try{i(y.throw(a))}catch(v){f(v)}},i=a=>a.done?m(a.value):Promise.resolve(a.value).then(R,C);i((y=y.apply(L,N)).next())});import{f as oe,T as se,A as ne,R as le}from"./index-DFv13GLf.js";import{k as ie,l as ue}from"./index-Dgu8EOgW.js";import{n as re}from"./quickReply-D6WzbhB8.js";import{d as de,r as l,x as ce,O as E,aQ as K,c as x,b as c,S as pe,ab as me,k as I,w as p,e as s,f as n,l as G,g as T,j as k,t as g,i as H,F as fe,h as ve,_ as be}from"./index-DOMkE6w1.js";const _e={class:"content-row"},ge={class:"job-info-card"},ye={class:"job-info-header"},he={class:"job-title-row"},ke={class:"job-title-name"},Ie={class:"job-salary"},Te={key:0,class:"job-salary-month"},Ce={class:"job-edu-tags"},Se={class:"job-desc-scroll"},je={class:"card-box"},xe={key:0,class:"btn-group-bottom"},Le=de({__name:"index",props:{dialogVisible:{type:Boolean,default:!1},isRightType:{type:String,default:""},currentRow:{type:Object,default:()=>({})}},emits:["cancelBtn","update:dialogVisible","update:updataList"],setup(L,{emit:N}){const y=l(!1),m=l("note"),f=l({}),R=l(""),C=l("lisi"),i=l(""),a=l({}),v=l([]),A=l([]),B=l([]),D=N,S=L,M=ce({get(){return S.dialogVisible&&W(),S.dialogVisible},set(e){D("update:dialogVisible",e)}});E(()=>S.isRightType,e=>{m.value=e},{immediate:!0});const W=()=>$(null,null,function*(){const e=yield ie({id:f.value.id});if(e.code===0){a.value=e.data;const t=e.data.positionKey;typeof t=="string"?v.value=t.split(",").map(o=>o.trim()):Array.isArray(t)?v.value=t:v.value=[]}else K.error(e.message)}),X=(e,t)=>{const o=r=>r==null||r==="",u=r=>r==="面议";if(u(e)&&u(t))return"面议";if((o(e)||u(e))&&(o(t)||u(t)))return"";const j=r=>{const _=parseFloat(r);return isNaN(_)?null:Math.floor(_/1e3)},b=j(e),h=j(t);return o(e)||u(e)||b===null?h!==null?`${h} k`:"":o(t)||u(t)||h===null?b!==null?`${b} k`:"":`${b} k-${h} k`};function U(){D("cancelBtn",!0),m.value="note"}const F=e=>$(null,null,function*(){(yield ue(e)).code===0&&(K.success("操作成功"),U())});function Y(e){const t={id:f.value.id,manualInspectionStatus:1};F(t),D("update:updataList",!0)}function Z(e){if(!i.value||i.value.trim()===""){K.warning("请填写驳回原因");return}const t={id:f.value.id,manualInspectionStatus:2,reason:i.value};F(t)}function V(){m.value="transfer"}function ee(e){U()}const te=[{name:"A组张三",value:"zhangsan"},{name:"A组李四",value:"lisi"}];return E(()=>S.currentRow,e=>{A.value=[],B.value=[],A.value.push({auditUserName:(e==null?void 0:e.manualInspectionUserName)||"",auditTime:(e==null?void 0:e.manualInspectionTime)||"",status:(e==null?void 0:e.manualInspectionStatus)===1?"1":(e==null?void 0:e.manualInspectionStatus)===2?"2":"0",reason:(e==null?void 0:e.reason)||""}),f.value=e,R.value=e.headImgUrl},{immediate:!0}),(e,t)=>{const o=T("el-table-column"),u=T("el-table"),j=T("el-tag"),b=T("el-button"),h=T("el-dialog"),r=me("loading");return c(),x("div",null,[pe((c(),I(h,{modelValue:M.value,"onUpdate:modelValue":t[4]||(t[4]=_=>M.value=_),"close-on-click-modal":"","destroy-on-close":"",title:null,width:"1100px","show-close":!0,class:"custom-detail-dialog","align-center":"",onClose:U},{default:p(()=>{var _,J,O,P,w,z,Q,q;return[s(u,{data:[a.value],border:"",style:{width:"100%","margin-bottom":"12px"},class:"info-table-normal"},{default:p(()=>[s(o,{prop:"name",label:"企业名称","min-width":"180"}),s(o,{prop:"socialCreditCode",label:"社会信用代码","min-width":"180"}),s(o,{prop:"enterpriseLegalPerson",label:"法人","min-width":"120"})]),_:1},8,["data"]),s(u,{data:[f.value],border:"",style:{width:"100%"},class:"info-table-normal"},{default:p(()=>[s(o,{prop:"userId",label:"提交人ID","min-width":"100"},{default:p(d=>[k(g(`ID: ${d.row.userId}`),1)]),_:1}),s(o,{prop:"positionName",label:"提交人姓名","min-width":"120"}),s(o,{prop:"phone",label:"提交人电话","min-width":"140"}),s(o,{prop:"createTime",label:"提交时间","min-width":"180"},{default:p(d=>[k(g(H(oe)(d.row.createTime)),1)]),_:1})]),_:1},8,["data"]),n("div",_e,[n("div",ge,[n("div",ye,[n("span",he,[t[5]||(t[5]=n("span",{class:"job-title-label"},"岗位名称：",-1)),n("span",ke,g(((_=a.value)==null?void 0:_.positionName)||""),1)]),n("span",Ie,[n("span",null,g(X((O=(J=a.value)==null?void 0:J.workSalaryBegin)!=null?O:"",(w=(P=a.value)==null?void 0:P.workSalaryEnd)!=null?w:"")||"--"),1),(z=a.value)!=null&&z.salaryMonths?(c(),x("span",Te,g(`·${(Q=a.value)==null?void 0:Q.salaryMonths} 薪`||""),1)):G("",!0)])]),n("div",Ce,[(c(!0),x(fe,null,ve(v.value,(d,ae)=>(c(),I(j,{key:ae,class:"edu-tag",type:"info",effect:"plain"},{default:p(()=>[k(g(d),1)]),_:2},1024))),128))]),n("div",Se,g(((q=a.value)==null?void 0:q.positionDesc)||""),1)]),n("div",je,[m.value==="transfer"?(c(),I(se,{key:0,modelValue:C.value,"onUpdate:modelValue":t[0]||(t[0]=d=>C.value=d),transferList:te,onSubmit:ee},null,8,["modelValue"])):m.value==="record"?(c(),I(ne,{key:1,auditList:A.value,statusList:B.value},null,8,["auditList","statusList"])):(c(),I(le,{key:2,modelValue:i.value,"onUpdate:modelValue":t[1]||(t[1]=d=>i.value=d),options:H(re)},null,8,["modelValue","options"]))])]),m.value==="note"?(c(),x("div",xe,[s(b,{type:"primary",style:{background:"#3477f4",border:"none"},onClick:V},{default:p(()=>t[6]||(t[6]=[k("转审")])),_:1}),s(b,{type:"danger",onClick:t[2]||(t[2]=()=>Z("reject"))},{default:p(()=>t[7]||(t[7]=[k("驳回")])),_:1}),s(b,{type:"success",onClick:t[3]||(t[3]=()=>Y("pass"))},{default:p(()=>t[8]||(t[8]=[k("通过")])),_:1})])):G("",!0)]}),_:1},8,["modelValue"])),[[r,y.value]])])}}}),$e=be(Le,[["__scopeId","data-v-42f03c49"]]);export{$e as default};
