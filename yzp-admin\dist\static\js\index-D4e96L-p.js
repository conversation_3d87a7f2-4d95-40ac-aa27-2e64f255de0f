var j=(D,l,u)=>new Promise((y,m)=>{var f=c=>{try{w(u.next(c))}catch(g){m(g)}},b=c=>{try{w(u.throw(c))}catch(g){m(g)}},w=c=>c.done?y(c.value):Promise.resolve(c.value).then(f,b);w((u=u.apply(D,l)).next())});import re from"./table_details-BoAL9NUi.js";import{c as se}from"./index-D6X2KkBM.js";import{d as ie,r as a,o as de,u as ue,a as pe,c as v,b as n,e as i,w as d,f as R,g as p,F as E,h as I,i as L,j as C,k as _,l as B,t as z,_ as ce}from"./index-VeYmKv4z.js";import{f as me}from"./dateFormat-BuOeynu9.js";import"./quickReply-DfweD696.js";const ve={class:"table-header-flex"},ye={class:"form-btns"},be={key:0},ge={key:1,style:{color:"#fb5451"}},he={key:3},_e={key:4},fe={style:{display:"flex","justify-content":"flex-end","margin-top":"20px"}},O=420,we=ie({__name:"index",setup(D){const l=a({}),u=a(!1),y=a(""),m=a(0),f=a(!1),b=a(!1);let w=[{type:"input",key:"name",label:"姓名"},{type:"input",key:"phone",label:"电话号码"},{type:"datetime",key:"dates",label:"提交时间"}];const c=[{property:"headImgUrl",label:"头像",width:""},{property:"phone",label:"手机号码",width:""},{property:"name",label:"姓名",width:""},{property:"age",label:"年龄",width:""},{property:"sex",label:"性别",width:""},{property:"createTime",label:"提交时间",width:""},{property:"status",label:"审批状态",width:""}],g=a([]),T=a(1),V=a(10),P=a("default"),F=a(!1),$=a(!1),H=a(0),U=a({}),A=ue(),r=a({entity:{endTime:"",id:null,name:"",phone:"",reason:"",startTime:"",status:null},orderBy:{},page:1,size:10}),Q=t=>{V.value=t,r.value.size=t,r.value.page=1,h()},q=t=>{T.value=t,r.value.page=t,h()},M=a(window.innerHeight-O);function N(){M.value=window.innerHeight-O}function G(t){U.value=t.row,u.value=!0,b.value=!1,y.value="note"}const J=t=>{U.value=t.row,u.value=!0,b.value=!0,y.value="record"},K=()=>{y.value="",u.value=!1,b.value=!1,h()},W=()=>{l.value.dates&&l.value.dates.length===2?(r.value.entity.startTime=l.value.dates[0],r.value.entity.endTime=l.value.dates[1]):(delete r.value.entity.startTime,delete r.value.entity.endTime),r.value.entity.name=l.value.name||void 0,r.value.entity.phone=l.value.phone||void 0,h()},X=()=>{l.value={},r.value={entity:{endTime:"",id:null,name:"",phone:"",reason:"",startTime:"",status:m.value},orderBy:{},page:1,size:10},h()},h=()=>j(null,null,function*(){f.value=!0;try{const t=yield se(r.value);t.code===0&&(H.value=t.data.total,g.value=t.data.list)}catch(t){}finally{f.value=!1}});return de(()=>{const t=A.meta.businessStatus;r.value.entity.status=t!==void 0?Number(t):0,m.value=t!==void 0?Number(t):0,h(),window.addEventListener("resize",N)}),pe(()=>{window.removeEventListener("resize",N)}),(t,o)=>{const Z=p("el-input"),ee=p("el-date-picker"),te=p("el-form-item"),ae=p("el-form"),x=p("el-button"),Y=p("el-card"),S=p("el-table-column"),le=p("el-image"),oe=p("el-table"),ne=p("el-pagination");return n(),v("div",null,[i(Y,{shadow:"never"},{default:d(()=>[R("div",ve,[i(ae,{inline:!0,model:l.value,class:"table-header-form"},{default:d(()=>[(n(!0),v(E,null,I(L(w),(e,k)=>(n(),_(te,{key:k,label:e.label,class:"form-item"},{default:d(()=>[e.type==="input"?(n(),_(Z,{key:0,modelValue:l.value[e.key],"onUpdate:modelValue":s=>l.value[e.key]=s,size:"large",placeholder:"请输入"+e.label,clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="datetime"?(n(),_(ee,{key:1,modelValue:l.value[e.key],"onUpdate:modelValue":s=>l.value[e.key]=s,type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",size:"large",style:{width:"380px"},"value-format":"YYYY-MM-DD HH:mm:ss",clearable:""},null,8,["modelValue","onUpdate:modelValue"])):B("",!0)]),_:2},1032,["label"]))),128))]),_:1},8,["model"]),R("div",ye,[i(x,{size:"large",type:"primary",onClick:W},{default:d(()=>o[3]||(o[3]=[C("搜索")])),_:1}),i(x,{size:"large",type:"info",style:{"background-color":"#b4c4d1",color:"#ffffff",border:"#b4c4d1"},onClick:X},{default:d(()=>o[4]||(o[4]=[C("重置")])),_:1})])])]),_:1}),i(Y,{shadow:"never",style:{"margin-top":"15px",padding:"5px 15px"}},{default:d(()=>[i(oe,{ref:"tableContainer",data:g.value,loading:f.value,"element-loading-text":"数据加载中...","element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(255, 255, 255, 0.9)","element-loading-svg-view-box":"-10, -10, 50, 50",style:{width:"100%"},height:M.value},{default:d(()=>[i(S,{type:"selection",width:"60"}),(n(),v(E,null,I(c,(e,k)=>i(S,{key:k,width:e.width,label:e.label,"show-overflow-tooltip":""},{default:d(s=>[e.property==="createTime"?(n(),v("span",be,z(L(me)(s.row[e.property])),1)):e.property==="status"?(n(),v("span",ge,z(s.row[e.property]===0?"待审核":s.row[e.property]===1?"通过":"驳回"),1)):e.property==="headImgUrl"?(n(),_(le,{key:2,src:s.row[e.property],style:{width:"30px",height:"30px",cursor:"pointer"},"preview-src-list":[s.row[e.property]],fit:"cover","preview-teleported":""},null,8,["src","preview-src-list"])):e.property==="sex"?(n(),v("span",he,z(s.row[e.property]===1?"男":"女"),1)):(n(),v("span",_e,z(s.row[e.property]),1))]),_:2},1032,["width","label"])),64)),i(S,{fixed:"right",label:"操作","min-width":"120"},{default:d(e=>[m.value===0?(n(),_(x,{key:0,link:"",type:"primary",style:{color:"#279efb"},onClick:k=>G(e)},{default:d(()=>o[5]||(o[5]=[C(" 审批 ")])),_:2},1032,["onClick"])):B("",!0),m.value===1||m.value===2?(n(),_(x,{key:1,link:"",type:"primary",style:{color:"#4eb906"},onClick:k=>J(e)},{default:d(()=>o[6]||(o[6]=[C(" 操作记录 ")])),_:2},1032,["onClick"])):B("",!0)]),_:1})]),_:1},8,["data","loading","height"]),R("div",fe,[i(ne,{"current-page":T.value,"onUpdate:currentPage":o[0]||(o[0]=e=>T.value=e),"page-size":V.value,"onUpdate:pageSize":o[1]||(o[1]=e=>V.value=e),"page-sizes":[10,20,50,100],size:P.value,disabled:$.value,background:F.value,layout:"total, sizes, prev, pager, next, jumper",total:H.value,onSizeChange:Q,onCurrentChange:q},null,8,["current-page","page-size","size","disabled","background","total"])])]),_:1}),i(re,{dialogVisible:u.value,"onUpdate:dialogVisible":o[2]||(o[2]=e=>u.value=e),isRightType:y.value,currentRow:U.value,closeOnClickModal:b.value,onCancelBtn:K},null,8,["dialogVisible","isRightType","currentRow","closeOnClickModal"])])}}}),Ue=ce(we,[["__scopeId","data-v-7887890b"]]);export{Ue as default};
