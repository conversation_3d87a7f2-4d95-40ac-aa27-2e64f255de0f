<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import TransferUserSelect from "@/components/TransferUserSelect/index.vue";
import AuditStatusRecords from "@/components/AuditStatusRecords/index.vue";
import ReasonEditableCard from "@/components/ReasonEditableCard/index.vue";
import VuePdfEmbed from "vue-pdf-embed";
import { formatTimestamp } from "@/utils/dateFormat";
import { getAppendixById, handleAppendixAudit } from "@/api/user_section";
import { ElMessage } from "element-plus";
import { noteOptions } from "@/utils/quickReply";

const noteText = ref("");
const loading = ref<boolean>(false);
const pdfLoading = ref<boolean>(false);
const rightType = ref<any>("note");
const avatarUrl = ref("");
const infoTableData = ref<any>({});
const fileUrl = ref("");

const auditList = ref<
  {
    auditUserName: string;
    auditTime: string;
    status: string;
    reason?: string;
  }[]
>([]);
const statusList = ref<{ person: string; time: string; person2: string }[]>([]);

const $emit = defineEmits(["cancelBtn", "update:dialogVisible"]);

const $props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  isRightType: {
    type: String,
    default: "note"
  },
  currentRow: {
    type: Object,
    default: () => ({})
  }
});

const visible = computed({
  get() {
    return $props.dialogVisible;
  },
  set(value) {
    $emit("update:dialogVisible", value);
  }
});

// 副作用：监听 isRightType，赋值给 rightType
watch(
  () => $props.isRightType,
  newVal => {
    rightType.value = newVal;
  },
  { immediate: true }
);

// 获取附件简历
const getAppendix = async () => {
  try {
    const res: any = await getAppendixById({ id: $props.currentRow.id });
    if (res.code === 0) {
      const originalUrl = res.data[0].fileUrl || "";
      // 将外部PDF URL转换为代理URL
      if (originalUrl && originalUrl.includes("img-test.easyzhipin.com")) {
        fileUrl.value = originalUrl.replace(
          "https://img-test.easyzhipin.com",
          "/api/pdf"
        );
      } else {
        fileUrl.value = originalUrl;
      }
      // 设置PDF URL后开始加载状态
      if (fileUrl.value) {
        pdfLoading.value = true;
      } else {
        console.log("没有PDF URL，不显示加载状态");
      }
    }
  } catch (error) {
    console.error("获取PDF失败:", error);
    pdfLoading.value = false;
  }
};

// 附件简历审核
const handleAudit = async (params: any) => {
  const res: any = await handleAppendixAudit({ ...params });
  if (res.code === 0) {
    ElMessage.success("操作成功");
  } else {
    ElMessage.error(res.message);
  }
};

// 取消
function cancelBtn() {
  $emit("cancelBtn", true);
  rightType.value = "note";
}

async function handlePass(val: any) {
  await handleAudit({
    id: infoTableData.value.id,
    status: val === "pass" ? 1 : 2
  });
  cancelBtn();
}
async function handleReject(val: any) {
  if (!noteText.value) {
    ElMessage.warning("请输入驳回原因");
    return;
  }
  await handleAudit({
    id: infoTableData.value.id,
    status: val === "reject" ? 2 : 1,
    reason: noteText.value
  });
  cancelBtn();
}
function handleTransfer() {
  rightType.value = "transfer";
}
function handleTransferSubmit(val: any) {
  // 这里处理转审逻辑
  rightType.value = "note"; // 或者根据业务回到初始
}

// PDF加载完成处理
const handlePdfLoaded = () => {
  pdfLoading.value = false;
};

// PDF加载失败处理
const handlePdfError = (error: any) => {
  pdfLoading.value = false;
  ElMessage.error("PDF加载失败，请稍后重试");
};

// 审核记录选择
const transferList = [
  { name: "A组张三", value: "zhangsan" },
  { name: "A组李四", value: "lisi" }
];
const transferValue = ref("lisi");

// 副作用：监听 currentRow，赋值给 infoTableData 和 avatarUrl
watch(
  () => $props.currentRow,
  newVal => {
    // 重置PDF状态
    pdfLoading.value = false;
    fileUrl.value = "";

    auditList.value = [];
    statusList.value = [];
    auditList.value.push({
      auditUserName: newVal?.manualInspectionUserName || "",
      auditTime: newVal?.manualInspectionTime || "",
      status: newVal?.status === 1 ? "1" : newVal?.status === 2 ? "2" : "0",
      reason: newVal?.reason || ""
    });
    infoTableData.value = newVal;
    avatarUrl.value = newVal.headImgUrl;
    getAppendix();
  },
  { immediate: true }
);
</script>

<template>
  <div>
    <el-dialog
      v-model="visible"
      v-loading="loading"
      close-on-click-modal
      destroy-on-close
      :title="null"
      width="1100px"
      :show-close="true"
      class="custom-detail-dialog"
      align-center
      @close="cancelBtn"
    >
      <!-- 顶部信息卡片 -->
      <el-table
        :data="[infoTableData]"
        border
        class="info-table info-table-top"
        style="width: 100%; margin-bottom: 32px"
        :show-header="true"
        :header-cell-style="{
          background: '#fff',
          color: '#888',
          fontWeight: 500,
          fontSize: '16px',
          textAlign: 'center'
        }"
        :cell-style="{
          background: '#fff',
          color: '#222',
          fontSize: '18px',
          fontWeight: 500,
          textAlign: 'center'
        }"
      >
        <el-table-column
          prop="id"
          label="提交人ID"
          show-overflow-tooltip
          min-width="120"
        >
          {{ `ID：${infoTableData.id}` }}
        </el-table-column>
        <el-table-column prop="name" label="提交人姓名" min-width="100" />
        <el-table-column prop="phone" label="提交人电话" min-width="140" />
        <el-table-column prop="createTime" label="提交时间" min-width="180">
          {{ formatTimestamp(infoTableData.createTime) }}
        </el-table-column>
      </el-table>
      <!-- 下方内容区 -->
      <div class="content-row">
        <!-- 左侧附件简历 -->
        <div class="card-box attachment-box">
          <div class="card-title">附件简历</div>
          <div class="pdf-preview-wrapper">
            <!-- PDF加载动画 -->
            <div v-if="pdfLoading && fileUrl" class="pdf-loading">
              <div class="loading-spinner"></div>
              <div class="loading-text">PDF加载中...</div>
            </div>
            <!-- PDF内容 -->
            <VuePdfEmbed
              v-if="fileUrl"
              :source="fileUrl"
              class="pdf-preview"
              :style="{ display: pdfLoading ? 'none' : 'block' }"
              @loaded="handlePdfLoaded"
              @loading-failed="handlePdfError"
            />
            <!-- 无附件提示 -->
            <div v-if="!fileUrl" class="no-pdf">暂无附件</div>
          </div>
        </div>
        <div class="card-box">
          <template v-if="rightType === 'transfer'">
            <TransferUserSelect
              v-model="transferValue"
              :transferList="transferList"
              @submit="handleTransferSubmit"
            />
          </template>
          <template v-else-if="rightType === 'record'">
            <AuditStatusRecords
              :auditList="auditList"
              :statusList="statusList"
            />
          </template>
          <template v-else>
            <ReasonEditableCard v-model="noteText" :options="noteOptions" />
          </template>
        </div>
      </div>
      <!-- 按钮组 -->
      <div v-if="rightType === 'note'" class="btn-group-bottom">
        <!-- <el-button
          type="primary"
          style="background: #3477f4; border: none"
          @click="handleTransfer"
          >转审</el-button
        > -->
        <el-button type="danger" @click="handleReject('reject')"
          >驳回</el-button
        >
        <el-button type="success" @click="handlePass('pass')">通过</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.custom-detail-dialog .el-dialog__body {
  background: #f5f6fa;
  padding-bottom: 0;
  min-height: 520px;
}
.info-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 24px 32px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 32px;
}
.info-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 120px;
  margin-right: 24px;
}
.info-label {
  color: #888;
  font-size: 14px;
  margin-bottom: 6px;
}
.info-value {
  color: #222;
  font-size: 18px;
  font-weight: 500;
}
.content-row {
  display: flex;
  gap: 32px;
  justify-content: center;
  margin-bottom: 32px;
}
.card-box {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 24px 24px 18px 24px;
  min-width: 340px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.card-title {
  font-size: 16px;
  color: #222;
  font-weight: 600;
}
.cert-img {
  width: 100%;
  height: 220px;
  object-fit: contain;
  border-radius: 8px;
  background: #f7f8fa;
}
.note-select {
  margin-bottom: 12px;
}
.note-textarea {
  .el-textarea__inner {
    background: #f7f8fa;
    border-radius: 8px;
    font-size: 16px;
    color: #222;
    padding: 18px 16px;
    min-height: 180px;
    border: none;
    box-shadow: none;
    resize: none;
  }
}
.btn-group-bottom {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin: 20px 40px 0 0;
}
:deep(.el-table--fit) {
  margin-top: 10px;
  border-radius: 3px;
}
.attachment-box {
  .pdf-preview-wrapper {
    background: #f7f8fa;
    border-radius: 8px;
    width: 100%;
    height: 320px;
    overflow: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }
  .pdf-preview {
    width: 100%;
    height: 320px;
    border: none;
    background: #f7f8fa;
  }
  .no-pdf {
    color: #888;
    font-size: 16px;
    text-align: center;
    width: 100%;
  }
  .pdf-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: #f7f8fa;
  }
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e4e7ed;
    border-top: 4px solid #409eff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }
  .loading-text {
    color: #606266;
    font-size: 14px;
    font-weight: 500;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
