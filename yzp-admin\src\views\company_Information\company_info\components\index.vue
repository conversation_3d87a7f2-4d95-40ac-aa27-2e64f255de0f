<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import TransferUserSelect from "@/components/TransferUserSelect/index.vue";
import AuditStatusRecords from "@/components/AuditStatusRecords/index.vue";
import ReasonEditableCard from "@/components/ReasonEditableCard/index.vue";
import { ElMessage } from "element-plus";
import { formatTimestamp } from "@/utils/dateFormat";
import { noteOptions } from "@/utils/quickReply";
import { getCompanyInfoById, handleCompanyAudit } from "@/api/company/index";

const loading = ref<boolean>(false);
const rightType = ref<any>("note");
const infoTableData = ref<any>({});
const avatarUrl = ref<any>("");
const companyInfo = ref<any>({});

const auditList = ref<
  {
    auditUserName: string;
    auditTime: string;
    status: string;
    reason?: string;
  }[]
>([]);
const statusList = ref<{ person: string; time: string; person2: string }[]>([]);

const $emit = defineEmits([
  "cancelBtn",
  "update:dialogVisible",
  "update:updataList"
]);

const $props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  isRightType: {
    type: String,
    default: ""
  },
  currentRow: {
    type: Object,
    default: () => ({})
  },
  closeOnClickModal: {
    type: Boolean,
    default: false
  }
});

const visible = computed({
  get() {
    if ($props.dialogVisible) {
      getCompanyInfo();
    }
    return $props.dialogVisible;
  },
  set(value) {
    $emit("update:dialogVisible", value);
  }
});

// 副作用：监听 isAudit，赋值给 showResult 和 showTransfer
watch(
  () => $props.isRightType,
  newVal => {
    rightType.value = newVal;
  },
  { immediate: true }
);

// 获取公司信息
const getCompanyInfo = async () => {
  const res: any = await getCompanyInfoById({
    id: infoTableData.value.id
  });
  if (res.code === 0) {
    companyInfo.value = res.data;
  } else {
    ElMessage.error(res.message);
  }
};

// 处理企业规模
const handleFinanceStep = (financeStep: any) => {
  switch (financeStep) {
    case 1:
      return "0-20人";
    case 2:
      return "20-99人";
    case 3:
      return "100-499人";
    case 4:
      return "500-999人";
    case 5:
      return "1000-9999人";
    case 6:
      return "10000人以上";
    default:
      return "";
  }
};

// 取消
function cancelBtn() {
  $emit("cancelBtn", true);
  rightType.value = "note";
}

// 审批
const handleAudit = async (params: any) => {
  const res: any = await handleCompanyAudit(params);
  if (res.code === 0) {
    ElMessage.success("操作成功");
    cancelBtn();
  }
};

// 通过
function handlePass(value: string) {
  const params = {
    id: infoTableData.value.id,
    manualInspectionStatus: value === "pass" ? 1 : 0
  };
  handleAudit(params);
  $emit("update:updataList", true);
}

// 驳回
function handleReject(value: string) {
  if (!noteText.value || noteText.value.trim() === "") {
    ElMessage.warning("请填写驳回原因");
    return;
  }
  const params = {
    id: infoTableData.value.id,
    manualInspectionStatus: value === "reject" ? 2 : 0,
    reason: noteText.value
  };
  handleAudit(params);
}

function handleTransfer() {
  rightType.value = "transfer";
}
function handleTransferSubmit(val) {
  // 这里处理转审逻辑
  cancelBtn();
}

// 审核记录选择
const transferList = [
  { name: "A组张三", value: "zhangsan" },
  { name: "A组李四", value: "lisi" }
];
const transferValue = ref("lisi");

const noteText = ref("");

watch(
  () => $props.currentRow,
  newVal => {
    auditList.value = [];
    statusList.value = [];
    auditList.value.push({
      auditUserName: newVal?.manualInspectionUserName || "",
      auditTime: newVal?.manualInspectionTime || "",
      status:
        newVal?.manualInspectionStatus === 1
          ? "1"
          : newVal?.manualInspectionStatus === 2
            ? "2"
            : "0",
      reason: newVal?.reason || ""
    });
    infoTableData.value = newVal;
    avatarUrl.value = newVal.headImgUrl;
  },
  { immediate: true }
);
</script>

<template>
  <div>
    <el-dialog
      v-model="visible"
      v-loading="loading"
      :close-on-click-modal="closeOnClickModal"
      destroy-on-close
      :title="null"
      width="1100px"
      :show-close="true"
      class="custom-detail-dialog"
      align-center
      @close="cancelBtn"
    >
      <!-- 顶部信息区：两个el-table分两行，表头隐藏，内容上下分布 -->
      <el-table
        :data="[infoTableData]"
        border
        style="width: 100%; margin-bottom: 12px"
        class="info-table-normal"
      >
        <el-table-column
          prop="name"
          label="企业名称"
          min-width="180"
          show-overflow-tooltip
        />
        <el-table-column
          prop="socialCreditCode"
          label="社会信用代码"
          min-width="180"
        />
        <el-table-column
          prop="enterpriseLegalPerson"
          label="法人"
          min-width="120"
        />
      </el-table>
      <el-table
        :data="[infoTableData]"
        border
        style="width: 100%"
        class="info-table-normal"
      >
        <el-table-column prop="createUserId" label="提交人ID" min-width="100">
          <template #default="scope">
            {{ `ID：${scope.row.createUserId}` }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="提交人姓名" min-width="120" />
        <el-table-column prop="phone" label="提交人电话" min-width="140" />
        <el-table-column prop="createTime" label="提交时间" min-width="180">
          <template #default="scope">
            {{ formatTimestamp(scope.row.createTime) }}
          </template>
        </el-table-column>
      </el-table>
      <!-- 下方内容区 -->
      <div class="content-row">
        <!-- 左侧公司信息 -->
        <div class="company-info-card">
          <!-- 顶部：logo+公司名+规模+行业 -->
          <div class="company-info-header">
            <el-image
              v-if="companyInfo?.companyLogoUrl || ''"
              :src="companyInfo?.companyLogoUrl"
              class="company-logo"
              fit="contain"
            />
            <div class="company-info-title">
              <div class="company-name">{{ companyInfo?.name || "" }}</div>
              <div class="company-tags">
                <div style="color: #888888; font-size: 14px">
                  {{ handleFinanceStep(companyInfo?.financeStep || "") }}
                </div>
                <div style="color: #888888; font-size: 14px">
                  {{ companyInfo?.industryName || "" }}
                </div>
              </div>
            </div>
          </div>
          <!-- 简介 -->
          <div class="company-desc-scroll">
            {{ companyInfo?.profile || "" }}
          </div>
          <!-- 福利标签 -->
          <div class="company-benefits">
            <el-tag
              v-for="(item, idx) in companyInfo?.fuLi || []"
              :key="idx"
              class="benefit-tag"
              type="info"
              effect="plain"
              >{{ item }}</el-tag
            >
          </div>
          <!-- 环境图片 -->
          <div class="company-env-imgs">
            <el-image
              v-for="(img, idx) in companyInfo?.tuPian || []"
              :key="idx"
              :src="img.attachIdUrl || ''"
              class="env-img"
              fit="cover"
              :preview-src-list="
                (companyInfo?.tuPian || [])
                  .map(item => item.attachIdUrl)
                  .filter(Boolean)
              "
              :initial-index="idx"
            />
          </div>
        </div>
        <!-- 右侧内容保持不变 -->
        <div class="card-box">
          <template v-if="rightType === 'transfer'">
            <TransferUserSelect
              v-model="transferValue"
              :transferList="transferList"
              @submit="handleTransferSubmit"
            />
          </template>
          <template v-else-if="rightType === 'record'">
            <AuditStatusRecords
              :auditList="auditList"
              :statusList="statusList"
            />
          </template>
          <template v-else>
            <ReasonEditableCard v-model="noteText" :options="noteOptions" />
          </template>
        </div>
      </div>
      <!-- 按钮组 -->
      <div v-if="rightType === 'note'" class="btn-group-bottom">
        <!-- <el-button
          type="primary"
          style="background: #3477f4; border: none"
          @click="handleTransfer"
          >转审</el-button
        > -->
        <el-button type="danger" @click="() => handleReject('reject')"
          >驳回</el-button
        >
        <el-button type="success" @click="() => handlePass('pass')"
          >通过</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.custom-detail-dialog .el-dialog__body {
  background: #f5f6fa;
  padding-bottom: 0;
  min-height: 520px;
}
.info-table-top {
  background: #fff;
  border-radius: 10px;
  font-size: 16px;
}
.info-table-top .el-table__cell {
  border: none;
  background: #fff;
  padding: 18px 24px;
}
.content-row {
  display: flex;
  gap: 32px;
  justify-content: center;
  margin-bottom: 32px;
  margin-top: 20px;
}
.card-box {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 24px 24px 18px 24px;
  min-width: 340px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.card-title {
  font-size: 16px;
  color: #222;
  font-weight: 600;
  margin-bottom: 10px;
}
.cert-img {
  width: 100%;
  height: 220px;
  object-fit: contain;
  border-radius: 8px;
  background: #f7f8fa;
}
.note-select {
  margin-bottom: 12px;
}
.note-textarea {
  .el-textarea__inner {
    background: #f7f8fa;
    border-radius: 8px;
    font-size: 16px;
    color: #222;
    padding: 18px 16px;
    min-height: 180px;
    border: none;
    box-shadow: none;
    resize: none;
  }
}
.btn-group-bottom {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin: 20px 40px 0 0;
}
:deep(.el-table--fit) {
  margin-top: 10px;
  border-radius: 3px;
}
.img-card-row {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  flex-direction: column;
}
@media (min-width: 900px) {
  .img-card-row {
    flex-direction: row;
  }
}
.img-card {
  background: #fff;
  border-radius: 10px;
  padding: 16px;
  width: 220px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.img-title {
  font-size: 15px;
  color: #222;
  font-weight: 600;
  margin-bottom: 12px;
}
.img-preview {
  width: 180px;
  height: 180px;
  border-radius: 8px;
  background: #f5f6fa;
}
.img-placeholder {
  width: 180px;
  height: 180px;
  background: #e5e6eb;
  border-radius: 8px;
}
.cell-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.cell-label {
  color: #888;
  font-size: 14px;
  margin-bottom: 6px;
}
.cell-value {
  color: #222;
  font-size: 18px;
  font-weight: 600;
  word-break: break-all;
}
.info-table-normal .el-table__cell {
  text-align: center;
  font-size: 16px;
}
.info-table-normal .el-table__header th {
  background: #f7f8fa;
  color: #888;
  font-weight: 500;
  font-size: 15px;
}
.company-info-card {
  height: 400px;
  overflow: auto;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 24px 24px 16px 24px;
  width: 100%;
  min-width: 340px;
  max-width: 420px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.company-info-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
.company-logo {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  margin-right: 16px;
  background: #f5f6fa;
}
.company-info-title {
  display: flex;
  flex-direction: column;
}
.company-name {
  font-size: 18px;
  font-weight: 600;
  color: #222;
  margin-bottom: 4px;
}
.company-tags span {
  font-size: 13px;
  color: #888888 !important;
  margin-right: 12px;
}
.company-benefits {
  margin-bottom: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 28px 0;
}
.benefit-tag {
  font-size: 13px;
  border-radius: 6px;
  padding: 10px 10px;
  margin: 5px 0;
  color: #000 !important;
  background: #f3f3f3 !important;
}
.company-env-imgs {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}
.env-img {
  width: calc(30% - 6px);
  object-fit: cover;
  border-radius: 8px;
}
</style>
