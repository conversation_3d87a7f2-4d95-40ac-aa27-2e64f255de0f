var $=(C,N,h)=>new Promise((v,p)=>{var R=o=>{try{i(h.next(o))}catch(m){p(m)}},j=o=>{try{i(h.throw(o))}catch(m){p(m)}},i=o=>o.done?v(o.value):Promise.resolve(o.value).then(R,j);i((h=h.apply(C,N)).next())});import{T as oe,A as se,R as ae,n as le}from"./quickReply-DfweD696.js";import{k as ne,l as ie}from"./index-BeicPvf_.js";import{f as ue}from"./dateFormat-BuOeynu9.js";import{d as re,r as n,m as de,P as G,aQ as K,c as L,b as c,T as ce,ab as pe,k,w as f,e as a,f as l,l as H,g as I,j as T,t as g,i as W,F as me,h as fe,_ as ve}from"./index-VeYmKv4z.js";const be={class:"content-row"},_e={class:"job-info-card"},ge={class:"job-info-header"},he={class:"job-title-row"},ye={class:"job-title-name"},ke={class:"job-salary"},Ie={class:"job-salary-text"},Te={key:0,class:"job-salary-month"},Ce={class:"job-edu-tags"},je={class:"job-desc-scroll"},Se={class:"card-box"},xe={key:0,class:"btn-group-bottom"},Le=re({__name:"index",props:{dialogVisible:{type:Boolean,default:!1},isRightType:{type:String,default:""},currentRow:{type:Object,default:()=>({})},closeOnClickModal:{type:Boolean,default:!1}},emits:["cancelBtn","update:dialogVisible","update:updataList"],setup(C,{emit:N}){const h=n(!1),v=n("note"),p=n({}),R=n(""),j=n("lisi"),i=n(""),o=n({}),m=n([]),A=n([]),M=n([]),D=N,S=C,B=de({get(){return S.dialogVisible&&X(),S.dialogVisible},set(e){D("update:dialogVisible",e)}});G(()=>S.isRightType,e=>{v.value=e},{immediate:!0});const X=()=>$(null,null,function*(){const e=yield ne({id:p.value.id});if(e.code===0){o.value=e.data;const t=e.data.positionKey;typeof t=="string"&&t!==""?m.value=t.split(",").map(s=>s.trim()):Array.isArray(t)?m.value=t:m.value=[]}else K.error(e.message)}),Y=(e,t)=>{const s=r=>r==null||r==="",u=r=>r==="面议";if(u(e)&&u(t)||(e===0||e==="0")&&(t===0||t==="0"))return"面议";if((s(e)||u(e))&&(s(t)||u(t)))return"";const x=r=>{const _=parseFloat(r);return isNaN(_)?null:Math.floor(_/1e3)},b=x(e),y=x(t);return s(e)||u(e)||b===null?y!==null?`${y}k`:"":s(t)||u(t)||y===null?b!==null?`${b}k`:"":`${b}k - ${y}k`};function U(){D("cancelBtn",!0),v.value="note"}const O=e=>$(null,null,function*(){(yield ie(e)).code===0&&(K.success("操作成功"),U())});function Z(e){const t={id:p.value.id,manualInspectionStatus:1};O(t),D("update:updataList",!0)}function E(e){if(!i.value||i.value.trim()===""){K.warning("请填写驳回原因");return}const t={id:p.value.id,manualInspectionStatus:2,reason:i.value};O(t)}function V(e){U()}const ee=[{name:"A组张三",value:"zhangsan"},{name:"A组李四",value:"lisi"}];return G(()=>S.currentRow,e=>{A.value=[],M.value=[],A.value.push({auditUserName:(e==null?void 0:e.manualInspectionUserName)||"",auditTime:(e==null?void 0:e.manualInspectionTime)||"",status:(e==null?void 0:e.manualInspectionStatus)===1?"1":(e==null?void 0:e.manualInspectionStatus)===2?"2":"0",reason:(e==null?void 0:e.reason)||""}),p.value=e,R.value=e.headImgUrl},{immediate:!0}),(e,t)=>{const s=I("el-table-column"),u=I("el-table"),x=I("el-tag"),b=I("el-button"),y=I("el-dialog"),r=pe("loading");return c(),L("div",null,[ce((c(),k(y,{modelValue:B.value,"onUpdate:modelValue":t[4]||(t[4]=_=>B.value=_),"close-on-click-modal":C.closeOnClickModal,"destroy-on-close":"",title:null,width:"1100px","show-close":!0,class:"custom-detail-dialog","align-center":"",onClose:U},{default:f(()=>{var _,P,F,J,w,z,Q,q;return[a(u,{data:[o.value],border:"",style:{width:"100%","margin-bottom":"12px"},class:"info-table-normal"},{default:f(()=>[a(s,{prop:"name",label:"企业名称","min-width":"180"}),a(s,{prop:"socialCreditCode",label:"社会信用代码","min-width":"180"}),a(s,{prop:"enterpriseLegalPerson",label:"法人","min-width":"120"})]),_:1},8,["data"]),a(u,{data:[p.value],border:"",style:{width:"100%"},class:"info-table-normal"},{default:f(()=>[a(s,{prop:"userId",label:"提交人ID","min-width":"100"},{default:f(d=>[T(g(`ID: ${d.row.userId}`),1)]),_:1}),a(s,{prop:"positionName",label:"提交人姓名","min-width":"120"}),a(s,{prop:"phone",label:"提交人电话","min-width":"140"}),a(s,{prop:"createTime",label:"提交时间","min-width":"180"},{default:f(d=>[T(g(W(ue)(d.row.createTime)),1)]),_:1})]),_:1},8,["data"]),l("div",be,[l("div",_e,[l("div",ge,[l("span",he,[t[5]||(t[5]=l("span",{class:"job-title-label"},"岗位名称：",-1)),l("span",ye,g(((_=o.value)==null?void 0:_.positionName)||""),1)]),l("span",ke,[l("span",Ie,g(Y((F=(P=o.value)==null?void 0:P.workSalaryBegin)!=null?F:"",(w=(J=o.value)==null?void 0:J.workSalaryEnd)!=null?w:"")||"--"),1),(z=o.value)!=null&&z.salaryMonths?(c(),L("span",Te,g(`·${(Q=o.value)==null?void 0:Q.salaryMonths} 薪`||""),1)):H("",!0)])]),l("div",Ce,[(c(!0),L(me,null,fe(m.value,(d,te)=>(c(),k(x,{key:te,class:"edu-tag",type:"info",effect:"plain"},{default:f(()=>[T(g(d),1)]),_:2},1024))),128))]),l("div",je,g(((q=o.value)==null?void 0:q.positionDesc)||""),1)]),l("div",Se,[v.value==="transfer"?(c(),k(oe,{key:0,modelValue:j.value,"onUpdate:modelValue":t[0]||(t[0]=d=>j.value=d),transferList:ee,onSubmit:V},null,8,["modelValue"])):v.value==="record"?(c(),k(se,{key:1,auditList:A.value,statusList:M.value},null,8,["auditList","statusList"])):(c(),k(ae,{key:2,modelValue:i.value,"onUpdate:modelValue":t[1]||(t[1]=d=>i.value=d),options:W(le)},null,8,["modelValue","options"]))])]),v.value==="note"?(c(),L("div",xe,[a(b,{type:"danger",onClick:t[2]||(t[2]=()=>E("reject"))},{default:f(()=>t[6]||(t[6]=[T("驳回")])),_:1}),a(b,{type:"success",onClick:t[3]||(t[3]=()=>Z("pass"))},{default:f(()=>t[7]||(t[7]=[T("通过")])),_:1})])):H("",!0)]}),_:1},8,["modelValue","close-on-click-modal"])),[[r,h.value]])])}}}),$e=ve(Le,[["__scopeId","data-v-dfb7ecd1"]]);export{$e as default};
