var Y=(N,n,s)=>new Promise((m,p)=>{var _=i=>{try{h(s.next(i))}catch(v){p(v)}},x=i=>{try{h(s.throw(i))}catch(v){p(v)}},h=i=>i.done?m(i.value):Promise.resolve(i.value).then(_,x);h((s=s.apply(N,n)).next())});import oe from"./index-drml3hdU.js";import{d as re,r as l,o as se,u as ue,a as ie,c as w,b as u,e as d,w as r,f as U,g as c,F as j,h as A,i as E,j as C,k as g,l as T,t as L,_ as de}from"./index-DOMkE6w1.js";import{f as pe}from"./index-DFv13GLf.js";import{c as ce}from"./index-Dgu8EOgW.js";const me={class:"table-header-flex"},ve={class:"form-btns"},ye={key:0},fe={key:1},be={style:{display:"flex","justify-content":"flex-end","margin-top":"20px"}},M=420,ge=re({__name:"index",setup(N){const n=l({}),s=l(!1),m=l(""),p=l(0),_=l(!1);let x=[{type:"input",key:"name",label:"法人"},{type:"input",key:"phone",label:"招聘者"},{type:"input",key:"cityName",label:"公司名称"},{type:"datetime",key:"dates",label:"提交时间"}];const h=[{property:"name",label:"公司名称",width:""},{property:"address",label:"公司地址",width:""},{property:"createUserName",label:"招聘者",width:""},{property:"phone",label:"电话",width:""},{property:"createTime",label:"提交时间",width:""}],i=l([]),v=l(1),V=l(10),P=l("default"),$=l(!1),F=l(!1),R=l(0),z=l({}),I=ue(),o=l({entity:{createUserName:null,endTime:null,name:null,startTime:null,status:null},orderBy:{},page:1,size:10}),y=()=>Y(null,null,function*(){_.value=!0;try{const t=yield ce(o.value);t.code===0&&(R.value=t.data.total,i.value=t.data.list)}catch(t){}finally{_.value=!1}}),Q=t=>{V.value=t,o.value.size=t,o.value.page=1,y()},q=t=>{v.value=t,o.value.page=t,y()},B=l(window.innerHeight-M);function S(){B.value=window.innerHeight-M}function G(t){z.value=t.row,s.value=!0,m.value="note"}const J=t=>{z.value=t.row,s.value=!0,m.value="transfer"},K=t=>{z.value=t.row,s.value=!0,m.value="record"},O=()=>{m.value="",s.value=!1,y()},W=()=>{n.value.dates&&n.value.dates.length===2?(o.value.entity.startTime=n.value.dates[0],o.value.entity.endTime=n.value.dates[1]):(delete o.value.entity.startTime,delete o.value.entity.endTime),o.value.entity.name=n.value.name||void 0,o.value.entity.phone=n.value.phone||void 0,y()},X=()=>{n.value={},o.value={entity:{createUserName:null,endTime:null,name:null,startTime:null,status:p.value},orderBy:{},page:1,size:10},y()};return se(()=>{const t=I.meta.businessStatus;o.value.entity.status=t!==void 0?Number(t):0,p.value=t!==void 0?Number(t):0,y(),window.addEventListener("resize",S)}),ie(()=>{window.removeEventListener("resize",S)}),(t,a)=>{const Z=c("el-input"),ee=c("el-date-picker"),te=c("el-form-item"),ae=c("el-form"),k=c("el-button"),D=c("el-card"),H=c("el-table-column"),le=c("el-table"),ne=c("el-pagination");return u(),w("div",null,[d(D,{shadow:"never"},{default:r(()=>[U("div",me,[d(ae,{inline:!0,model:n.value,class:"table-header-form"},{default:r(()=>[(u(!0),w(j,null,A(E(x),(e,f)=>(u(),g(te,{key:f,label:e.label,class:"form-item"},{default:r(()=>[e.type==="input"?(u(),g(Z,{key:0,modelValue:n.value[e.key],"onUpdate:modelValue":b=>n.value[e.key]=b,placeholder:"请输入"+e.label,clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="datetime"?(u(),g(ee,{key:1,modelValue:n.value[e.key],"onUpdate:modelValue":b=>n.value[e.key]=b,type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",style:{width:"380px"},"value-format":"YYYY-MM-DD HH:mm:ss",clearable:""},null,8,["modelValue","onUpdate:modelValue"])):T("",!0)]),_:2},1032,["label"]))),128))]),_:1},8,["model"]),U("div",ve,[d(k,{size:"large",type:"primary",onClick:W},{default:r(()=>a[3]||(a[3]=[C("搜索")])),_:1}),d(k,{size:"large",type:"info",style:{"background-color":"#b4c4d1",color:"#ffffff",border:"#b4c4d1"},onClick:X},{default:r(()=>a[4]||(a[4]=[C("重置")])),_:1})])])]),_:1}),d(D,{shadow:"never",style:{"margin-top":"15px",padding:"5px 15px"}},{default:r(()=>[d(le,{ref:"tableContainer",data:i.value,loading:_.value,style:{width:"100%"},border:"",height:B.value},{default:r(()=>[(u(),w(j,null,A(h,(e,f)=>d(H,{key:f,width:e.width,label:e.label,"show-overflow-tooltip":""},{default:r(b=>[e.property==="createTime"?(u(),w("span",ye,L(E(pe)(b.row[e.property])),1)):(u(),w("span",fe,L(b.row[e.property]),1))]),_:2},1032,["width","label"])),64)),d(H,{fixed:"right",label:"操作","min-width":"120"},{default:r(e=>[p.value===0?(u(),g(k,{key:0,link:"",type:"primary",style:{color:"#279efb"},onClick:f=>G(e)},{default:r(()=>a[5]||(a[5]=[C(" 审批 ")])),_:2},1032,["onClick"])):T("",!0),p.value===0?(u(),g(k,{key:1,link:"",type:"primary",style:{color:"#fb2727"},onClick:f=>J(e)},{default:r(()=>a[6]||(a[6]=[C(" 转办 ")])),_:2},1032,["onClick"])):T("",!0),p.value===1||p.value===2?(u(),g(k,{key:2,link:"",type:"primary",style:{color:"#4eb906"},onClick:f=>K(e)},{default:r(()=>a[7]||(a[7]=[C(" 操作记录 ")])),_:2},1032,["onClick"])):T("",!0)]),_:1})]),_:1},8,["data","loading","height"]),U("div",be,[d(ne,{"current-page":v.value,"onUpdate:currentPage":a[0]||(a[0]=e=>v.value=e),"page-size":V.value,"onUpdate:pageSize":a[1]||(a[1]=e=>V.value=e),"page-sizes":[10,20,50,100],size:P.value,disabled:F.value,background:$.value,layout:"total, sizes, prev, pager, next, jumper",total:R.value,onSizeChange:Q,onCurrentChange:q},null,8,["current-page","page-size","size","disabled","background","total"])])]),_:1}),d(oe,{dialogVisible:s.value,"onUpdate:dialogVisible":a[2]||(a[2]=e=>s.value=e),isRightType:m.value,currentRow:z.value,onCancelBtn:O},null,8,["dialogVisible","isRightType","currentRow"])])}}}),ze=de(ge,[["__scopeId","data-v-2f190e00"]]);export{ze as default};
