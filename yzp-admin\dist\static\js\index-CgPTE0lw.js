var L=(U,n,r)=>new Promise((c,p)=>{var _=u=>{try{h(r.next(u))}catch(v){p(v)}},x=u=>{try{h(r.throw(u))}catch(v){p(v)}},h=u=>u.done?c(u.value):Promise.resolve(u.value).then(_,x);h((r=r.apply(U,n)).next())});import oe from"./index-CM1n3WfY.js";import{a as re}from"./index-Dgu8EOgW.js";import{d as se,r as l,o as ie,u as ue,a as de,c as w,b as i,e as d,w as o,f as N,g as m,F as I,h as Y,i as j,j as C,k as g,l as T,t as E,_ as pe}from"./index-DOMkE6w1.js";import{f as me}from"./index-DFv13GLf.js";import"./quickReply-D6WzbhB8.js";const ce={class:"table-header-flex"},ve={class:"form-btns"},ye={key:0},fe={key:1},be={style:{display:"flex","justify-content":"flex-end","margin-top":"20px"}},M=420,ge=se({__name:"index",setup(U){const n=l({}),r=l(!1),c=l(""),p=l(0),_=l(!1);let x=[{type:"input",key:"phone",label:"招聘者"},{type:"input",key:"name",label:"公司名称"},{type:"datetime",key:"dates",label:"提交时间"}];const h=[{property:"name",label:"公司名称",width:""},{property:"positionName",label:"岗位名称",width:""},{property:"createUserName",label:"招聘者",width:""},{property:"phone",label:"电话",width:""},{property:"createTime",label:"提交时间",width:""}],u=l([]),v=l(1),V=l(10),P=l("default"),$=l(!1),F=l(!1),S=l(0),z=l({}),J=ue(),s=l({entity:{name:"",positionName:"",createUserName:"",startTime:"",endTime:"",manualInspectionStatus:null},orderBy:{},page:1,size:10}),A=t=>{V.value=t,s.value.size=t,s.value.page=1,y()},Q=t=>{v.value=t,s.value.page=t,y()},R=l(window.innerHeight-M);function B(){R.value=window.innerHeight-M}function q(t){z.value=t.row,r.value=!0,c.value="note"}const G=t=>{z.value=t.row,r.value=!0,c.value="transfer"},K=t=>{z.value=t.row,r.value=!0,c.value="record"},O=()=>{c.value="",r.value=!1,y()},W=()=>{n.value.dates&&n.value.dates.length===2?(s.value.entity.startTime=n.value.dates[0],s.value.entity.endTime=n.value.dates[1]):(delete s.value.entity.startTime,delete s.value.entity.endTime),s.value.entity.name=n.value.name||void 0,y()},X=()=>{n.value={},s.value={entity:{name:"",positionName:"",createUserName:"",startTime:"",endTime:"",manualInspectionStatus:p.value},orderBy:{},page:1,size:10},y()},y=()=>L(null,null,function*(){_.value=!0;try{const t=yield re(s.value);t.code===0&&(S.value=t.data.total,u.value=t.data.list)}catch(t){}finally{_.value=!1}});return ie(()=>{const t=J.meta.businessStatus;s.value.entity.manualInspectionStatus=t!==void 0?Number(t):0,p.value=t!==void 0?Number(t):0,y(),window.addEventListener("resize",B)}),de(()=>{window.removeEventListener("resize",B)}),(t,a)=>{const Z=m("el-input"),ee=m("el-date-picker"),te=m("el-form-item"),ae=m("el-form"),k=m("el-button"),D=m("el-card"),H=m("el-table-column"),le=m("el-table"),ne=m("el-pagination");return i(),w("div",null,[d(D,{shadow:"never"},{default:o(()=>[N("div",ce,[d(ae,{inline:!0,model:n.value,class:"table-header-form"},{default:o(()=>[(i(!0),w(I,null,Y(j(x),(e,f)=>(i(),g(te,{key:f,label:e.label,class:"form-item"},{default:o(()=>[e.type==="input"?(i(),g(Z,{key:0,modelValue:n.value[e.key],"onUpdate:modelValue":b=>n.value[e.key]=b,placeholder:"请输入"+e.label,clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="datetime"?(i(),g(ee,{key:1,modelValue:n.value[e.key],"onUpdate:modelValue":b=>n.value[e.key]=b,type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",style:{width:"380px"},"value-format":"YYYY-MM-DD HH:mm:ss",clearable:""},null,8,["modelValue","onUpdate:modelValue"])):T("",!0)]),_:2},1032,["label"]))),128))]),_:1},8,["model"]),N("div",ve,[d(k,{size:"large",type:"primary",onClick:W},{default:o(()=>a[3]||(a[3]=[C("搜索")])),_:1}),d(k,{size:"large",type:"info",style:{"background-color":"#b4c4d1",color:"#ffffff",border:"#b4c4d1"},onClick:X},{default:o(()=>a[4]||(a[4]=[C("重置")])),_:1})])])]),_:1}),d(D,{shadow:"never",style:{"margin-top":"15px",padding:"5px 15px"}},{default:o(()=>[d(le,{ref:"tableContainer",data:u.value,loading:_.value,style:{width:"100%"},border:"",height:R.value},{default:o(()=>[(i(),w(I,null,Y(h,(e,f)=>d(H,{key:f,width:e.width,label:e.label,"show-overflow-tooltip":""},{default:o(b=>[e.property==="createTime"?(i(),w("span",ye,E(j(me)(b.row[e.property])),1)):(i(),w("span",fe,E(b.row[e.property]),1))]),_:2},1032,["width","label"])),64)),d(H,{fixed:"right",label:"操作","min-width":"120"},{default:o(e=>[p.value===0?(i(),g(k,{key:0,link:"",type:"primary",style:{color:"#279efb"},onClick:f=>q(e)},{default:o(()=>a[5]||(a[5]=[C(" 视检 ")])),_:2},1032,["onClick"])):T("",!0),p.value===0?(i(),g(k,{key:1,link:"",type:"primary",style:{color:"#fb2727"},onClick:f=>G(e)},{default:o(()=>a[6]||(a[6]=[C(" 转办 ")])),_:2},1032,["onClick"])):T("",!0),p.value===1||p.value===2?(i(),g(k,{key:2,link:"",type:"primary",style:{color:"#4eb906"},onClick:f=>K(e)},{default:o(()=>a[7]||(a[7]=[C(" 操作记录 ")])),_:2},1032,["onClick"])):T("",!0)]),_:1})]),_:1},8,["data","loading","height"]),N("div",be,[d(ne,{"current-page":v.value,"onUpdate:currentPage":a[0]||(a[0]=e=>v.value=e),"page-size":V.value,"onUpdate:pageSize":a[1]||(a[1]=e=>V.value=e),"page-sizes":[10,20,50,100],size:P.value,disabled:F.value,background:$.value,layout:"total, sizes, prev, pager, next, jumper",total:S.value,onSizeChange:A,onCurrentChange:Q},null,8,["current-page","page-size","size","disabled","background","total"])])]),_:1}),d(oe,{dialogVisible:r.value,"onUpdate:dialogVisible":a[2]||(a[2]=e=>r.value=e),isRightType:c.value,currentRow:z.value,onCancelBtn:O,"onUpdate:updataList":y},null,8,["dialogVisible","isRightType","currentRow"])])}}}),Te=pe(ge,[["__scopeId","data-v-3e40de4e"]]);export{Te as default};
