var k=(h,x,m)=>new Promise((l,i)=>{var p=o=>{try{d(m.next(o))}catch(f){i(f)}},v=o=>{try{d(m.throw(o))}catch(f){i(f)}},d=o=>o.done?l(o.value):Promise.resolve(o.value).then(p,v);d((m=m.apply(h,x)).next())});import{T as W,A as Q,R as q,n as F}from"./quickReply-DfweD696.js";import{f as G}from"./dateFormat-BuOeynu9.js";import{h as H}from"./index-D6X2KkBM.js";import{d as J,r as n,m as K,P as L,c as U,b as r,T as X,ab as Y,k as b,w as u,e as s,f as g,l as Z,g as _,j as y,t as B,i as N,aQ as A,_ as V}from"./index-VeYmKv4z.js";const ee={class:"content-row"},te={class:"card-box"},ae={key:1,class:"no-avatar"},oe={class:"card-box"},se={key:0,class:"btn-group-bottom"},le=J({__name:"index",props:{dialogVisible:{type:Boolean,default:!1},isRightType:{type:String,default:"note"},currentRow:{type:Object,default:()=>({})},closeOnClickModal:{type:Boolean,default:!1}},emits:["cancelBtn","update:dialogVisible"],setup(h,{emit:x}){const m=n(!1),l=n("note"),i=n(""),p=n({}),v=n(""),d=n([]),o=n([]),f=x,T=h,R=K({get(){return T.dialogVisible},set(e){f("update:dialogVisible",e)}});L(()=>T.isRightType,e=>{l.value=e},{immediate:!0});function C(){f("cancelBtn",!0),l.value="note"}const S=e=>k(null,null,function*(){const t=yield H(e);t.code===0?A.success("操作成功"):A.error(t.message)});function w(e){return k(this,null,function*(){yield S({id:p.value.id,status:3}),C()})}function $(e){return k(this,null,function*(){if(!i.value||i.value.trim()===""){A.warning("请填写驳回原因");return}yield S({id:p.value.id,status:2,reason:i.value}),C()})}function j(e){l.value="note"}const O=[{name:"A组张三",value:"zhangsan"},{name:"A组李四",value:"lisi"}],D=n("lisi");return L(()=>T.currentRow,e=>{d.value=[],o.value=[],d.value.push({auditUserName:(e==null?void 0:e.manualInspectionUserName)||"",auditTime:(e==null?void 0:e.manualInspectionTime)||"",status:(e==null?void 0:e.status)===3?"1":(e==null?void 0:e.status)===2?"2":"0",reason:(e==null?void 0:e.reason)||""}),p.value=e,v.value=e.attachmentUrl},{immediate:!0}),(e,t)=>{const c=_("el-table-column"),z=_("el-table"),E=_("el-image"),I=_("el-button"),M=_("el-dialog"),P=Y("loading");return r(),U("div",null,[X((r(),b(M,{modelValue:R.value,"onUpdate:modelValue":t[4]||(t[4]=a=>R.value=a),"close-on-click-modal":h.closeOnClickModal,"destroy-on-close":"",title:null,width:"1100px","show-close":!0,class:"custom-detail-dialog","align-center":"",onClose:C},{default:u(()=>[s(z,{data:[p.value],border:"",class:"info-table info-table-top",style:{width:"100%","margin-bottom":"32px"},"show-header":!0,"header-cell-style":{background:"#fff",color:"#888",fontWeight:500,fontSize:"16px",textAlign:"center"},"cell-style":{background:"#fff",color:"#222",fontSize:"18px",fontWeight:500,textAlign:"center"}},{default:u(()=>[s(c,{prop:"createUserId",label:"提交人ID","show-overflow-tooltip":"","min-width":"120"},{default:u(a=>[y(B(`ID：${a.row.createUserId}`),1)]),_:1}),s(c,{prop:"name",label:"提交人姓名","min-width":"100"}),s(c,{prop:"age",label:"年龄","min-width":"80"}),s(c,{prop:"sex",label:"性别","min-width":"80"},{default:u(a=>[y(B(a.row.sex===1?"男":"女"),1)]),_:1}),s(c,{prop:"certificate",label:"证书名称","show-overflow-tooltip":"","min-width":"140"}),s(c,{prop:"phone",label:"提交人电话","min-width":"140"}),s(c,{prop:"createTime",label:"提交时间","min-width":"180"},{default:u(a=>[y(B(N(G)(a.row.createTime)),1)]),_:1})]),_:1},8,["data"]),g("div",ee,[g("div",te,[t[6]||(t[6]=g("div",{class:"card-title"},"证书图片",-1)),v.value?(r(),b(E,{key:0,class:"cert-img",src:v.value,"preview-src-list":[v.value],fit:"contain"},{error:u(()=>t[5]||(t[5]=[g("div",{class:"no-avatar"},"加载失败",-1)])),_:1},8,["src","preview-src-list"])):(r(),U("div",ae,"暂无证书"))]),g("div",oe,[l.value==="transfer"?(r(),b(W,{key:0,modelValue:D.value,"onUpdate:modelValue":t[0]||(t[0]=a=>D.value=a),transferList:O,onSubmit:j},null,8,["modelValue"])):l.value==="record"?(r(),b(Q,{key:1,auditList:d.value,statusList:o.value},null,8,["auditList","statusList"])):(r(),b(q,{key:2,modelValue:i.value,"onUpdate:modelValue":t[1]||(t[1]=a=>i.value=a),options:N(F)},null,8,["modelValue","options"]))])]),l.value==="note"?(r(),U("div",se,[s(I,{type:"danger",onClick:t[2]||(t[2]=a=>$("reject"))},{default:u(()=>t[7]||(t[7]=[y("驳回")])),_:1}),s(I,{type:"success",onClick:t[3]||(t[3]=a=>w("pass"))},{default:u(()=>t[8]||(t[8]=[y("通过")])),_:1})])):Z("",!0)]),_:1},8,["modelValue","close-on-click-modal"])),[[P,m.value]])])}}}),ce=V(le,[["__scopeId","data-v-a9188873"]]);export{ce as default};
