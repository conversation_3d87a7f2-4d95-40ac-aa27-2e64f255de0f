import{o as N}from"./index-BPHCkZA8.js";import{d as R,r as u,g,c,b as d,f as t,e as a,w as i,j as v,k as E,l as j,i as _,aW as k,t as F,aQ as m,_ as z}from"./index-DOMkE6w1.js";const I={class:"test-pdf-page"},M={class:"pdf-test-container"},S={class:"pdf-preview-wrapper"},T={key:0,class:"pdf-loading"},U={class:"loading-spinner"},G={key:0,class:"loading-progress"},H={class:"progress-text"},Q={key:1,class:"pdf-error"},W={class:"error-text"},$={key:3,class:"no-pdf"},q=R({__name:"test-pdf",setup(A){const p=u(!1),r=u(""),n=u(!1),o=u(""),l=u(0),x=()=>{p.value=!0,h()},h=()=>{n.value=!1,o.value="",l.value=0,r.value=""},P=()=>{n.value=!0,o.value="",l.value=0,r.value="https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf"},w=()=>{n.value=!1,l.value=100,m.success("PDF加载完成")},C=s=>{n.value=!1,o.value=(s==null?void 0:s.message)||"PDF加载失败",m.error(o.value),console.error("PDF加载失败:",s)},D=s=>{s&&s.total>0&&(l.value=Math.round(s.loaded/s.total*100))},V=()=>{},b=s=>{o.value=(s==null?void 0:s.message)||"PDF渲染失败",m.error(o.value),console.error("PDF渲染失败:",s)};return(s,e)=>{const f=g("el-button"),L=g("el-progress"),B=g("el-dialog");return d(),c("div",I,[e[7]||(e[7]=t("h1",null,"PDF加载测试页面",-1)),a(f,{onClick:x,type:"primary"},{default:i(()=>e[2]||(e[2]=[v("测试PDF加载")])),_:1}),a(B,{modelValue:p.value,"onUpdate:modelValue":e[1]||(e[1]=y=>p.value=y),title:"附件简历测试",width:"1100px","destroy-on-close":""},{footer:i(()=>[a(f,{onClick:e[0]||(e[0]=y=>p.value=!1)},{default:i(()=>e[5]||(e[5]=[v("关闭")])),_:1}),a(f,{onClick:P,type:"primary"},{default:i(()=>e[6]||(e[6]=[v("加载测试PDF")])),_:1})]),default:i(()=>[t("div",M,[t("div",S,[n.value&&r.value?(d(),c("div",T,[t("div",U,[a(_(k),{icon:"eos-icons:loading",class:"loading-icon"})]),e[3]||(e[3]=t("div",{class:"loading-text"},"PDF正在加载中...",-1)),l.value>0?(d(),c("div",G,[a(L,{percentage:l.value,"show-text":!1,"stroke-width":4,color:"#409eff"},null,8,["percentage"]),t("span",H,F(l.value)+"%",1)])):j("",!0)])):o.value?(d(),c("div",Q,[a(_(k),{icon:"material-symbols:warning",class:"error-icon"}),t("div",W,F(o.value),1),a(f,{type:"primary",size:"small",style:{"margin-top":"12px"},onClick:P},{default:i(()=>e[4]||(e[4]=[v(" 重新加载 ")])),_:1})])):r.value&&!n.value?(d(),E(_(N),{key:2,source:{url:r.value,httpHeaders:{"Cache-Control":"max-age=3600"},withCredentials:!1},page:1,"text-layer":"","annotation-layer":"",class:"pdf-preview",onLoaded:w,onLoadingFailed:C,onProgress:D,onRendered:V,onRenderingFailed:b},null,8,["source"])):(d(),c("div",$,"点击按钮加载测试PDF"))])])]),_:1},8,["modelValue"])])}}}),O=z(q,[["__scopeId","data-v-6600947c"]]);export{O as default};
